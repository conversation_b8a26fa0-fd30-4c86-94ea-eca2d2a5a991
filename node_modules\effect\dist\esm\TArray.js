/**
 * @since 2.0.0
 */
import * as internal from "./internal/stm/tArray.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const TArrayTypeId = internal.TArrayTypeId;
/**
 * Finds the result of applying a partial function to the first value in its
 * domain.
 *
 * @since 2.0.0
 * @category elements
 */
export const collectFirst = internal.collectFirst;
/**
 * Finds the result of applying an transactional partial function to the first
 * value in its domain.
 *
 * @since 2.0.0
 * @category elements
 */
export const collectFirstSTM = internal.collectFirstSTM;
/**
 * Determine if the array contains a specified value.
 *
 * @macro trace
 * @since 2.0.0
 * @category elements
 */
export const contains = internal.contains;
/**
 * Count the values in the array matching a predicate.
 *
 * @macro trace
 * @since 2.0.0
 * @category folding
 */
export const count = internal.count;
/**
 * Count the values in the array matching a transactional predicate.
 *
 * @macro trace
 * @since 2.0.0
 * @category folding
 */
export const countSTM = internal.countSTM;
/**
 * Makes an empty `TArray`.
 *
 * @since 2.0.0
 * @category constructors
 */
export const empty = internal.empty;
/**
 * Atomically evaluate the conjunction of a predicate across the members of
 * the array.
 *
 * @since 2.0.0
 * @category elements
 */
export const every = internal.every;
/**
 * Atomically evaluate the conjunction of a transactional predicate across the
 * members of the array.
 *
 * @since 2.0.0
 * @category elements
 */
export const everySTM = internal.everySTM;
/**
 * Find the first element in the array matching the specified predicate.
 *
 * @since 2.0.0
 * @category elements
 */
export const findFirst = internal.findFirst;
/**
 * Get the first index of a specific value in the array.
 *
 * @since 2.0.0
 * @category elements
 */
export const findFirstIndex = internal.findFirstIndex;
/**
 * Get the first index of a specific value in the array starting from the
 * specified index.
 *
 * @since 2.0.0
 * @category elements
 */
export const findFirstIndexFrom = internal.findFirstIndexFrom;
/**
 * Get the index of the first entry in the array matching a predicate.
 *
 * @since 2.0.0
 * @category elements
 */
export const findFirstIndexWhere = internal.findFirstIndexWhere;
/**
 * Get the index of the first entry in the array starting from the specified
 * index, matching a predicate.
 *
 * @since 2.0.0
 * @category elements
 */
export const findFirstIndexWhereFrom = internal.findFirstIndexWhereFrom;
/**
 * Get the index of the next entry that matches a transactional predicate.
 *
 * @since 2.0.0
 * @category elements
 */
export const findFirstIndexWhereSTM = internal.findFirstIndexWhereSTM;
/**
 * Starting at specified index, get the index of the next entry that matches a
 * transactional predicate.
 *
 * @since 2.0.0
 * @category elements
 */
export const findFirstIndexWhereFromSTM = internal.findFirstIndexWhereFromSTM;
/**
 * Find the first element in the array matching a transactional predicate.
 *
 * @since 2.0.0
 * @category elements
 */
export const findFirstSTM = internal.findFirstSTM;
/**
 * Find the last element in the array matching a predicate.
 *
 * @since 2.0.0
 * @category elements
 */
export const findLast = internal.findLast;
/**
 * Get the last index of a specific value in the array bounded above by a
 * specific index.
 *
 * @since 2.0.0
 * @category elements
 */
export const findLastIndex = internal.findLastIndex;
/**
 * Get the last index of a specific value in the array bounded above by a
 * specific index.
 *
 * @since 2.0.0
 * @category elements
 */
export const findLastIndexFrom = internal.findLastIndexFrom;
/**
 * Find the last element in the array matching a transactional predicate.
 *
 * @since 2.0.0
 * @category elements
 */
export const findLastSTM = internal.findLastSTM;
/**
 * Atomically performs transactional effect for each item in array.
 *
 * @since 2.0.0
 * @category elements
 */
export const forEach = internal.forEach;
/**
 * Creates a new `TArray` from an iterable collection of values.
 *
 * @since 2.0.0
 * @category constructors
 */
export const fromIterable = internal.fromIterable;
/**
 * Extracts value from ref in array.
 *
 * @since 2.0.0
 * @category elements
 */
export const get = internal.get;
/**
 * The first entry of the array, if it exists.
 *
 * @since 2.0.0
 * @category elements
 */
export const headOption = internal.headOption;
/**
 * The last entry in the array, if it exists.
 *
 * @since 2.0.0
 * @category elements
 */
export const lastOption = internal.lastOption;
/**
 * Makes a new `TArray` that is initialized with specified values.
 *
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
/**
 * Atomically compute the greatest element in the array, if it exists.
 *
 * @since 2.0.0
 * @category elements
 */
export const maxOption = internal.maxOption;
/**
 * Atomically compute the least element in the array, if it exists.
 *
 * @since 2.0.0
 * @category elements
 */
export const minOption = internal.minOption;
/**
 * Atomically folds using a pure function.
 *
 * @since 2.0.0
 * @category folding
 */
export const reduce = internal.reduce;
/**
 * Atomically reduce the array, if non-empty, by a binary operator.
 *
 * @since 2.0.0
 * @category elements
 */
export const reduceOption = internal.reduceOption;
/**
 * Atomically reduce the non-empty array using a transactional binary
 * operator.
 *
 * @since 2.0.0
 * @category elements
 */
export const reduceOptionSTM = internal.reduceOptionSTM;
/**
 * Atomically folds using a transactional function.
 *
 * @macro trace
 * @since 2.0.0
 * @category folding
 */
export const reduceSTM = internal.reduceSTM;
/**
 * Returns the size of the `TArray`.
 *
 * @since 2.0.0
 * @category getters
 */
export const size = internal.size;
/**
 * Determine if the array contains a value satisfying a predicate.
 *
 * @since 2.0.0
 * @category elements
 */
export const some = internal.some;
/**
 * Determine if the array contains a value satisfying a transactional
 * predicate.
 *
 * @since 2.0.0
 * @category elements
 */
export const someSTM = internal.someSTM;
/**
 * Collects all elements into a chunk.
 *
 * @since 2.0.0
 * @since 2.0.0
 * @category destructors
 */
export const toArray = internal.toArray;
/**
 * Atomically updates all elements using a pure function.
 *
 * @since 2.0.0
 * @category elements
 */
export const transform = internal.transform;
/**
 * Atomically updates all elements using a transactional effect.
 *
 * @since 2.0.0
 * @category elements
 */
export const transformSTM = internal.transformSTM;
/**
 * Updates element in the array with given function.
 *
 * @since 2.0.0
 * @category elements
 */
export const update = internal.update;
/**
 * Atomically updates element in the array with given transactional effect.
 *
 * @since 2.0.0
 * @category elements
 */
export const updateSTM = internal.updateSTM;
//# sourceMappingURL=TArray.js.map