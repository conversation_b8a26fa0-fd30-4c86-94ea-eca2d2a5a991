{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/ (unsupported edge import crypto)"], "sourcesContent": ["__turbopack_context__.n(__import_unsupported(`crypto`));\n"], "names": [], "mappings": "AAAA,sBAAsB,CAAC,CAAC,qBAAqB,CAAC,MAAM,CAAC"}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/ (unsupported edge import stream)"], "sourcesContent": ["__turbopack_context__.n(__import_unsupported(`stream`));\n"], "names": [], "mappings": "AAAA,sBAAsB,CAAC,CAAC,qBAAqB,CAAC,MAAM,CAAC"}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n}\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload\n  } catch {\n    return null\n  }\n}\n\nexport function getTokenFromRequest(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization')\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7)\n  }\n  \n  // Also check for token in cookies\n  const token = request.cookies.get('auth-token')?.value\n  return token || null\n}\n\nexport function getUserFromRequest(request: NextRequest): JWTPayload | null {\n  const token = getTokenFromRequest(request)\n  console.log('Auth check:', {\n    hasToken: !!token,\n    tokenLength: token?.length,\n    cookies: request.cookies.getAll().map(c => ({ name: c.name, hasValue: !!c.value }))\n  })\n\n  if (!token) return null\n\n  const user = verifyToken(token)\n  console.log('Token verification:', { isValid: !!user, userId: user?.userId })\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAOtC,eAAe,aAAa,QAAgB;IACjD,OAAO,yIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,yIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAmB;IAC/C,OAAO,6IAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,6IAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,oBAAoB,OAAoB;IACtD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,kCAAkC;IAClC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,SAAS,mBAAmB,OAAoB;IACrD,MAAM,QAAQ,oBAAoB;IAClC,QAAQ,GAAG,CAAC,eAAe;QACzB,UAAU,CAAC,CAAC;QACZ,aAAa,OAAO;QACpB,SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,MAAM,EAAE,IAAI;gBAAE,UAAU,CAAC,CAAC,EAAE,KAAK;YAAC,CAAC;IACnF;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,OAAO,YAAY;IACzB,QAAQ,GAAG,CAAC,uBAAuB;QAAE,SAAS,CAAC,CAAC;QAAM,QAAQ,MAAM;IAAO;IAE3E,OAAO;AACT"}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getUserFromRequest } from '@/lib/auth'\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // Skip middleware for static files and Next.js internals\n  if (\n    pathname.startsWith('/_next/') ||\n    pathname.startsWith('/api/_next/') ||\n    pathname.includes('favicon.ico') ||\n    pathname.includes('.') && !pathname.includes('/api/')\n  ) {\n    return NextResponse.next()\n  }\n\n  // Only protect API routes (except auth routes)\n  if (pathname.startsWith('/api/')) {\n    const publicApiRoutes = ['/api/auth/', '/api/test-auth']\n    const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route))\n\n    if (!isPublicApiRoute) {\n      const user = getUserFromRequest(request)\n      if (!user) {\n        return NextResponse.json(\n          { error: 'Unauthorized' },\n          { status: 401 }\n        )\n      }\n    }\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|public/).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,yDAAyD;IACzD,IACE,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,QAAQ,CAAC,kBAClB,SAAS,QAAQ,CAAC,QAAQ,CAAC,SAAS,QAAQ,CAAC,UAC7C;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,+CAA+C;IAC/C,IAAI,SAAS,UAAU,CAAC,UAAU;QAChC,MAAM,kBAAkB;YAAC;YAAc;SAAiB;QACxD,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC;QAE3E,IAAI,CAAC,kBAAkB;YACrB,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,IAAI,CAAC,MAAM;gBACT,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAe,GACxB;oBAAE,QAAQ;gBAAI;YAElB;QACF;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}