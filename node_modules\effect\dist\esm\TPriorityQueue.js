import * as internal from "./internal/stm/tPriorityQueue.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const TPriorityQueueTypeId = internal.TPriorityQueueTypeId;
/**
 * Constructs a new empty `TPriorityQueue` with the specified `Order`.
 *
 * @since 2.0.0
 * @category constructors
 */
export const empty = internal.empty;
/**
 * Creates a new `TPriorityQueue` from an iterable collection of values.
 *
 * @since 2.0.0
 * @category constructors
 */
export const fromIterable = internal.fromIterable;
/**
 * Checks whether the queue is empty.
 *
 * @since 2.0.0
 * @category getters
 */
export const isEmpty = internal.isEmpty;
/**
 * Checks whether the queue is not empty.
 *
 * @since 2.0.0
 * @category getters
 */
export const isNonEmpty = internal.isNonEmpty;
/**
 * Makes a new `TPriorityQueue` that is initialized with specified values.
 *
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
/**
 * Offers the specified value to the queue.
 *
 * @since 2.0.0
 * @category mutations
 */
export const offer = internal.offer;
/**
 * Offers all of the elements in the specified collection to the queue.
 *
 * @since 2.0.0
 * @category mutations
 */
export const offerAll = internal.offerAll;
/**
 * Peeks at the first value in the queue without removing it, retrying until a
 * value is in the queue.
 *
 * @since 2.0.0
 * @category getters
 */
export const peek = internal.peek;
/**
 * Peeks at the first value in the queue without removing it, returning `None`
 * if there is not a value in the queue.
 *
 * @since 2.0.0
 * @category getters
 */
export const peekOption = internal.peekOption;
/**
 * Removes all elements from the queue matching the specified predicate.
 *
 * @since 2.0.0
 * @category getters
 */
export const removeIf = internal.removeIf;
/**
 * Retains only elements from the queue matching the specified predicate.
 *
 * @since 2.0.0
 * @category getters
 */
export const retainIf = internal.retainIf;
/**
 * Returns the size of the queue.
 *
 * @since 2.0.0
 * @category getters
 */
export const size = internal.size;
/**
 * Takes a value from the queue, retrying until a value is in the queue.
 *
 * @since 2.0.0
 * @category mutations
 */
export const take = internal.take;
/**
 * Takes all values from the queue.
 *
 * @since 2.0.0
 * @category mutations
 */
export const takeAll = internal.takeAll;
/**
 * Takes a value from the queue, returning `None` if there is not a value in
 * the queue.
 *
 * @since 2.0.0
 * @category mutations
 */
export const takeOption = internal.takeOption;
/**
 * Takes up to the specified maximum number of elements from the queue.
 *
 * @since 2.0.0
 * @category mutations
 */
export const takeUpTo = internal.takeUpTo;
/**
 * Collects all values into a `Chunk`.
 *
 * @since 2.0.0
 * @category destructors
 */
export const toChunk = internal.toChunk;
/**
 * Collects all values into an array.
 *
 * @since 2.0.0
 * @category destructors
 */
export const toArray = internal.toArray;
//# sourceMappingURL=TPriorityQueue.js.map