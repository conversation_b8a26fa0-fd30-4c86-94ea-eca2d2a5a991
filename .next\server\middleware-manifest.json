{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0e912bf5._.js", "server/edge/chunks/[root-of-the-server]__912732ae._.js", "server/edge/chunks/edge-wrapper_455f47ee.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "j/hHicgoNX9HWG5VRphNHs9wDCx/Z/kndf6fG17Knw4=", "__NEXT_PREVIEW_MODE_ID": "bfb767954546f4298310a172f799a39e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ba3414cd3afacf7792dd0163c0e412d9b772ca3de59cfc27d512f332b2115308", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c7e1509066fb5aa3546bf75caf6371a7f6d729da69cc06a44ed93ae83cc79905"}}}, "sortedMiddleware": ["/"], "functions": {}}