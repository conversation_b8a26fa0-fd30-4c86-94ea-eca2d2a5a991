{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Users, BarChart3, Calendar, Upload } from 'lucide-react'\n\nexport default function Home() {\n  const router = useRouter()\n\n  useEffect(() => {\n    // Check if user is already authenticated\n    const checkAuth = async () => {\n      try {\n        const response = await fetch('/api/dashboard/stats')\n        if (response.ok) {\n          // User is authenticated, redirect to dashboard\n          router.push('/dashboard')\n        }\n      } catch (error) {\n        // User is not authenticated, stay on landing page\n      }\n    }\n\n    checkAuth()\n  }, [router])\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4 py-16\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h1 className=\"text-5xl font-bold text-gray-900 mb-6\">\n            Lead Tracker\n          </h1>\n          <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n            Manage your leads efficiently with our comprehensive CRM solution.\n            Track contacts, manage follow-ups, and convert more prospects into customers.\n          </p>\n          <div className=\"flex gap-4 justify-center\">\n            <Link href=\"/auth/signup\">\n              <Button size=\"lg\" className=\"px-8\">\n                Get Started\n              </Button>\n            </Link>\n            <Link href=\"/auth/signin\">\n              <Button variant=\"outline\" size=\"lg\" className=\"px-8\">\n                Sign In\n              </Button>\n            </Link>\n          </div>\n        </div>\n\n        {/* Features */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\">\n          <Card>\n            <CardHeader className=\"text-center\">\n              <Users className=\"h-12 w-12 mx-auto mb-4 text-blue-600\" />\n              <CardTitle>Lead Management</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Organize and track all your leads in one place with detailed contact information and status tracking.\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"text-center\">\n              <Calendar className=\"h-12 w-12 mx-auto mb-4 text-green-600\" />\n              <CardTitle>Follow-ups</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Never miss an opportunity with automated follow-up reminders and scheduling.\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"text-center\">\n              <BarChart3 className=\"h-12 w-12 mx-auto mb-4 text-purple-600\" />\n              <CardTitle>Analytics</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Track your conversion rates and performance with detailed analytics and reporting.\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"text-center\">\n              <Upload className=\"h-12 w-12 mx-auto mb-4 text-orange-600\" />\n              <CardTitle>Bulk Import</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Import leads in bulk from CSV or Excel files to get started quickly.\n              </CardDescription>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* CTA */}\n        <div className=\"text-center\">\n          <Card className=\"max-w-2xl mx-auto\">\n            <CardHeader>\n              <CardTitle className=\"text-2xl\">Ready to get started?</CardTitle>\n              <CardDescription>\n                Join thousands of businesses using Lead Tracker to manage their sales pipeline.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Link href=\"/auth/signup\">\n                <Button size=\"lg\" className=\"w-full\">\n                  Create Your Free Account\n                </Button>\n              </Link>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,yCAAyC;YACzC,MAAM;4CAAY;oBAChB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,SAAS,EAAE,EAAE;4BACf,+CAA+C;4BAC/C,OAAO,IAAI,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;oBACd,kDAAkD;oBACpD;gBACF;;YAEA;QACF;yBAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAO;;;;;;;;;;;8CAIrC,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAO;;;;;;;;;;;;;;;;;;;;;;;8BAQ3D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAc;;;;;;;;;;;;;;;;;sCAM7C,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAc;;;;;;;;;;;;;;;;;sCAM7C,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAc;;;;;;;;;;;;;;;;;sCAM7C,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;8BAQ/C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;kDAChC,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAtHwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}