{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/providers/query-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools'\nimport { useState } from 'react'\n\nexport default function QueryProvider({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            staleTime: 60 * 1000, // 1 minute\n            gcTime: 10 * 60 * 1000, // 10 minutes\n            retry: (failureCount, error: any) => {\n              // Don't retry on 401/403 errors\n              if (error?.status === 401 || error?.status === 403) {\n                return false\n              }\n              return failureCount < 3\n            },\n          },\n        },\n      })\n  )\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      <ReactQueryDevtools initialIsOpen={false} />\n    </QueryClientProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAMe,SAAS,cAAc,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IACpC,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;kCAC3B,IACE,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACd,gBAAgB;oBACd,SAAS;wBACP,WAAW,KAAK;wBAChB,QAAQ,KAAK,KAAK;wBAClB,KAAK;sDAAE,CAAC,cAAc;gCACpB,gCAAgC;gCAChC,IAAI,CAAA,kBAAA,4BAAA,MAAO,MAAM,MAAK,OAAO,CAAA,kBAAA,4BAAA,MAAO,MAAM,MAAK,KAAK;oCAClD,OAAO;gCACT;gCACA,OAAO,eAAe;4BACxB;;oBACF;gBACF;YACF;;IAGJ,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;0BACD,6LAAC,uLAAA,CAAA,qBAAkB;gBAAC,eAAe;;;;;;;;;;;;AAGzC;GA1BwB;KAAA", "debugId": null}}]}