import { NextRequest, NextResponse } from 'next/server'
import { getUserFromRequest } from '@/lib/auth'

export async function GET(request: NextRequest) {
  const user = getUserFromRequest(request)
  
  return NextResponse.json({
    authenticated: !!user,
    user: user || null,
    cookies: request.cookies.getAll(),
    headers: {
      authorization: request.headers.get('authorization'),
      cookie: request.headers.get('cookie'),
    }
  })
}
