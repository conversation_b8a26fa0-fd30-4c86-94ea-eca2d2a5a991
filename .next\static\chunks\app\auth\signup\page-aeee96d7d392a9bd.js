(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[778],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var a=t(5155),s=t(2115),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((e,r)=>{let{className:t,variant:s,size:i,asChild:l=!1,...o}=e;return(0,a.jsx)("button",{className:(0,n.cn)(d({variant:s,size:i,className:t})),ref:r,...o})});l.displayName="Button"},343:(e,r,t)=>{"use strict";t.d(r,{EA:()=>l,Sd:()=>s,UY:()=>d,aE:()=>i,aq:()=>n});var a=t(8309);let s=a.Ik({name:a.Yj().min(2,"Name must be at least 2 characters"),email:a.Yj().email("Invalid email address"),password:a.Yj().min(6,"Password must be at least 6 characters")}),i=a.Ik({email:a.Yj().email("Invalid email address"),password:a.Yj().min(1,"Password is required")}),n=a.Ik({name:a.Yj().min(1,"Name is required"),email:a.Yj().email("Invalid email address"),phone:a.Yj().optional(),source:a.Yj().optional(),status:a.k5(["NEW","CONTACTED","INTERESTED","CONVERTED","LOST"]).default("NEW")});n.partial();let d=a.Ik({content:a.Yj().min(1,"Content is required"),leadId:a.Yj().min(1,"Lead ID is required")}),l=a.Ik({title:a.Yj().min(1,"Title is required"),description:a.Yj().optional(),dueDate:a.Yj().datetime("Invalid date format"),leadId:a.Yj().min(1,"Lead ID is required")});l.partial().extend({completed:a.zM().optional()}),a.Ik({leads:a.YO(n.omit({status:!0}))})},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var a=t(5155),s=t(2115),i=t(9434);let n=s.forwardRef((e,r)=>{let{className:t,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...n})});n.displayName="Input"},2542:(e,r,t)=>{Promise.resolve().then(t.bind(t,4820))},4820:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var a=t(5155),s=t(2115),i=t(5695),n=t(6874),d=t.n(n),l=t(2177),o=t(221),c=t(343),m=t(285),u=t(2523),f=t(6695),p=t(7759);function x(){let[e,r]=(0,s.useState)(!1),[t,n]=(0,s.useState)(""),x=(0,i.useRouter)(),h=(0,l.mN)({resolver:(0,o.u)(c.Sd),defaultValues:{name:"",email:"",password:""}}),g=async e=>{r(!0),n("");try{let r=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),t=await r.json();if(!r.ok)throw Error(t.error||"Something went wrong");x.push("/dashboard"),x.refresh()}catch(e){n(e instanceof Error?e.message:"Something went wrong")}finally{r(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)(f.Zp,{className:"w-full max-w-md",children:[(0,a.jsxs)(f.aR,{className:"space-y-1",children:[(0,a.jsx)(f.ZB,{className:"text-2xl text-center",children:"Create account"}),(0,a.jsx)(f.BT,{className:"text-center",children:"Enter your information to create your account"})]}),(0,a.jsx)(f.Wu,{children:(0,a.jsxs)(p.lV,{onSubmit:h.handleSubmit(g),children:[t&&(0,a.jsx)("div",{className:"bg-destructive/15 text-destructive text-sm p-3 rounded-md",children:t}),(0,a.jsxs)(p.zB,{children:[(0,a.jsx)(p.lR,{htmlFor:"name",children:"Name"}),(0,a.jsx)(u.p,{id:"name",type:"text",placeholder:"Enter your name",...h.register("name")}),h.formState.errors.name&&(0,a.jsx)(p.C5,{children:h.formState.errors.name.message})]}),(0,a.jsxs)(p.zB,{children:[(0,a.jsx)(p.lR,{htmlFor:"email",children:"Email"}),(0,a.jsx)(u.p,{id:"email",type:"email",placeholder:"Enter your email",...h.register("email")}),h.formState.errors.email&&(0,a.jsx)(p.C5,{children:h.formState.errors.email.message})]}),(0,a.jsxs)(p.zB,{children:[(0,a.jsx)(p.lR,{htmlFor:"password",children:"Password"}),(0,a.jsx)(u.p,{id:"password",type:"password",placeholder:"Enter your password",...h.register("password")}),h.formState.errors.password&&(0,a.jsx)(p.C5,{children:h.formState.errors.password.message})]}),(0,a.jsx)(m.$,{type:"submit",className:"w-full",disabled:e,children:e?"Creating account...":"Create account"}),(0,a.jsxs)("div",{className:"text-center text-sm",children:["Already have an account?"," ",(0,a.jsx)(d(),{href:"/auth/signin",className:"text-primary hover:underline",children:"Sign in"})]})]})})]})})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>d});var a=t(5155),s=t(2115),i=t(9434);let n=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});n.displayName="Card";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...s})});d.displayName="CardHeader";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});l.displayName="CardTitle";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",t),...s})});o.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",t),...s})});c.displayName="CardContent",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",t),...s})}).displayName="CardFooter"},7759:(e,r,t)=>{"use strict";t.d(r,{C5:()=>o,lR:()=>l,lV:()=>n,zB:()=>d});var a=t(5155),s=t(2115),i=t(9434);let n=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("form",{ref:r,className:(0,i.cn)("space-y-6",t),...s})});n.displayName="Form";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,i.cn)("space-y-2",t),...s})});d.displayName="FormField";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("label",{ref:r,className:(0,i.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",t),...s})});l.displayName="FormLabel";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,i.cn)("text-sm font-medium text-destructive",t),...s})});o.displayName="FormMessage"},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var a=t(2596),s=t(9688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}}},e=>{e.O(0,[3,889,244,441,964,358],()=>e(e.s=2542)),_N_E=e.O()}]);