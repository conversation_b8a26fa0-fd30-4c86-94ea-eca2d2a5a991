(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[236],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(5155),l=r(2115),s=r(2085),i=r(9434);let n=(0,s.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=l.forwardRef((e,t)=>{let{className:r,variant:l,size:s,asChild:d=!1,...o}=e;return(0,a.jsx)("button",{className:(0,i.cn)(n({variant:l,size:s,className:r})),ref:t,...o})});d.displayName="Button"},343:(e,t,r)=>{"use strict";r.d(t,{EA:()=>d,Sd:()=>l,UY:()=>n,aE:()=>s,aq:()=>i});var a=r(8309);let l=a.Ik({name:a.Yj().min(2,"Name must be at least 2 characters"),email:a.Yj().email("Invalid email address"),password:a.Yj().min(6,"Password must be at least 6 characters")}),s=a.Ik({email:a.Yj().email("Invalid email address"),password:a.Yj().min(1,"Password is required")}),i=a.Ik({name:a.Yj().min(1,"Name is required"),email:a.Yj().email("Invalid email address"),phone:a.Yj().optional(),source:a.Yj().optional(),status:a.k5(["NEW","CONTACTED","INTERESTED","CONVERTED","LOST"]).default("NEW")});i.partial();let n=a.Ik({content:a.Yj().min(1,"Content is required"),leadId:a.Yj().min(1,"Lead ID is required")}),d=a.Ik({title:a.Yj().min(1,"Title is required"),description:a.Yj().optional(),dueDate:a.Yj().datetime("Invalid date format"),leadId:a.Yj().min(1,"Lead ID is required")});d.partial().extend({completed:a.zM().optional()}),a.Ik({leads:a.YO(i.omit({status:!0}))})},646:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(5155),l=r(2115),s=r(9434);let i=l.forwardRef((e,t)=>{let{className:r,type:l,...i}=e;return(0,a.jsx)("input",{type:l,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},2525:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},3243:(e,t,r)=>{Promise.resolve().then(r.bind(r,6596))},3717:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4165:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>n,L3:()=>o,c7:()=>d,lG:()=>i});var a=r(5155),l=r(2115),s=r(9434);let i=e=>{let{open:t,onOpenChange:r,children:s}=e;return(l.useEffect(()=>{let e=e=>{"Escape"===e.key&&r&&r(!1)};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,r]),t)?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50",onClick:()=>null==r?void 0:r(!1)}),(0,a.jsx)("div",{className:"relative z-50 max-h-[90vh] overflow-auto",children:s})]}):null},n=l.forwardRef((e,t)=>{let{className:r,children:l,...i}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("bg-background p-6 shadow-lg rounded-lg border max-w-lg w-full mx-4",r),...i,children:l})});n.displayName="DialogContent";let d=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 text-center sm:text-left",r),...l})});d.displayName="DialogHeader";let o=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("h2",{ref:t,className:(0,s.cn)("text-lg font-semibold leading-none tracking-tight",r),...l})});o.displayName="DialogTitle",l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...l})}).displayName="DialogDescription"},4616:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var a=r(5155);r(2115);var l=r(2085),s=r(9434);let i=(0,l.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:r,...l}=e;return(0,a.jsx)("div",{className:(0,s.cn)(i({variant:r}),t),...l})}},6596:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(5155),l=r(2115),s=r(2177),i=r(221),n=r(343),d=r(285),o=r(2523),c=r(9409),u=r(6695),f=r(6126),m=r(4165),p=r(7759),h=r(4616),x=r(9074),g=r(646);let v=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var y=r(3717),w=r(2525);function b(){let[e,t]=(0,l.useState)([]),[r,b]=(0,l.useState)([]),[j,N]=(0,l.useState)(!0),[k,D]=(0,l.useState)(!1),[C,E]=(0,l.useState)(!1),[A,R]=(0,l.useState)(null),[S,I]=(0,l.useState)("pending"),F=(0,s.mN)({resolver:(0,i.u)(n.EA),defaultValues:{title:"",description:"",dueDate:"",leadId:""}});(0,l.useEffect)(()=>{T(),L()},[]);let T=async()=>{try{let e=await fetch("/api/leads?limit=100");if(e.ok){let t=await e.json();b(t.leads)}}catch(e){console.error("Error fetching leads:",e)}},L=async()=>{try{let e=await fetch("/api/follow-ups");if(e.ok){let r=await e.json();t(r)}}catch(e){console.error("Error fetching follow-ups:",e)}finally{N(!1)}},z=async e=>{try{let t=A?"/api/follow-ups/".concat(A.id):"/api/follow-ups",r=A?"PUT":"POST";(await fetch(t,{method:r,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok&&(await L(),D(!1),E(!1),R(null),F.reset())}catch(e){console.error("Error saving follow-up:",e)}},Y=async(e,t)=>{try{(await fetch("/api/follow-ups/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({completed:t})})).ok&&await L()}catch(e){console.error("Error updating follow-up:",e)}},M=async e=>{if(confirm("Are you sure you want to delete this follow-up?"))try{(await fetch("/api/follow-ups/".concat(e),{method:"DELETE"})).ok&&await L()}catch(e){console.error("Error deleting follow-up:",e)}},O=(()=>{let t=new Date;return e.filter(e=>{let r=new Date(e.dueDate);switch(S){case"pending":return!e.completed&&r>=t;case"completed":return e.completed;case"overdue":return!e.completed&&r<t;default:return!0}}).sort((e,t)=>new Date(e.dueDate).getTime()-new Date(t.dueDate).getTime())})();return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Follow-ups"}),(0,a.jsxs)(d.$,{onClick:()=>D(!0),children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Add Follow-up"]})]}),(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsx)(u.ZB,{children:"Filter Follow-ups"})}),(0,a.jsx)(u.Wu,{children:(0,a.jsx)("div",{className:"flex gap-2",children:[{key:"pending",label:"Pending",count:e.filter(e=>!e.completed&&new Date(e.dueDate)>=new Date).length},{key:"overdue",label:"Overdue",count:e.filter(e=>!e.completed&&new Date(e.dueDate)<new Date).length},{key:"completed",label:"Completed",count:e.filter(e=>e.completed).length},{key:"all",label:"All",count:e.length}].map(e=>(0,a.jsxs)(d.$,{variant:S===e.key?"default":"outline",onClick:()=>I(e.key),className:"flex items-center gap-2",children:[e.label,(0,a.jsx)(f.E,{variant:"secondary",children:e.count})]},e.key))})})]}),(0,a.jsx)("div",{className:"space-y-4",children:j?(0,a.jsx)("p",{children:"Loading follow-ups..."}):0===O.length?(0,a.jsx)(u.Zp,{children:(0,a.jsxs)(u.Wu,{className:"text-center py-8",children:[(0,a.jsx)(x.A,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No follow-ups found for the selected filter."})]})}):O.map(e=>{let t=(e=>{if(e.completed)return"completed";let t=new Date;return new Date(e.dueDate)<t?"overdue":"pending"})(e);return(0,a.jsx)(u.Zp,{children:(0,a.jsx)(u.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.title}),(e=>{switch(e){case"completed":return(0,a.jsx)(f.E,{className:"bg-green-500 text-white",children:"Completed"});case"overdue":return(0,a.jsx)(f.E,{className:"bg-red-500 text-white",children:"Overdue"});case"pending":return(0,a.jsx)(f.E,{className:"bg-blue-500 text-white",children:"Pending"});default:return(0,a.jsx)(f.E,{variant:"outline",children:"Unknown"})}})(t)]}),e.description&&(0,a.jsx)("p",{className:"text-gray-700",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),new Date(e.dueDate).toLocaleString()]}),(0,a.jsxs)("div",{children:["Lead: ",e.lead.name," (",e.lead.email,")"]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[!e.completed&&(0,a.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>Y(e.id,!0),children:(0,a.jsx)(g.A,{className:"h-4 w-4"})}),e.completed&&(0,a.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>Y(e.id,!1),children:(0,a.jsx)(v,{className:"h-4 w-4"})}),(0,a.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>{R(e),F.reset({title:e.title,description:e.description||"",dueDate:new Date(e.dueDate).toISOString().slice(0,16),leadId:e.leadId}),E(!0)},children:(0,a.jsx)(y.A,{className:"h-4 w-4"})}),(0,a.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>M(e.id),children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})]})]})})},e.id)})}),(0,a.jsx)(m.lG,{open:k||C,onOpenChange:e=>{e||(D(!1),E(!1),R(null),F.reset())},children:(0,a.jsxs)(m.Cf,{children:[(0,a.jsx)(m.c7,{children:(0,a.jsx)(m.L3,{children:A?"Edit Follow-up":"Add New Follow-up"})}),(0,a.jsxs)(p.lV,{onSubmit:F.handleSubmit(z),children:[(0,a.jsxs)(p.zB,{children:[(0,a.jsx)(p.lR,{htmlFor:"leadId",children:"Lead"}),(0,a.jsxs)(c.l,{...F.register("leadId"),children:[(0,a.jsx)("option",{value:"",children:"Select a lead"}),r.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.name," (",e.email,")"]},e.id))]}),F.formState.errors.leadId&&(0,a.jsx)(p.C5,{children:F.formState.errors.leadId.message})]}),(0,a.jsxs)(p.zB,{children:[(0,a.jsx)(p.lR,{htmlFor:"title",children:"Title"}),(0,a.jsx)(o.p,{id:"title",placeholder:"Enter follow-up title",...F.register("title")}),F.formState.errors.title&&(0,a.jsx)(p.C5,{children:F.formState.errors.title.message})]}),(0,a.jsxs)(p.zB,{children:[(0,a.jsx)(p.lR,{htmlFor:"description",children:"Description (Optional)"}),(0,a.jsx)("textarea",{id:"description",rows:3,placeholder:"Enter description...",className:"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",...F.register("description")})]}),(0,a.jsxs)(p.zB,{children:[(0,a.jsx)(p.lR,{htmlFor:"dueDate",children:"Due Date"}),(0,a.jsx)(o.p,{id:"dueDate",type:"datetime-local",...F.register("dueDate")}),F.formState.errors.dueDate&&(0,a.jsx)(p.C5,{children:F.formState.errors.dueDate.message})]}),(0,a.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,a.jsx)(d.$,{type:"submit",className:"flex-1",children:A?"Update Follow-up":"Create Follow-up"}),(0,a.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>{D(!1),E(!1),R(null),F.reset()},children:"Cancel"})]})]})]})})]})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n});var a=r(5155),l=r(2115),s=r(9434);let i=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...l})});i.displayName="Card";let n=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...l})});n.displayName="CardHeader";let d=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...l})});d.displayName="CardTitle";let o=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...l})});o.displayName="CardDescription";let c=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...l})});c.displayName="CardContent",l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...l})}).displayName="CardFooter"},7759:(e,t,r)=>{"use strict";r.d(t,{C5:()=>o,lR:()=>d,lV:()=>i,zB:()=>n});var a=r(5155),l=r(2115),s=r(9434);let i=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("form",{ref:t,className:(0,s.cn)("space-y-6",r),...l})});i.displayName="Form";let n=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("space-y-2",r),...l})});n.displayName="FormField";let d=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("label",{ref:t,className:(0,s.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",r),...l})});d.displayName="FormLabel";let o=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("p",{ref:t,className:(0,s.cn)("text-sm font-medium text-destructive",r),...l})});o.displayName="FormMessage"},9074:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9409:(e,t,r)=>{"use strict";r.d(t,{l:()=>i});var a=r(5155),l=r(2115),s=r(9434);let i=l.forwardRef((e,t)=>{let{className:r,children:l,...i}=e;return(0,a.jsx)("select",{className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i,children:l})});i.displayName="Select"},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var a=r(2596),l=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,l.QP)((0,a.$)(t))}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(2115);let l=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:n=2,absoluteStrokeWidth:d,className:o="",children:c,iconNode:u,...f}=e;return(0,a.createElement)("svg",{ref:t,...i,width:l,height:l,stroke:r,strokeWidth:d?24*Number(n)/Number(l):n,className:s("lucide",o),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),d=(e,t)=>{let r=(0,a.forwardRef)((r,i)=>{let{className:d,...o}=r;return(0,a.createElement)(n,{ref:i,iconNode:t,className:s("lucide-".concat(l(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...o})});return r.displayName=l(e),r}}},e=>{e.O(0,[3,889,441,964,358],()=>e(e.s=3243)),_N_E=e.O()}]);