import * as internal from "./internal/subscriptionRef.js";
import * as Ref from "./Ref.js";
import * as Synchronized from "./SynchronizedRef.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const SubscriptionRefTypeId = internal.SubscriptionRefTypeId;
/**
 * @since 2.0.0
 * @category getters
 */
export const get = internal.get;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndSet = Ref.getAndSet;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndUpdate = Ref.getAndUpdate;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndUpdateEffect = Synchronized.getAndUpdateEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndUpdateSome = Ref.getAndUpdateSome;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndUpdateSomeEffect = Synchronized.getAndUpdateSomeEffect;
/**
 * Creates a new `SubscriptionRef` with the specified value.
 *
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
/**
 * @since 2.0.0
 * @category utils
 */
export const modify = internal.modify;
/**
 * @since 2.0.0
 * @category utils
 */
export const modifyEffect = internal.modifyEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const modifySome = Ref.modifySome;
/**
 * @since 2.0.0
 * @category utils
 */
export const modifySomeEffect = Synchronized.modifySomeEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const set = internal.set;
/**
 * @since 2.0.0
 * @category utils
 */
export const setAndGet = Ref.setAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
export const update = Ref.update;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateEffect = Synchronized.updateEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateAndGet = Ref.updateAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateAndGetEffect = Synchronized.updateAndGetEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateSome = Ref.updateSome;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateSomeEffect = Synchronized.updateSomeEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateSomeAndGet = Ref.updateSomeAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateSomeAndGetEffect = Synchronized.updateSomeAndGetEffect;
//# sourceMappingURL=SubscriptionRef.js.map