"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3],{2085:(e,r,o)=>{o.d(r,{F:()=>l});var t=o(2596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=t.$,l=(e,r)=>o=>{var t;if((null==r?void 0:r.variants)==null)return n(e,null==o?void 0:o.class,null==o?void 0:o.className);let{variants:l,defaultVariants:s}=r,i=Object.keys(l).map(e=>{let r=null==o?void 0:o[e],t=null==s?void 0:s[e];if(null===r)return null;let n=a(r)||a(t);return l[e][n]}),d=o&&Object.entries(o).reduce((e,r)=>{let[o,t]=r;return void 0===t||(e[o]=t),e},{});return n(e,i,null==r||null==(t=r.compoundVariants)?void 0:t.reduce((e,r)=>{let{class:o,className:t,...a}=r;return Object.entries(a).every(e=>{let[r,o]=e;return Array.isArray(o)?o.includes({...s,...d}[r]):({...s,...d})[r]===o})?[...e,o,t]:e},[]),null==o?void 0:o.class,null==o?void 0:o.className)}},2596:(e,r,o)=>{o.d(r,{$:()=>t});function t(){for(var e,r,o=0,t="",a=arguments.length;o<a;o++)(e=arguments[o])&&(r=function e(r){var o,t,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r)if(Array.isArray(r)){var n=r.length;for(o=0;o<n;o++)r[o]&&(t=e(r[o]))&&(a&&(a+=" "),a+=t)}else for(t in r)r[t]&&(a&&(a+=" "),a+=t);return a}(e))&&(t&&(t+=" "),t+=r);return t}},9688:(e,r,o)=>{o.d(r,{QP:()=>ee});let t=(e,r)=>{if(0===e.length)return r.classGroupId;let o=e[0],a=r.nextPart.get(o),n=a?t(e.slice(1),a):void 0;if(n)return n;if(0===r.validators.length)return;let l=e.join("-");return r.validators.find(({validator:e})=>e(l))?.classGroupId},a=/^\[(.+)\]$/,n=(e,r,o,t)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:l(r,e)).classGroupId=o;return}if("function"==typeof e)return s(e)?void n(e(t),r,o,t):void r.validators.push({validator:e,classGroupId:o});Object.entries(e).forEach(([e,a])=>{n(a,l(r,e),o,t)})})},l=(e,r)=>{let o=e;return r.split("-").forEach(e=>{o.nextPart.has(e)||o.nextPart.set(e,{nextPart:new Map,validators:[]}),o=o.nextPart.get(e)}),o},s=e=>e.isThemeGetter,i=/\s+/;function d(){let e,r,o=0,t="";for(;o<arguments.length;)(e=arguments[o++])&&(r=c(e))&&(t&&(t+=" "),t+=r);return t}let c=e=>{let r;if("string"==typeof e)return e;let o="";for(let t=0;t<e.length;t++)e[t]&&(r=c(e[t]))&&(o&&(o+=" "),o+=r);return o},m=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},p=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,u=/^\((?:(\w[\w-]*):)?(.+)\)$/i,b=/^\d+\/\d+$/,f=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,h=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,k=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,x=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,w=e=>b.test(e),v=e=>!!e&&!Number.isNaN(Number(e)),y=e=>!!e&&Number.isInteger(Number(e)),z=e=>e.endsWith("%")&&v(e.slice(0,-1)),j=e=>f.test(e),N=()=>!0,M=e=>g.test(e)&&!h.test(e),P=()=>!1,$=e=>k.test(e),C=e=>x.test(e),G=e=>!I(e)&&!T(e),E=e=>H(e,R,P),I=e=>p.test(e),O=e=>H(e,U,M),S=e=>H(e,X,v),_=e=>H(e,K,P),A=e=>H(e,L,C),W=e=>H(e,Z,$),T=e=>u.test(e),q=e=>J(e,U),F=e=>J(e,Y),Q=e=>J(e,K),V=e=>J(e,R),B=e=>J(e,L),D=e=>J(e,Z,!0),H=(e,r,o)=>{let t=p.exec(e);return!!t&&(t[1]?r(t[1]):o(t[2]))},J=(e,r,o=!1)=>{let t=u.exec(e);return!!t&&(t[1]?r(t[1]):o)},K=e=>"position"===e||"percentage"===e,L=e=>"image"===e||"url"===e,R=e=>"length"===e||"size"===e||"bg-size"===e,U=e=>"length"===e,X=e=>"number"===e,Y=e=>"family-name"===e,Z=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...r){let o,l,s,c=function(i){let d;return l=(o={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,o=new Map,t=new Map,a=(a,n)=>{o.set(a,n),++r>e&&(r=0,t=o,o=new Map)};return{get(e){let r=o.get(e);return void 0!==r?r:void 0!==(r=t.get(e))?(a(e,r),r):void 0},set(e,r){o.has(e)?o.set(e,r):a(e,r)}}})((d=r.reduce((e,r)=>r(e),e())).cacheSize),parseClassName:(e=>{let{prefix:r,experimentalParseClassName:o}=e,t=e=>{let r,o,t=[],a=0,n=0,l=0;for(let o=0;o<e.length;o++){let s=e[o];if(0===a&&0===n){if(":"===s){t.push(e.slice(l,o)),l=o+1;continue}if("/"===s){r=o;continue}}"["===s?a++:"]"===s?a--:"("===s?n++:")"===s&&n--}let s=0===t.length?e:e.substring(l),i=(o=s).endsWith("!")?o.substring(0,o.length-1):o.startsWith("!")?o.substring(1):o;return{modifiers:t,hasImportantModifier:i!==s,baseClassName:i,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};if(r){let e=r+":",o=t;t=r=>r.startsWith(e)?o(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(o){let e=t;t=r=>o({className:r,parseClassName:e})}return t})(d),sortModifiers:(e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let o=[],t=[];return e.forEach(e=>{"["===e[0]||r[e]?(o.push(...t.sort(),e),t=[]):t.push(e)}),o.push(...t.sort()),o}})(d),...(e=>{let r=(e=>{let{theme:r,classGroups:o}=e,t={nextPart:new Map,validators:[]};for(let e in o)n(o[e],t,e,r);return t})(e),{conflictingClassGroups:o,conflictingClassGroupModifiers:l}=e;return{getClassGroupId:e=>{let o=e.split("-");return""===o[0]&&1!==o.length&&o.shift(),t(o,r)||(e=>{if(a.test(e)){let r=a.exec(e)[1],o=r?.substring(0,r.indexOf(":"));if(o)return"arbitrary.."+o}})(e)},getConflictingClassGroupIds:(e,r)=>{let t=o[e]||[];return r&&l[e]?[...t,...l[e]]:t}}})(d)}).cache.get,s=o.cache.set,c=m,m(i)};function m(e){let r=l(e);if(r)return r;let t=((e,r)=>{let{parseClassName:o,getClassGroupId:t,getConflictingClassGroupIds:a,sortModifiers:n}=r,l=[],s=e.trim().split(i),d="";for(let e=s.length-1;e>=0;e-=1){let r=s[e],{isExternal:i,modifiers:c,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:u}=o(r);if(i){d=r+(d.length>0?" "+d:d);continue}let b=!!u,f=t(b?p.substring(0,u):p);if(!f){if(!b||!(f=t(p))){d=r+(d.length>0?" "+d:d);continue}b=!1}let g=n(c).join(":"),h=m?g+"!":g,k=h+f;if(l.includes(k))continue;l.push(k);let x=a(f,b);for(let e=0;e<x.length;++e){let r=x[e];l.push(h+r)}d=r+(d.length>0?" "+d:d)}return d})(e,o);return s(e,t),t}return function(){return c(d.apply(null,arguments))}}(()=>{let e=m("color"),r=m("font"),o=m("text"),t=m("font-weight"),a=m("tracking"),n=m("leading"),l=m("breakpoint"),s=m("container"),i=m("spacing"),d=m("radius"),c=m("shadow"),p=m("inset-shadow"),u=m("text-shadow"),b=m("drop-shadow"),f=m("blur"),g=m("perspective"),h=m("aspect"),k=m("ease"),x=m("animate"),M=()=>["auto","avoid","all","avoid-page","page","left","right","column"],P=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],$=()=>[...P(),T,I],C=()=>["auto","hidden","clip","visible","scroll"],H=()=>["auto","contain","none"],J=()=>[T,I,i],K=()=>[w,"full","auto",...J()],L=()=>[y,"none","subgrid",T,I],R=()=>["auto",{span:["full",y,T,I]},y,T,I],U=()=>[y,"auto",T,I],X=()=>["auto","min","max","fr",T,I],Y=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Z=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...J()],er=()=>[w,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...J()],eo=()=>[e,T,I],et=()=>[...P(),Q,_,{position:[T,I]}],ea=()=>["no-repeat",{repeat:["","x","y","space","round"]}],en=()=>["auto","cover","contain",V,E,{size:[T,I]}],el=()=>[z,q,O],es=()=>["","none","full",d,T,I],ei=()=>["",v,q,O],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],em=()=>[v,z,Q,_],ep=()=>["","none",f,T,I],eu=()=>["none",v,T,I],eb=()=>["none",v,T,I],ef=()=>[v,T,I],eg=()=>[w,"full",...J()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[j],breakpoint:[j],color:[N],container:[j],"drop-shadow":[j],ease:["in","out","in-out"],font:[G],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[j],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[j],shadow:[j],spacing:["px",v],text:[j],"text-shadow":[j],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",w,I,T,h]}],container:["container"],columns:[{columns:[v,I,T,s]}],"break-after":[{"break-after":M()}],"break-before":[{"break-before":M()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:$()}],overflow:[{overflow:C()}],"overflow-x":[{"overflow-x":C()}],"overflow-y":[{"overflow-y":C()}],overscroll:[{overscroll:H()}],"overscroll-x":[{"overscroll-x":H()}],"overscroll-y":[{"overscroll-y":H()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:K()}],"inset-x":[{"inset-x":K()}],"inset-y":[{"inset-y":K()}],start:[{start:K()}],end:[{end:K()}],top:[{top:K()}],right:[{right:K()}],bottom:[{bottom:K()}],left:[{left:K()}],visibility:["visible","invisible","collapse"],z:[{z:[y,"auto",T,I]}],basis:[{basis:[w,"full","auto",s,...J()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[v,w,"auto","initial","none",I]}],grow:[{grow:["",v,T,I]}],shrink:[{shrink:["",v,T,I]}],order:[{order:[y,"first","last","none",T,I]}],"grid-cols":[{"grid-cols":L()}],"col-start-end":[{col:R()}],"col-start":[{"col-start":U()}],"col-end":[{"col-end":U()}],"grid-rows":[{"grid-rows":L()}],"row-start-end":[{row:R()}],"row-start":[{"row-start":U()}],"row-end":[{"row-end":U()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":X()}],"auto-rows":[{"auto-rows":X()}],gap:[{gap:J()}],"gap-x":[{"gap-x":J()}],"gap-y":[{"gap-y":J()}],"justify-content":[{justify:[...Y(),"normal"]}],"justify-items":[{"justify-items":[...Z(),"normal"]}],"justify-self":[{"justify-self":["auto",...Z()]}],"align-content":[{content:["normal",...Y()]}],"align-items":[{items:[...Z(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Z(),{baseline:["","last"]}]}],"place-content":[{"place-content":Y()}],"place-items":[{"place-items":[...Z(),"baseline"]}],"place-self":[{"place-self":["auto",...Z()]}],p:[{p:J()}],px:[{px:J()}],py:[{py:J()}],ps:[{ps:J()}],pe:[{pe:J()}],pt:[{pt:J()}],pr:[{pr:J()}],pb:[{pb:J()}],pl:[{pl:J()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":J()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":J()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[s,"screen",...er()]}],"min-w":[{"min-w":[s,"screen","none",...er()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[l]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",o,q,O]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,T,S]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",z,I]}],"font-family":[{font:[F,I,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,T,I]}],"line-clamp":[{"line-clamp":[v,"none",T,S]}],leading:[{leading:[n,...J()]}],"list-image":[{"list-image":["none",T,I]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",T,I]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:eo()}],"text-color":[{text:eo()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[v,"from-font","auto",T,O]}],"text-decoration-color":[{decoration:eo()}],"underline-offset":[{"underline-offset":[v,"auto",T,I]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:J()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",T,I]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",T,I]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:et()}],"bg-repeat":[{bg:ea()}],"bg-size":[{bg:en()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},y,T,I],radial:["",T,I],conic:[y,T,I]},B,A]}],"bg-color":[{bg:eo()}],"gradient-from-pos":[{from:el()}],"gradient-via-pos":[{via:el()}],"gradient-to-pos":[{to:el()}],"gradient-from":[{from:eo()}],"gradient-via":[{via:eo()}],"gradient-to":[{to:eo()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:eo()}],"border-color-x":[{"border-x":eo()}],"border-color-y":[{"border-y":eo()}],"border-color-s":[{"border-s":eo()}],"border-color-e":[{"border-e":eo()}],"border-color-t":[{"border-t":eo()}],"border-color-r":[{"border-r":eo()}],"border-color-b":[{"border-b":eo()}],"border-color-l":[{"border-l":eo()}],"divide-color":[{divide:eo()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[v,T,I]}],"outline-w":[{outline:["",v,q,O]}],"outline-color":[{outline:eo()}],shadow:[{shadow:["","none",c,D,W]}],"shadow-color":[{shadow:eo()}],"inset-shadow":[{"inset-shadow":["none",p,D,W]}],"inset-shadow-color":[{"inset-shadow":eo()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:eo()}],"ring-offset-w":[{"ring-offset":[v,O]}],"ring-offset-color":[{"ring-offset":eo()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":eo()}],"text-shadow":[{"text-shadow":["none",u,D,W]}],"text-shadow-color":[{"text-shadow":eo()}],opacity:[{opacity:[v,T,I]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[v]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":eo()}],"mask-image-linear-to-color":[{"mask-linear-to":eo()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":eo()}],"mask-image-t-to-color":[{"mask-t-to":eo()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":eo()}],"mask-image-r-to-color":[{"mask-r-to":eo()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":eo()}],"mask-image-b-to-color":[{"mask-b-to":eo()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":eo()}],"mask-image-l-to-color":[{"mask-l-to":eo()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":eo()}],"mask-image-x-to-color":[{"mask-x-to":eo()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":eo()}],"mask-image-y-to-color":[{"mask-y-to":eo()}],"mask-image-radial":[{"mask-radial":[T,I]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":eo()}],"mask-image-radial-to-color":[{"mask-radial-to":eo()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":P()}],"mask-image-conic-pos":[{"mask-conic":[v]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":eo()}],"mask-image-conic-to-color":[{"mask-conic-to":eo()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:et()}],"mask-repeat":[{mask:ea()}],"mask-size":[{mask:en()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",T,I]}],filter:[{filter:["","none",T,I]}],blur:[{blur:ep()}],brightness:[{brightness:[v,T,I]}],contrast:[{contrast:[v,T,I]}],"drop-shadow":[{"drop-shadow":["","none",b,D,W]}],"drop-shadow-color":[{"drop-shadow":eo()}],grayscale:[{grayscale:["",v,T,I]}],"hue-rotate":[{"hue-rotate":[v,T,I]}],invert:[{invert:["",v,T,I]}],saturate:[{saturate:[v,T,I]}],sepia:[{sepia:["",v,T,I]}],"backdrop-filter":[{"backdrop-filter":["","none",T,I]}],"backdrop-blur":[{"backdrop-blur":ep()}],"backdrop-brightness":[{"backdrop-brightness":[v,T,I]}],"backdrop-contrast":[{"backdrop-contrast":[v,T,I]}],"backdrop-grayscale":[{"backdrop-grayscale":["",v,T,I]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[v,T,I]}],"backdrop-invert":[{"backdrop-invert":["",v,T,I]}],"backdrop-opacity":[{"backdrop-opacity":[v,T,I]}],"backdrop-saturate":[{"backdrop-saturate":[v,T,I]}],"backdrop-sepia":[{"backdrop-sepia":["",v,T,I]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":J()}],"border-spacing-x":[{"border-spacing-x":J()}],"border-spacing-y":[{"border-spacing-y":J()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",T,I]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[v,"initial",T,I]}],ease:[{ease:["linear","initial",k,T,I]}],delay:[{delay:[v,T,I]}],animate:[{animate:["none",x,T,I]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,T,I]}],"perspective-origin":[{"perspective-origin":$()}],rotate:[{rotate:eu()}],"rotate-x":[{"rotate-x":eu()}],"rotate-y":[{"rotate-y":eu()}],"rotate-z":[{"rotate-z":eu()}],scale:[{scale:eb()}],"scale-x":[{"scale-x":eb()}],"scale-y":[{"scale-y":eb()}],"scale-z":[{"scale-z":eb()}],"scale-3d":["scale-3d"],skew:[{skew:ef()}],"skew-x":[{"skew-x":ef()}],"skew-y":[{"skew-y":ef()}],transform:[{transform:[T,I,"","none","gpu","cpu"]}],"transform-origin":[{origin:$()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:eo()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:eo()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",T,I]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":J()}],"scroll-mx":[{"scroll-mx":J()}],"scroll-my":[{"scroll-my":J()}],"scroll-ms":[{"scroll-ms":J()}],"scroll-me":[{"scroll-me":J()}],"scroll-mt":[{"scroll-mt":J()}],"scroll-mr":[{"scroll-mr":J()}],"scroll-mb":[{"scroll-mb":J()}],"scroll-ml":[{"scroll-ml":J()}],"scroll-p":[{"scroll-p":J()}],"scroll-px":[{"scroll-px":J()}],"scroll-py":[{"scroll-py":J()}],"scroll-ps":[{"scroll-ps":J()}],"scroll-pe":[{"scroll-pe":J()}],"scroll-pt":[{"scroll-pt":J()}],"scroll-pr":[{"scroll-pr":J()}],"scroll-pb":[{"scroll-pb":J()}],"scroll-pl":[{"scroll-pl":J()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",T,I]}],fill:[{fill:["none",...eo()]}],"stroke-w":[{stroke:[v,q,O,S]}],stroke:[{stroke:["none",...eo()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})}}]);