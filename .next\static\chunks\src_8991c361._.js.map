{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// Auth schemas\nexport const signUpSchema = z.object({\n  name: z.string().min(2, 'Name must be at least 2 characters'),\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n})\n\nexport const signInSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(1, 'Password is required'),\n})\n\n// Lead schemas\nexport const leadSchema = z.object({\n  name: z.string().min(1, 'Name is required'),\n  email: z.string().email('Invalid email address'),\n  phone: z.string().optional(),\n  source: z.string().optional(),\n  status: z.enum(['NEW', 'CONTACTED', 'INTERESTED', 'CONVERTED', 'LOST']).default('NEW'),\n})\n\nexport const updateLeadSchema = leadSchema.partial()\n\n// Note schemas\nexport const noteSchema = z.object({\n  content: z.string().min(1, 'Content is required'),\n  leadId: z.string().min(1, 'Lead ID is required'),\n})\n\n// Follow-up schemas\nexport const followUpSchema = z.object({\n  title: z.string().min(1, 'Title is required'),\n  description: z.string().optional(),\n  dueDate: z.string().datetime('Invalid date format'),\n  leadId: z.string().min(1, 'Lead ID is required'),\n})\n\nexport const updateFollowUpSchema = followUpSchema.partial().extend({\n  completed: z.boolean().optional(),\n})\n\n// Bulk import schema\nexport const bulkLeadSchema = z.object({\n  leads: z.array(leadSchema.omit({ status: true })),\n})\n\nexport type SignUpInput = z.infer<typeof signUpSchema>\nexport type SignInInput = z.infer<typeof signInSchema>\nexport type LeadInput = z.infer<typeof leadSchema>\nexport type UpdateLeadInput = z.infer<typeof updateLeadSchema>\nexport type NoteInput = z.infer<typeof noteSchema>\nexport type FollowUpInput = z.infer<typeof followUpSchema>\nexport type UpdateFollowUpInput = z.infer<typeof updateFollowUpSchema>\nexport type BulkLeadInput = z.infer<typeof bulkLeadSchema>\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGO,MAAM,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,MAAM,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAGO,MAAM,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,QAAQ,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,QAAQ,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;QAAa;QAAc;QAAa;KAAO,EAAE,OAAO,CAAC;AAClF;AAEO,MAAM,mBAAmB,WAAW,OAAO;AAG3C,MAAM,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,QAAQ,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC5B;AAGO,MAAM,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,QAAQ,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC5B;AAEO,MAAM,uBAAuB,eAAe,OAAO,GAAG,MAAM,CAAC;IAClE,WAAW,gLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;AACjC;AAGO,MAAM,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,gLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC;QAAE,QAAQ;IAAK;AAChD", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/form.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Form = React.forwardRef<\n  HTMLFormElement,\n  React.FormHTMLAttributes<HTMLFormElement>\n>(({ className, ...props }, ref) => (\n  <form\n    ref={ref}\n    className={cn(\"space-y-6\", className)}\n    {...props}\n  />\n))\nForm.displayName = \"Form\"\n\nconst FormField = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"space-y-2\", className)}\n    {...props}\n  />\n))\nFormField.displayName = \"FormField\"\n\nconst FormLabel = React.forwardRef<\n  HTMLLabelElement,\n  React.LabelHTMLAttributes<HTMLLabelElement>\n>(({ className, ...props }, ref) => (\n  <label\n    ref={ref}\n    className={cn(\n      \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n      className\n    )}\n    {...props}\n  />\n))\nFormLabel.displayName = \"FormLabel\"\n\nconst FormMessage = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm font-medium text-destructive\", className)}\n    {...props}\n  />\n))\nFormMessage.displayName = \"FormMessage\"\n\nexport { Form, FormField, FormLabel, FormMessage }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/app/auth/signup/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { signUpSchema, type SignUpInput } from '@/lib/validations'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Form, FormField, FormLabel, FormMessage } from '@/components/ui/form'\n\nexport default function SignUpPage() {\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n  const router = useRouter()\n\n  const form = useForm<SignUpInput>({\n    resolver: zodResolver(signUpSchema),\n    defaultValues: {\n      name: '',\n      email: '',\n      password: '',\n    },\n  })\n\n  const onSubmit = async (data: SignUpInput) => {\n    setIsLoading(true)\n    setError('')\n\n    try {\n      const response = await fetch('/api/auth/signup', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      })\n\n      const result = await response.json()\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Something went wrong')\n      }\n\n      // Redirect to dashboard on success\n      // Use window.location to ensure full page reload and cookie recognition\n      setTimeout(() => {\n        window.location.href = '/dashboard'\n      }, 100)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Something went wrong')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"space-y-1\">\n          <CardTitle className=\"text-2xl text-center\">Create account</CardTitle>\n          <CardDescription className=\"text-center\">\n            Enter your information to create your account\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Form onSubmit={form.handleSubmit(onSubmit)}>\n            {error && (\n              <div className=\"bg-destructive/15 text-destructive text-sm p-3 rounded-md\">\n                {error}\n              </div>\n            )}\n            \n            <FormField>\n              <FormLabel htmlFor=\"name\">Name</FormLabel>\n              <Input\n                id=\"name\"\n                type=\"text\"\n                placeholder=\"Enter your name\"\n                {...form.register('name')}\n              />\n              {form.formState.errors.name && (\n                <FormMessage>{form.formState.errors.name.message}</FormMessage>\n              )}\n            </FormField>\n\n            <FormField>\n              <FormLabel htmlFor=\"email\">Email</FormLabel>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                {...form.register('email')}\n              />\n              {form.formState.errors.email && (\n                <FormMessage>{form.formState.errors.email.message}</FormMessage>\n              )}\n            </FormField>\n\n            <FormField>\n              <FormLabel htmlFor=\"password\">Password</FormLabel>\n              <Input\n                id=\"password\"\n                type=\"password\"\n                placeholder=\"Enter your password\"\n                {...form.register('password')}\n              />\n              {form.formState.errors.password && (\n                <FormMessage>{form.formState.errors.password.message}</FormMessage>\n              )}\n            </FormField>\n\n            <Button\n              type=\"submit\"\n              className=\"w-full\"\n              disabled={isLoading}\n            >\n              {isLoading ? 'Creating account...' : 'Create account'}\n            </Button>\n\n            <div className=\"text-center text-sm\">\n              Already have an account?{' '}\n              <Link href=\"/auth/signin\" className=\"text-primary hover:underline\">\n                Sign in\n              </Link>\n            </div>\n          </Form>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAae,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAe;QAChC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,4HAAA,CAAA,eAAY;QAClC,eAAe;YACb,MAAM;YACN,OAAO;YACP,UAAU;QACZ;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,mCAAmC;YACnC,wEAAwE;YACxE,WAAW;gBACT,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,GAAG;QACL,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAuB;;;;;;sCAC5C,6LAAC,mIAAA,CAAA,kBAAe;4BAAC,WAAU;sCAAc;;;;;;;;;;;;8BAI3C,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,UAAU,KAAK,YAAY,CAAC;;4BAC/B,uBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAIL,6LAAC,mIAAA,CAAA,YAAS;;kDACR,6LAAC,mIAAA,CAAA,YAAS;wCAAC,SAAQ;kDAAO;;;;;;kDAC1B,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACX,GAAG,KAAK,QAAQ,CAAC,OAAO;;;;;;oCAE1B,KAAK,SAAS,CAAC,MAAM,CAAC,IAAI,kBACzB,6LAAC,mIAAA,CAAA,cAAW;kDAAE,KAAK,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;;;;;;;;;;;;0CAIpD,6LAAC,mIAAA,CAAA,YAAS;;kDACR,6LAAC,mIAAA,CAAA,YAAS;wCAAC,SAAQ;kDAAQ;;;;;;kDAC3B,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACX,GAAG,KAAK,QAAQ,CAAC,QAAQ;;;;;;oCAE3B,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,kBAC1B,6LAAC,mIAAA,CAAA,cAAW;kDAAE,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;;;;;;;;;;;;0CAIrD,6LAAC,mIAAA,CAAA,YAAS;;kDACR,6LAAC,mIAAA,CAAA,YAAS;wCAAC,SAAQ;kDAAW;;;;;;kDAC9B,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACX,GAAG,KAAK,QAAQ,CAAC,WAAW;;;;;;oCAE9B,KAAK,SAAS,CAAC,MAAM,CAAC,QAAQ,kBAC7B,6LAAC,mIAAA,CAAA,cAAW;kDAAE,KAAK,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO;;;;;;;;;;;;0CAIxD,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,UAAU;0CAET,YAAY,wBAAwB;;;;;;0CAGvC,6LAAC;gCAAI,WAAU;;oCAAsB;oCACV;kDACzB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;kDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjF;GAxHwB;;QAGP,qIAAA,CAAA,YAAS;QAEX,iKAAA,CAAA,UAAO;;;KALE", "debugId": null}}]}