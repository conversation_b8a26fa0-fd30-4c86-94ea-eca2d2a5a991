import * as circular from "./internal/effect/circular.js";
import * as ref from "./internal/ref.js";
import * as internal from "./internal/synchronizedRef.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const SynchronizedRefTypeId = circular.SynchronizedTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
export const make = circular.makeSynchronized;
/**
 * @since 2.0.0
 * @category getters
 */
export const get = ref.get;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndSet = ref.getAndSet;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndUpdate = ref.getAndUpdate;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndUpdateEffect = internal.getAndUpdateEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndUpdateSome = ref.getAndUpdateSome;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndUpdateSomeEffect = internal.getAndUpdateSomeEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const modify = internal.modify;
/**
 * @since 2.0.0
 * @category utils
 */
export const modifyEffect = internal.modifyEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const modifySome = ref.modifySome;
/**
 * @since 2.0.0
 * @category utils
 */
export const modifySomeEffect = internal.modifySomeEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const set = ref.set;
/**
 * @since 2.0.0
 * @category utils
 */
export const setAndGet = ref.setAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
export const update = ref.update;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateEffect = internal.updateEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateAndGet = ref.updateAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateAndGetEffect = internal.updateAndGetEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateSome = ref.updateSome;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateSomeEffect = internal.updateSomeEffect;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateSomeAndGet = ref.updateSomeAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateSomeAndGetEffect = circular.updateSomeAndGetEffectSynchronized;
/**
 * @since 2.0.0
 * @category unsafe
 */
export const unsafeMake = circular.unsafeMakeSynchronized;
//# sourceMappingURL=SynchronizedRef.js.map