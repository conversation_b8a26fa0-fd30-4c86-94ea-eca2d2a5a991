/**
 * @since 2.0.0
 */
import * as internal from "./internal/stream/haltStrategy.js";
/**
 * @since 2.0.0
 * @category constructors
 */
export const Left = internal.Left;
/**
 * @since 2.0.0
 * @category constructors
 */
export const Right = internal.Right;
/**
 * @since 2.0.0
 * @category constructors
 */
export const Both = internal.Both;
/**
 * @since 2.0.0
 * @category constructors
 */
export const Either = internal.Either;
/**
 * @since 2.0.0
 * @category constructors
 */
export const fromInput = internal.fromInput;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isLeft = internal.isLeft;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isRight = internal.isRight;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isBoth = internal.isBoth;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isEither = internal.isEither;
/**
 * Folds over the specified `HaltStrategy` using the provided case functions.
 *
 * @since 2.0.0
 * @category folding
 */
export const match = internal.match;
//# sourceMappingURL=StreamHaltStrategy.js.map