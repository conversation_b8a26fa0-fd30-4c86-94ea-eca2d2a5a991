"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.match = exports.isRight = exports.isLeft = exports.isEither = exports.isBoth = exports.fromInput = exports.Right = exports.Left = exports.Either = exports.Both = void 0;
var internal = _interopRequireWildcard(require("./internal/stream/haltStrategy.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */

/**
 * @since 2.0.0
 * @category constructors
 */
const Left = exports.Left = internal.Left;
/**
 * @since 2.0.0
 * @category constructors
 */
const Right = exports.Right = internal.Right;
/**
 * @since 2.0.0
 * @category constructors
 */
const Both = exports.Both = internal.Both;
/**
 * @since 2.0.0
 * @category constructors
 */
const Either = exports.Either = internal.Either;
/**
 * @since 2.0.0
 * @category constructors
 */
const fromInput = exports.fromInput = internal.fromInput;
/**
 * @since 2.0.0
 * @category refinements
 */
const isLeft = exports.isLeft = internal.isLeft;
/**
 * @since 2.0.0
 * @category refinements
 */
const isRight = exports.isRight = internal.isRight;
/**
 * @since 2.0.0
 * @category refinements
 */
const isBoth = exports.isBoth = internal.isBoth;
/**
 * @since 2.0.0
 * @category refinements
 */
const isEither = exports.isEither = internal.isEither;
/**
 * Folds over the specified `HaltStrategy` using the provided case functions.
 *
 * @since 2.0.0
 * @category folding
 */
const match = exports.match = internal.match;
//# sourceMappingURL=StreamHaltStrategy.js.map