import * as internal from "./internal/take.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const TakeTypeId = internal.TakeTypeId;
/**
 * Creates a `Take` with the specified chunk.
 *
 * @since 2.0.0
 * @category constructors
 */
export const chunk = internal.chunk;
/**
 * Creates a failing `Take` with the specified defect.
 *
 * @since 2.0.0
 * @category constructors
 */
export const die = internal.die;
/**
 * Creates a failing `Take` with the specified error message.
 *
 * @since 2.0.0
 * @category constructors
 */
export const dieMessage = internal.dieMessage;
/**
 * Transforms a `Take<A, E>` to an `Effect<A, E>`.
 *
 * @since 2.0.0
 * @category destructors
 */
export const done = internal.done;
/**
 * Represents the end-of-stream marker.
 *
 * @since 2.0.0
 * @category constructors
 */
export const end = internal.end;
/**
 * Creates a failing `Take` with the specified error.
 *
 * @since 2.0.0
 * @category constructors
 */
export const fail = internal.fail;
/**
 * Creates a failing `Take` with the specified cause.
 *
 * @since 2.0.0
 * @category constructors
 */
export const failCause = internal.failCause;
/**
 * Creates an effect from `Effect<A, E, R>` that does not fail, but succeeds with
 * the `Take<A, E>`. Error from stream when pulling is converted to
 * `Take.failCause`. Creates a single value chunk.
 *
 * @since 2.0.0
 * @category constructors
 */
export const fromEffect = internal.fromEffect;
/**
 * Creates a `Take` from an `Exit`.
 *
 * @since 2.0.0
 * @category constructors
 */
export const fromExit = internal.fromExit;
/**
 * Creates effect from `Effect<Chunk<A>, Option<E>, R>` that does not fail, but
 * succeeds with the `Take<A, E>`. Errors from stream when pulling are converted
 * to `Take.failCause`, and the end-of-stream is converted to `Take.end`.
 *
 * @since 2.0.0
 * @category constructors
 */
export const fromPull = internal.fromPull;
/**
 * Checks if this `take` is done (`Take.end`).
 *
 * @since 2.0.0
 * @category getters
 */
export const isDone = internal.isDone;
/**
 * Checks if this `take` is a failure.
 *
 * @since 2.0.0
 * @category getters
 */
export const isFailure = internal.isFailure;
/**
 * Checks if this `take` is a success.
 *
 * @since 2.0.0
 * @category getters
 */
export const isSuccess = internal.isSuccess;
/**
 * Constructs a `Take`.
 *
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
/**
 * Transforms `Take<A, E>` to `Take<B, A>` by applying function `f`.
 *
 * @since 2.0.0
 * @category mapping
 */
export const map = internal.map;
/**
 * Folds over the failure cause, success value and end-of-stream marker to
 * yield a value.
 *
 * @since 2.0.0
 * @category destructors
 */
export const match = internal.match;
/**
 * Effectful version of `Take.fold`.
 *
 * Folds over the failure cause, success value and end-of-stream marker to
 * yield an effect.
 *
 * @since 2.0.0
 * @category destructors
 */
export const matchEffect = internal.matchEffect;
/**
 * Creates a `Take` with a single value chunk.
 *
 * @since 2.0.0
 * @category constructors
 */
export const of = internal.of;
/**
 * Returns an effect that effectfully "peeks" at the success of this take.
 *
 * @since 2.0.0
 * @category sequencing
 */
export const tap = internal.tap;
//# sourceMappingURL=Take.js.map