{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/providers/query-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools'\nimport { useState } from 'react'\n\nexport default function QueryProvider({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            staleTime: 60 * 1000, // 1 minute\n            gcTime: 10 * 60 * 1000, // 10 minutes\n            retry: (failureCount, error: any) => {\n              // Don't retry on 401/403 errors\n              if (error?.status === 401 || error?.status === 403) {\n                return false\n              }\n              return failureCount < 3\n            },\n          },\n        },\n      })\n  )\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      <ReactQueryDevtools initialIsOpen={false} />\n    </QueryClientProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAMe,SAAS,cAAc,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IACpC,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;kCAC3B,IACE,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACd,gBAAgB;oBACd,SAAS;wBACP,WAAW,KAAK;wBAChB,QAAQ,KAAK,KAAK;wBAClB,KAAK;sDAAE,CAAC,cAAc;gCACpB,gCAAgC;gCAChC,IAAI,CAAA,kBAAA,4BAAA,MAAO,MAAM,MAAK,OAAO,CAAA,kBAAA,4BAAA,MAAO,MAAM,MAAK,KAAK;oCAClD,OAAO;gCACT;gCACA,OAAO,eAAe;4BACxB;;oBACF;gBACF;YACF;;IAGJ,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;0BACD,6LAAC,uLAAA,CAAA,qBAAkB;gBAAC,eAAe;;;;;;;;;;;;AAGzC;GA1BwB;KAAA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/providers/auth-provider.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\n\ninterface User {\n  id: string\n  name: string\n  email: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  login: (email: string, password: string) => Promise<void>\n  signup: (name: string, email: string, password: string) => Promise<void>\n  logout: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const router = useRouter()\n\n  // Check authentication status on mount\n  useEffect(() => {\n    checkAuth()\n  }, [])\n\n  const checkAuth = async () => {\n    try {\n      const response = await fetch('/api/test-auth')\n      if (response.ok) {\n        const data = await response.json()\n        if (data.authenticated && data.user) {\n          setUser(data.user)\n        }\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const login = async (email: string, password: string) => {\n    const response = await fetch('/api/auth/signin', {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/json' },\n      body: JSON.stringify({ email, password }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Login failed')\n    }\n\n    const data = await response.json()\n    setUser(data.user)\n    router.push('/dashboard')\n  }\n\n  const signup = async (name: string, email: string, password: string) => {\n    const response = await fetch('/api/auth/signup', {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/json' },\n      body: JSON.stringify({ name, email, password }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Signup failed')\n    }\n\n    const data = await response.json()\n    setUser(data.user)\n    router.push('/dashboard')\n  }\n\n  const logout = async () => {\n    try {\n      await fetch('/api/auth/signout', { method: 'POST' })\n    } catch (error) {\n      console.error('Logout error:', error)\n    } finally {\n      setUser(null)\n      router.push('/auth/signin')\n    }\n  }\n\n  return (\n    <AuthContext.Provider value={{ user, loading, login, signup, logout }}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAmBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAQT,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,aAAa,IAAI,KAAK,IAAI,EAAE;oBACnC,QAAQ,KAAK,IAAI;gBACnB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,MAAM,WAAW,MAAM,MAAM,oBAAoB;YAC/C,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,KAAK,IAAI;QACjB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,SAAS,OAAO,MAAc,OAAe;QACjD,MAAM,WAAW,MAAM,MAAM,oBAAoB;YAC/C,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAM;gBAAO;YAAS;QAC/C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,KAAK,IAAI;QACjB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,MAAM,qBAAqB;gBAAE,QAAQ;YAAO;QACpD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,QAAQ;YACR,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;YAAO;YAAQ;QAAO;kBACjE;;;;;;AAGP;IA5EgB;;QAGC,qIAAA,CAAA,YAAS;;;KAHV", "debugId": null}}]}