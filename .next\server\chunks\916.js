"use strict";exports.id=916,exports.ids=[916],exports.modules={87916:(a,b,c)=>{function d(a,b,c){function d(c,d){var e;for(let f in Object.defineProperty(c,"_zod",{value:c._zod??{},enumerable:!1}),(e=c._zod).traits??(e.traits=new Set),c._zod.traits.add(a),b(c,d),g.prototype)f in c||Object.defineProperty(c,f,{value:g.prototype[f].bind(c)});c._zod.constr=g,c._zod.def=d}let e=c?.Parent??Object;class f extends e{}function g(a){var b;let e=c?.Parent?new f:this;for(let c of(d(e,a),(b=e._zod).deferred??(b.deferred=[]),e._zod.deferred))c();return e}return Object.defineProperty(f,"name",{value:a}),Object.defineProperty(g,"init",{value:d}),Object.defineProperty(g,Symbol.hasInstance,{value:b=>!!c?.Parent&&b instanceof c.Parent||b?._zod?.traits?.has(a)}),Object.defineProperty(g,"name",{value:a}),g}c.d(b,{EB:()=>bG,YO:()=>b4,zM:()=>b_,k5:()=>ca,Ik:()=>b6,Yj:()=>bF}),Object.freeze({status:"aborted"}),Symbol("zod_brand");class e extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let f={};function g(a){return a&&Object.assign(f,a),f}let h=/^[cC][^\s-]{8,}$/,i=/^[0-9a-z]+$/,j=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,k=/^[0-9a-vA-V]{20}$/,l=/^[A-Za-z0-9]{27}$/,m=/^[a-zA-Z0-9_-]{21}$/,n=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,o=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,p=a=>a?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${a}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,q=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,r=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,s=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,t=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,u=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,v=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,w=/^[A-Za-z0-9_-]*$/,x=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,y=/^\+(?:[0-9]){6,14}[0-9]$/,z="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",A=RegExp(`^${z}$`);function B(a){let b="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof a.precision?-1===a.precision?`${b}`:0===a.precision?`${b}:[0-5]\\d`:`${b}:[0-5]\\d\\.\\d{${a.precision}}`:`${b}(?::[0-5]\\d(?:\\.\\d+)?)?`}let C=/true|false/i,D=/^[^A-Z]*$/,E=/^[^a-z]*$/;function F(a,b){return"bigint"==typeof b?b.toString():b}function G(a){return{get value(){{let b=a();return Object.defineProperty(this,"value",{value:b}),b}}}}function H(a){let b=+!!a.startsWith("^"),c=a.endsWith("$")?a.length-1:a.length;return a.slice(b,c)}let I=Symbol("evaluating");function J(a,b,c){let d;Object.defineProperty(a,b,{get(){if(d!==I)return void 0===d&&(d=I,d=c()),d},set(c){Object.defineProperty(a,b,{value:c})},configurable:!0})}function K(a,b,c){Object.defineProperty(a,b,{value:c,writable:!0,enumerable:!0,configurable:!0})}function L(...a){let b={};for(let c of a)Object.assign(b,Object.getOwnPropertyDescriptors(c));return Object.defineProperties({},b)}function M(a){return JSON.stringify(a)}let N="captureStackTrace"in Error?Error.captureStackTrace:(...a)=>{};function O(a){return"object"==typeof a&&null!==a&&!Array.isArray(a)}let P=G(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(a){return!1}});function Q(a){if(!1===O(a))return!1;let b=a.constructor;if(void 0===b)return!0;let c=b.prototype;return!1!==O(c)&&!1!==Object.prototype.hasOwnProperty.call(c,"isPrototypeOf")}let R=new Set(["string","number","symbol"]);function S(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function T(a,b,c){let d=new a._zod.constr(b??a._zod.def);return(!b||c?.parent)&&(d._zod.parent=a),d}function U(a){if(!a)return{};if("string"==typeof a)return{error:()=>a};if(a?.message!==void 0){if(a?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");a.error=a.message}return(delete a.message,"string"==typeof a.error)?{...a,error:()=>a.error}:a}function V(a,b=0){for(let c=b;c<a.issues.length;c++)if(a.issues[c]?.continue!==!0)return!0;return!1}function W(a,b){return b.map(b=>(b.path??(b.path=[]),b.path.unshift(a),b))}function X(a){return"string"==typeof a?a:a?.message}function Y(a,b,c){let d={...a,path:a.path??[]};return a.message||(d.message=X(a.inst?._zod.def?.error?.(a))??X(b?.error?.(a))??X(c.customError?.(a))??X(c.localeError?.(a))??"Invalid input"),delete d.inst,delete d.continue,b?.reportInput||delete d.input,d}function Z(a){return Array.isArray(a)?"array":"string"==typeof a?"string":"unknown"}function $(...a){let[b,c,d]=a;return"string"==typeof b?{message:b,code:"custom",input:c,inst:d}:{...b}}Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MAX_VALUE,Number.MAX_VALUE;let _=d("$ZodCheck",(a,b)=>{var c;a._zod??(a._zod={}),a._zod.def=b,(c=a._zod).onattach??(c.onattach=[])}),aa=d("$ZodCheckMaxLength",(a,b)=>{var c;_.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.maximum??1/0;b.maximum<c&&(a._zod.bag.maximum=b.maximum)}),a._zod.check=c=>{let d=c.value;if(d.length<=b.maximum)return;let e=Z(d);c.issues.push({origin:e,code:"too_big",maximum:b.maximum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),ab=d("$ZodCheckMinLength",(a,b)=>{var c;_.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.minimum??-1/0;b.minimum>c&&(a._zod.bag.minimum=b.minimum)}),a._zod.check=c=>{let d=c.value;if(d.length>=b.minimum)return;let e=Z(d);c.issues.push({origin:e,code:"too_small",minimum:b.minimum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),ac=d("$ZodCheckLengthEquals",(a,b)=>{var c;_.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag;c.minimum=b.length,c.maximum=b.length,c.length=b.length}),a._zod.check=c=>{let d=c.value,e=d.length;if(e===b.length)return;let f=Z(d),g=e>b.length;c.issues.push({origin:f,...g?{code:"too_big",maximum:b.length}:{code:"too_small",minimum:b.length},inclusive:!0,exact:!0,input:c.value,inst:a,continue:!b.abort})}}),ad=d("$ZodCheckStringFormat",(a,b)=>{var c,d;_.init(a,b),a._zod.onattach.push(a=>{let c=a._zod.bag;c.format=b.format,b.pattern&&(c.patterns??(c.patterns=new Set),c.patterns.add(b.pattern))}),b.pattern?(c=a._zod).check??(c.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:b.format,input:c.value,...b.pattern?{pattern:b.pattern.toString()}:{},inst:a,continue:!b.abort})}):(d=a._zod).check??(d.check=()=>{})}),ae=d("$ZodCheckRegex",(a,b)=>{ad.init(a,b),a._zod.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:"regex",input:c.value,pattern:b.pattern.toString(),inst:a,continue:!b.abort})}}),af=d("$ZodCheckLowerCase",(a,b)=>{b.pattern??(b.pattern=D),ad.init(a,b)}),ag=d("$ZodCheckUpperCase",(a,b)=>{b.pattern??(b.pattern=E),ad.init(a,b)}),ah=d("$ZodCheckIncludes",(a,b)=>{_.init(a,b);let c=S(b.includes),d=new RegExp("number"==typeof b.position?`^.{${b.position}}${c}`:c);b.pattern=d,a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(d)}),a._zod.check=c=>{c.value.includes(b.includes,b.position)||c.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:b.includes,input:c.value,inst:a,continue:!b.abort})}}),ai=d("$ZodCheckStartsWith",(a,b)=>{_.init(a,b);let c=RegExp(`^${S(b.prefix)}.*`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.startsWith(b.prefix)||c.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:b.prefix,input:c.value,inst:a,continue:!b.abort})}}),aj=d("$ZodCheckEndsWith",(a,b)=>{_.init(a,b);let c=RegExp(`.*${S(b.suffix)}$`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.endsWith(b.suffix)||c.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:b.suffix,input:c.value,inst:a,continue:!b.abort})}}),ak=d("$ZodCheckOverwrite",(a,b)=>{_.init(a,b),a._zod.check=a=>{a.value=b.tx(a.value)}});class al{constructor(a=[]){this.content=[],this.indent=0,this&&(this.args=a)}indented(a){this.indent+=1,a(this),this.indent-=1}write(a){if("function"==typeof a){a(this,{execution:"sync"}),a(this,{execution:"async"});return}let b=a.split("\n").filter(a=>a),c=Math.min(...b.map(a=>a.length-a.trimStart().length));for(let a of b.map(a=>a.slice(c)).map(a=>" ".repeat(2*this.indent)+a))this.content.push(a)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(a=>`  ${a}`)].join("\n"))}}let am=(a,b)=>{a.name="$ZodError",Object.defineProperty(a,"_zod",{value:a._zod,enumerable:!1}),Object.defineProperty(a,"issues",{value:b,enumerable:!1}),a.message=JSON.stringify(b,F,2),Object.defineProperty(a,"toString",{value:()=>a.message,enumerable:!1})},an=d("$ZodError",am),ao=d("$ZodError",am,{Parent:Error}),ap=a=>(b,c,d)=>{let f=d?{...d,async:!1}:{async:!1},h=b._zod.run({value:c,issues:[]},f);if(h instanceof Promise)throw new e;return h.issues.length?{success:!1,error:new(a??an)(h.issues.map(a=>Y(a,f,g())))}:{success:!0,data:h.value}},aq=ap(ao),ar=a=>async(b,c,d)=>{let e=d?Object.assign(d,{async:!0}):{async:!0},f=b._zod.run({value:c,issues:[]},e);return f instanceof Promise&&(f=await f),f.issues.length?{success:!1,error:new a(f.issues.map(a=>Y(a,e,g())))}:{success:!0,data:f.value}},as=ar(ao),at={major:4,minor:0,patch:14},au=d("$ZodType",(a,b)=>{var c;a??(a={}),a._zod.def=b,a._zod.bag=a._zod.bag||{},a._zod.version=at;let d=[...a._zod.def.checks??[]];for(let b of(a._zod.traits.has("$ZodCheck")&&d.unshift(a),d))for(let c of b._zod.onattach)c(a);if(0===d.length)(c=a._zod).deferred??(c.deferred=[]),a._zod.deferred?.push(()=>{a._zod.run=a._zod.parse});else{let b=(a,b,c)=>{let d,f=V(a);for(let g of b){if(g._zod.def.when){if(!g._zod.def.when(a))continue}else if(f)continue;let b=a.issues.length,h=g._zod.check(a);if(h instanceof Promise&&c?.async===!1)throw new e;if(d||h instanceof Promise)d=(d??Promise.resolve()).then(async()=>{await h,a.issues.length!==b&&(f||(f=V(a,b)))});else{if(a.issues.length===b)continue;f||(f=V(a,b))}}return d?d.then(()=>a):a};a._zod.run=(c,f)=>{let g=a._zod.parse(c,f);if(g instanceof Promise){if(!1===f.async)throw new e;return g.then(a=>b(a,d,f))}return b(g,d,f)}}a["~standard"]={validate:b=>{try{let c=aq(a,b);return c.success?{value:c.data}:{issues:c.error?.issues}}catch(c){return as(a,b).then(a=>a.success?{value:a.data}:{issues:a.error?.issues})}},vendor:"zod",version:1}}),av=d("$ZodString",(a,b)=>{au.init(a,b),a._zod.pattern=[...a?._zod.bag?.patterns??[]].pop()??(a=>{let b=a?`[\\s\\S]{${a?.minimum??0},${a?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${b}$`)})(a._zod.bag),a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=String(c.value)}catch(a){}return"string"==typeof c.value||c.issues.push({expected:"string",code:"invalid_type",input:c.value,inst:a}),c}}),aw=d("$ZodStringFormat",(a,b)=>{ad.init(a,b),av.init(a,b)}),ax=d("$ZodGUID",(a,b)=>{b.pattern??(b.pattern=o),aw.init(a,b)}),ay=d("$ZodUUID",(a,b)=>{if(b.version){let a={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[b.version];if(void 0===a)throw Error(`Invalid UUID version: "${b.version}"`);b.pattern??(b.pattern=p(a))}else b.pattern??(b.pattern=p());aw.init(a,b)}),az=d("$ZodEmail",(a,b)=>{b.pattern??(b.pattern=q),aw.init(a,b)}),aA=d("$ZodURL",(a,b)=>{aw.init(a,b),a._zod.check=c=>{try{let d=c.value.trim(),e=new URL(d);b.hostname&&(b.hostname.lastIndex=0,b.hostname.test(e.hostname)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:x.source,input:c.value,inst:a,continue:!b.abort})),b.protocol&&(b.protocol.lastIndex=0,b.protocol.test(e.protocol.endsWith(":")?e.protocol.slice(0,-1):e.protocol)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:b.protocol.source,input:c.value,inst:a,continue:!b.abort})),b.normalize?c.value=e.href:c.value=d;return}catch(d){c.issues.push({code:"invalid_format",format:"url",input:c.value,inst:a,continue:!b.abort})}}}),aB=d("$ZodEmoji",(a,b)=>{b.pattern??(b.pattern=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),aw.init(a,b)}),aC=d("$ZodNanoID",(a,b)=>{b.pattern??(b.pattern=m),aw.init(a,b)}),aD=d("$ZodCUID",(a,b)=>{b.pattern??(b.pattern=h),aw.init(a,b)}),aE=d("$ZodCUID2",(a,b)=>{b.pattern??(b.pattern=i),aw.init(a,b)}),aF=d("$ZodULID",(a,b)=>{b.pattern??(b.pattern=j),aw.init(a,b)}),aG=d("$ZodXID",(a,b)=>{b.pattern??(b.pattern=k),aw.init(a,b)}),aH=d("$ZodKSUID",(a,b)=>{b.pattern??(b.pattern=l),aw.init(a,b)}),aI=d("$ZodISODateTime",(a,b)=>{b.pattern??(b.pattern=function(a){let b=B({precision:a.precision}),c=["Z"];a.local&&c.push(""),a.offset&&c.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");let d=`${b}(?:${c.join("|")})`;return RegExp(`^${z}T(?:${d})$`)}(b)),aw.init(a,b)}),aJ=d("$ZodISODate",(a,b)=>{b.pattern??(b.pattern=A),aw.init(a,b)}),aK=d("$ZodISOTime",(a,b)=>{b.pattern??(b.pattern=RegExp(`^${B(b)}$`)),aw.init(a,b)}),aL=d("$ZodISODuration",(a,b)=>{b.pattern??(b.pattern=n),aw.init(a,b)}),aM=d("$ZodIPv4",(a,b)=>{b.pattern??(b.pattern=r),aw.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv4"})}),aN=d("$ZodIPv6",(a,b)=>{b.pattern??(b.pattern=s),aw.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv6"}),a._zod.check=c=>{try{new URL(`http://[${c.value}]`)}catch{c.issues.push({code:"invalid_format",format:"ipv6",input:c.value,inst:a,continue:!b.abort})}}}),aO=d("$ZodCIDRv4",(a,b)=>{b.pattern??(b.pattern=t),aw.init(a,b)}),aP=d("$ZodCIDRv6",(a,b)=>{b.pattern??(b.pattern=u),aw.init(a,b),a._zod.check=c=>{let[d,e]=c.value.split("/");try{if(!e)throw Error();let a=Number(e);if(`${a}`!==e||a<0||a>128)throw Error();new URL(`http://[${d}]`)}catch{c.issues.push({code:"invalid_format",format:"cidrv6",input:c.value,inst:a,continue:!b.abort})}}});function aQ(a){if(""===a)return!0;if(a.length%4!=0)return!1;try{return atob(a),!0}catch{return!1}}let aR=d("$ZodBase64",(a,b)=>{b.pattern??(b.pattern=v),aw.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64"}),a._zod.check=c=>{aQ(c.value)||c.issues.push({code:"invalid_format",format:"base64",input:c.value,inst:a,continue:!b.abort})}}),aS=d("$ZodBase64URL",(a,b)=>{b.pattern??(b.pattern=w),aw.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64url"}),a._zod.check=c=>{!function(a){if(!w.test(a))return!1;let b=a.replace(/[-_]/g,a=>"-"===a?"+":"/");return aQ(b.padEnd(4*Math.ceil(b.length/4),"="))}(c.value)&&c.issues.push({code:"invalid_format",format:"base64url",input:c.value,inst:a,continue:!b.abort})}}),aT=d("$ZodE164",(a,b)=>{b.pattern??(b.pattern=y),aw.init(a,b)}),aU=d("$ZodJWT",(a,b)=>{aw.init(a,b),a._zod.check=c=>{!function(a,b=null){try{let c=a.split(".");if(3!==c.length)return!1;let[d]=c;if(!d)return!1;let e=JSON.parse(atob(d));if("typ"in e&&e?.typ!=="JWT"||!e.alg||b&&(!("alg"in e)||e.alg!==b))return!1;return!0}catch{return!1}}(c.value,b.alg)&&c.issues.push({code:"invalid_format",format:"jwt",input:c.value,inst:a,continue:!b.abort})}}),aV=d("$ZodBoolean",(a,b)=>{au.init(a,b),a._zod.pattern=C,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=!!c.value}catch(a){}let e=c.value;return"boolean"==typeof e||c.issues.push({expected:"boolean",code:"invalid_type",input:e,inst:a}),c}}),aW=d("$ZodUnknown",(a,b)=>{au.init(a,b),a._zod.parse=a=>a}),aX=d("$ZodNever",(a,b)=>{au.init(a,b),a._zod.parse=(b,c)=>(b.issues.push({expected:"never",code:"invalid_type",input:b.value,inst:a}),b)});function aY(a,b,c){a.issues.length&&b.issues.push(...W(c,a.issues)),b.value[c]=a.value}let aZ=d("$ZodArray",(a,b)=>{au.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!Array.isArray(e))return c.issues.push({expected:"array",code:"invalid_type",input:e,inst:a}),c;c.value=Array(e.length);let f=[];for(let a=0;a<e.length;a++){let g=e[a],h=b.element._zod.run({value:g,issues:[]},d);h instanceof Promise?f.push(h.then(b=>aY(b,c,a))):aY(h,c,a)}return f.length?Promise.all(f).then(()=>c):c}});function a$(a,b,c,d){a.issues.length&&b.issues.push(...W(c,a.issues)),void 0===a.value?c in d&&(b.value[c]=void 0):b.value[c]=a.value}let a_=d("$ZodObject",(a,b)=>{let c,d;au.init(a,b);let e=G(()=>{let a=Object.keys(b.shape);for(let c of a)if(!(b.shape[c]instanceof au))throw Error(`Invalid element at key "${c}": expected a Zod schema`);let c=function(a){return Object.keys(a).filter(b=>"optional"===a[b]._zod.optin&&"optional"===a[b]._zod.optout)}(b.shape);return{shape:b.shape,keys:a,keySet:new Set(a),numKeys:a.length,optionalKeys:new Set(c)}});J(a._zod,"propValues",()=>{let a=b.shape,c={};for(let b in a){let d=a[b]._zod;if(d.values)for(let a of(c[b]??(c[b]=new Set),d.values))c[b].add(a)}return c});let g=!f.jitless,h=g&&P.value,i=b.catchall;a._zod.parse=(f,j)=>{d??(d=e.value);let k=f.value;if(!O(k))return f.issues.push({expected:"object",code:"invalid_type",input:k,inst:a}),f;let l=[];if(g&&h&&j?.async===!1&&!0!==j.jitless)c||(c=(a=>{let b=new al(["shape","payload","ctx"]),c=e.value,d=a=>{let b=M(a);return`shape[${b}]._zod.run({ value: input[${b}], issues: [] }, ctx)`};b.write("const input = payload.value;");let f=Object.create(null),g=0;for(let a of c.keys)f[a]=`key_${g++}`;for(let a of(b.write("const newResult = {}"),c.keys)){let c=f[a],e=M(a);b.write(`const ${c} = ${d(a)};`),b.write(`
        if (${c}.issues.length) {
          payload.issues = payload.issues.concat(${c}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${e}, ...iss.path] : [${e}]
          })));
        }
        
        if (${c}.value === undefined) {
          if (${e} in input) {
            newResult[${e}] = undefined;
          }
        } else {
          newResult[${e}] = ${c}.value;
        }
      `)}b.write("payload.value = newResult;"),b.write("return payload;");let h=b.compile();return(b,c)=>h(a,b,c)})(b.shape)),f=c(f,j);else{f.value={};let a=d.shape;for(let b of d.keys){let c=a[b]._zod.run({value:k[b],issues:[]},j);c instanceof Promise?l.push(c.then(a=>a$(a,f,b,k))):a$(c,f,b,k)}}if(!i)return l.length?Promise.all(l).then(()=>f):f;let m=[],n=d.keySet,o=i._zod,p=o.def.type;for(let a of Object.keys(k)){if(n.has(a))continue;if("never"===p){m.push(a);continue}let b=o.run({value:k[a],issues:[]},j);b instanceof Promise?l.push(b.then(b=>a$(b,f,a,k))):a$(b,f,a,k)}return(m.length&&f.issues.push({code:"unrecognized_keys",keys:m,input:k,inst:a}),l.length)?Promise.all(l).then(()=>f):f}});function a0(a,b,c,d){for(let c of a)if(0===c.issues.length)return b.value=c.value,b;let e=a.filter(a=>!V(a));return 1===e.length?(b.value=e[0].value,e[0]):(b.issues.push({code:"invalid_union",input:b.value,inst:c,errors:a.map(a=>a.issues.map(a=>Y(a,d,g())))}),b)}let a1=d("$ZodUnion",(a,b)=>{au.init(a,b),J(a._zod,"optin",()=>b.options.some(a=>"optional"===a._zod.optin)?"optional":void 0),J(a._zod,"optout",()=>b.options.some(a=>"optional"===a._zod.optout)?"optional":void 0),J(a._zod,"values",()=>{if(b.options.every(a=>a._zod.values))return new Set(b.options.flatMap(a=>Array.from(a._zod.values)))}),J(a._zod,"pattern",()=>{if(b.options.every(a=>a._zod.pattern)){let a=b.options.map(a=>a._zod.pattern);return RegExp(`^(${a.map(a=>H(a.source)).join("|")})$`)}});let c=1===b.options.length,d=b.options[0]._zod.run;a._zod.parse=(e,f)=>{if(c)return d(e,f);let g=!1,h=[];for(let a of b.options){let b=a._zod.run({value:e.value,issues:[]},f);if(b instanceof Promise)h.push(b),g=!0;else{if(0===b.issues.length)return b;h.push(b)}}return g?Promise.all(h).then(b=>a0(b,e,a,f)):a0(h,e,a,f)}}),a2=d("$ZodIntersection",(a,b)=>{au.init(a,b),a._zod.parse=(a,c)=>{let d=a.value,e=b.left._zod.run({value:d,issues:[]},c),f=b.right._zod.run({value:d,issues:[]},c);return e instanceof Promise||f instanceof Promise?Promise.all([e,f]).then(([b,c])=>a3(a,b,c)):a3(a,e,f)}});function a3(a,b,c){if(b.issues.length&&a.issues.push(...b.issues),c.issues.length&&a.issues.push(...c.issues),V(a))return a;let d=function a(b,c){if(b===c||b instanceof Date&&c instanceof Date&&+b==+c)return{valid:!0,data:b};if(Q(b)&&Q(c)){let d=Object.keys(c),e=Object.keys(b).filter(a=>-1!==d.indexOf(a)),f={...b,...c};for(let d of e){let e=a(b[d],c[d]);if(!e.valid)return{valid:!1,mergeErrorPath:[d,...e.mergeErrorPath]};f[d]=e.data}return{valid:!0,data:f}}if(Array.isArray(b)&&Array.isArray(c)){if(b.length!==c.length)return{valid:!1,mergeErrorPath:[]};let d=[];for(let e=0;e<b.length;e++){let f=a(b[e],c[e]);if(!f.valid)return{valid:!1,mergeErrorPath:[e,...f.mergeErrorPath]};d.push(f.data)}return{valid:!0,data:d}}return{valid:!1,mergeErrorPath:[]}}(b.value,c.value);if(!d.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(d.mergeErrorPath)}`);return a.value=d.data,a}let a4=d("$ZodEnum",(a,b)=>{au.init(a,b);let c=function(a){let b=Object.values(a).filter(a=>"number"==typeof a);return Object.entries(a).filter(([a,c])=>-1===b.indexOf(+a)).map(([a,b])=>b)}(b.entries),d=new Set(c);a._zod.values=d,a._zod.pattern=RegExp(`^(${c.filter(a=>R.has(typeof a)).map(a=>"string"==typeof a?S(a):a.toString()).join("|")})$`),a._zod.parse=(b,e)=>{let f=b.value;return d.has(f)||b.issues.push({code:"invalid_value",values:c,input:f,inst:a}),b}}),a5=d("$ZodTransform",(a,b)=>{au.init(a,b),a._zod.parse=(a,c)=>{let d=b.transform(a.value,a);if(c.async)return(d instanceof Promise?d:Promise.resolve(d)).then(b=>(a.value=b,a));if(d instanceof Promise)throw new e;return a.value=d,a}});function a6(a,b){return a.issues.length&&void 0===b?{issues:[],value:void 0}:a}let a7=d("$ZodOptional",(a,b)=>{au.init(a,b),a._zod.optin="optional",a._zod.optout="optional",J(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,void 0]):void 0),J(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${H(a.source)})?$`):void 0}),a._zod.parse=(a,c)=>{if("optional"===b.innerType._zod.optin){let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(b=>a6(b,a.value)):a6(d,a.value)}return void 0===a.value?a:b.innerType._zod.run(a,c)}}),a8=d("$ZodNullable",(a,b)=>{au.init(a,b),J(a._zod,"optin",()=>b.innerType._zod.optin),J(a._zod,"optout",()=>b.innerType._zod.optout),J(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${H(a.source)}|null)$`):void 0}),J(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,null]):void 0),a._zod.parse=(a,c)=>null===a.value?a:b.innerType._zod.run(a,c)}),a9=d("$ZodDefault",(a,b)=>{au.init(a,b),a._zod.optin="optional",J(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{if(void 0===a.value)return a.value=b.defaultValue,a;let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(a=>ba(a,b)):ba(d,b)}});function ba(a,b){return void 0===a.value&&(a.value=b.defaultValue),a}let bb=d("$ZodPrefault",(a,b)=>{au.init(a,b),a._zod.optin="optional",J(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>(void 0===a.value&&(a.value=b.defaultValue),b.innerType._zod.run(a,c))}),bc=d("$ZodNonOptional",(a,b)=>{au.init(a,b),J(a._zod,"values",()=>{let a=b.innerType._zod.values;return a?new Set([...a].filter(a=>void 0!==a)):void 0}),a._zod.parse=(c,d)=>{let e=b.innerType._zod.run(c,d);return e instanceof Promise?e.then(b=>bd(b,a)):bd(e,a)}});function bd(a,b){return a.issues.length||void 0!==a.value||a.issues.push({code:"invalid_type",expected:"nonoptional",input:a.value,inst:b}),a}let be=d("$ZodCatch",(a,b)=>{au.init(a,b),J(a._zod,"optin",()=>b.innerType._zod.optin),J(a._zod,"optout",()=>b.innerType._zod.optout),J(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(d=>(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>Y(a,c,g()))},input:a.value}),a.issues=[]),a)):(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>Y(a,c,g()))},input:a.value}),a.issues=[]),a)}}),bf=d("$ZodPipe",(a,b)=>{au.init(a,b),J(a._zod,"values",()=>b.in._zod.values),J(a._zod,"optin",()=>b.in._zod.optin),J(a._zod,"optout",()=>b.out._zod.optout),J(a._zod,"propValues",()=>b.in._zod.propValues),a._zod.parse=(a,c)=>{let d=b.in._zod.run(a,c);return d instanceof Promise?d.then(a=>bg(a,b,c)):bg(d,b,c)}});function bg(a,b,c){return a.issues.length?a:b.out._zod.run({value:a.value,issues:a.issues},c)}let bh=d("$ZodReadonly",(a,b)=>{au.init(a,b),J(a._zod,"propValues",()=>b.innerType._zod.propValues),J(a._zod,"values",()=>b.innerType._zod.values),J(a._zod,"optin",()=>b.innerType._zod.optin),J(a._zod,"optout",()=>b.innerType._zod.optout),a._zod.parse=(a,c)=>{let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(bi):bi(d)}});function bi(a){return a.value=Object.freeze(a.value),a}let bj=d("$ZodCustom",(a,b)=>{_.init(a,b),au.init(a,b),a._zod.parse=(a,b)=>a,a._zod.check=c=>{let d=c.value,e=b.fn(d);if(e instanceof Promise)return e.then(b=>bk(b,c,d,a));bk(e,c,d,a)}});function bk(a,b,c,d){if(!a){let a={code:"custom",input:c,inst:d,path:[...d._zod.def.path??[]],continue:!d._zod.def.abort};d._zod.def.params&&(a.params=d._zod.def.params),b.issues.push($(a))}}Symbol("ZodOutput"),Symbol("ZodInput");class bl{constructor(){this._map=new Map,this._idmap=new Map}add(a,...b){let c=b[0];if(this._map.set(a,c),c&&"object"==typeof c&&"id"in c){if(this._idmap.has(c.id))throw Error(`ID ${c.id} already exists in the registry`);this._idmap.set(c.id,a)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(a){let b=this._map.get(a);return b&&"object"==typeof b&&"id"in b&&this._idmap.delete(b.id),this._map.delete(a),this}get(a){let b=a._zod.parent;if(b){let c={...this.get(b)??{}};delete c.id;let d={...c,...this._map.get(a)};return Object.keys(d).length?d:void 0}return this._map.get(a)}has(a){return this._map.has(a)}}let bm=new bl;function bn(a,b){return new a({type:"string",format:"guid",check:"string_format",abort:!1,...U(b)})}function bo(a,b){return new aa({check:"max_length",...U(b),maximum:a})}function bp(a,b){return new ab({check:"min_length",...U(b),minimum:a})}function bq(a,b){return new ac({check:"length_equals",...U(b),length:a})}function br(a){return new ak({check:"overwrite",tx:a})}let bs=d("ZodISODateTime",(a,b)=>{aI.init(a,b),bG.init(a,b)}),bt=d("ZodISODate",(a,b)=>{aJ.init(a,b),bG.init(a,b)}),bu=d("ZodISOTime",(a,b)=>{aK.init(a,b),bG.init(a,b)}),bv=d("ZodISODuration",(a,b)=>{aL.init(a,b),bG.init(a,b)}),bw=(a,b)=>{an.init(a,b),a.name="ZodError",Object.defineProperties(a,{format:{value:b=>(function(a,b){let c=b||function(a){return a.message},d={_errors:[]},e=a=>{for(let b of a.issues)if("invalid_union"===b.code&&b.errors.length)b.errors.map(a=>e({issues:a}));else if("invalid_key"===b.code)e({issues:b.issues});else if("invalid_element"===b.code)e({issues:b.issues});else if(0===b.path.length)d._errors.push(c(b));else{let a=d,e=0;for(;e<b.path.length;){let d=b.path[e];e===b.path.length-1?(a[d]=a[d]||{_errors:[]},a[d]._errors.push(c(b))):a[d]=a[d]||{_errors:[]},a=a[d],e++}}};return e(a),d})(a,b)},flatten:{value:b=>(function(a,b=a=>a.message){let c={},d=[];for(let e of a.issues)e.path.length>0?(c[e.path[0]]=c[e.path[0]]||[],c[e.path[0]].push(b(e))):d.push(b(e));return{formErrors:d,fieldErrors:c}})(a,b)},addIssue:{value:b=>{a.issues.push(b),a.message=JSON.stringify(a.issues,F,2)}},addIssues:{value:b=>{a.issues.push(...b),a.message=JSON.stringify(a.issues,F,2)}},isEmpty:{get:()=>0===a.issues.length}})};d("ZodError",bw);let bx=d("ZodError",bw,{Parent:Error}),by=(a,b,c,d)=>{let f=c?Object.assign(c,{async:!1}):{async:!1},h=a._zod.run({value:b,issues:[]},f);if(h instanceof Promise)throw new e;if(h.issues.length){let a=new(d?.Err??bx)(h.issues.map(a=>Y(a,f,g())));throw N(a,d?.callee),a}return h.value},bz=async(a,b,c,d)=>{let e=c?Object.assign(c,{async:!0}):{async:!0},f=a._zod.run({value:b,issues:[]},e);if(f instanceof Promise&&(f=await f),f.issues.length){let a=new(d?.Err??bx)(f.issues.map(a=>Y(a,e,g())));throw N(a,d?.callee),a}return f.value},bA=ap(bx),bB=ar(bx),bC=d("ZodType",(a,b)=>(au.init(a,b),a.def=b,Object.defineProperty(a,"_def",{value:b}),a.check=(...c)=>a.clone({...b,checks:[...b.checks??[],...c.map(a=>"function"==typeof a?{_zod:{check:a,def:{check:"custom"},onattach:[]}}:a)]}),a.clone=(b,c)=>T(a,b,c),a.brand=()=>a,a.register=(b,c)=>(b.add(a,c),a),a.parse=(b,c)=>by(a,b,c,{callee:a.parse}),a.safeParse=(b,c)=>bA(a,b,c),a.parseAsync=async(b,c)=>bz(a,b,c,{callee:a.parseAsync}),a.safeParseAsync=async(b,c)=>bB(a,b,c),a.spa=a.safeParseAsync,a.refine=(b,c)=>a.check(function(a,b={}){return new cn({type:"custom",check:"custom",fn:a,...U(b)})}(b,c)),a.superRefine=b=>a.check(function(a){let b=function(a,b){let c=new _({check:"custom",...U(void 0)});return c._zod.check=a,c}(c=>(c.addIssue=a=>{"string"==typeof a?c.issues.push($(a,c.value,b._zod.def)):(a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=c.value),a.inst??(a.inst=b),a.continue??(a.continue=!b._zod.def.abort),c.issues.push($(a)))},a(c.value,c)));return b}(b)),a.overwrite=b=>a.check(br(b)),a.optional=()=>cd(a),a.nullable=()=>cf(a),a.nullish=()=>cd(cf(a)),a.nonoptional=b=>new ci({type:"nonoptional",innerType:a,...U(b)}),a.array=()=>b4(a),a.or=b=>new b7({type:"union",options:[a,b],...U(void 0)}),a.and=b=>new b8({type:"intersection",left:a,right:b}),a.transform=b=>cl(a,new cb({type:"transform",transform:b})),a.default=b=>(function(a,b){return new cg({type:"default",innerType:a,get defaultValue(){return"function"==typeof b?b():b}})})(a,b),a.prefault=b=>(function(a,b){return new ch({type:"prefault",innerType:a,get defaultValue(){return"function"==typeof b?b():b}})})(a,b),a.catch=b=>(function(a,b){return new cj({type:"catch",innerType:a,catchValue:"function"==typeof b?b:()=>b})})(a,b),a.pipe=b=>cl(a,b),a.readonly=()=>new cm({type:"readonly",innerType:a}),a.describe=b=>{let c=a.clone();return bm.add(c,{description:b}),c},Object.defineProperty(a,"description",{get:()=>bm.get(a)?.description,configurable:!0}),a.meta=(...b)=>{if(0===b.length)return bm.get(a);let c=a.clone();return bm.add(c,b[0]),c},a.isOptional=()=>a.safeParse(void 0).success,a.isNullable=()=>a.safeParse(null).success,a)),bD=d("_ZodString",(a,b)=>{av.init(a,b),bC.init(a,b);let c=a._zod.bag;a.format=c.format??null,a.minLength=c.minimum??null,a.maxLength=c.maximum??null,a.regex=(...b)=>a.check(function(a,b){return new ae({check:"string_format",format:"regex",...U(b),pattern:a})}(...b)),a.includes=(...b)=>a.check(function(a,b){return new ah({check:"string_format",format:"includes",...U(b),includes:a})}(...b)),a.startsWith=(...b)=>a.check(function(a,b){return new ai({check:"string_format",format:"starts_with",...U(b),prefix:a})}(...b)),a.endsWith=(...b)=>a.check(function(a,b){return new aj({check:"string_format",format:"ends_with",...U(b),suffix:a})}(...b)),a.min=(...b)=>a.check(bp(...b)),a.max=(...b)=>a.check(bo(...b)),a.length=(...b)=>a.check(bq(...b)),a.nonempty=(...b)=>a.check(bp(1,...b)),a.lowercase=b=>a.check(new af({check:"string_format",format:"lowercase",...U(b)})),a.uppercase=b=>a.check(new ag({check:"string_format",format:"uppercase",...U(b)})),a.trim=()=>a.check(br(a=>a.trim())),a.normalize=(...b)=>a.check(function(a){return br(b=>b.normalize(a))}(...b)),a.toLowerCase=()=>a.check(br(a=>a.toLowerCase())),a.toUpperCase=()=>a.check(br(a=>a.toUpperCase()))}),bE=d("ZodString",(a,b)=>{av.init(a,b),bD.init(a,b),a.email=b=>a.check(new bH({type:"string",format:"email",check:"string_format",abort:!1,...U(b)})),a.url=b=>a.check(new bK({type:"string",format:"url",check:"string_format",abort:!1,...U(b)})),a.jwt=b=>a.check(new bZ({type:"string",format:"jwt",check:"string_format",abort:!1,...U(b)})),a.emoji=b=>a.check(new bL({type:"string",format:"emoji",check:"string_format",abort:!1,...U(b)})),a.guid=b=>a.check(bn(bI,b)),a.uuid=b=>a.check(new bJ({type:"string",format:"uuid",check:"string_format",abort:!1,...U(b)})),a.uuidv4=b=>a.check(new bJ({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...U(b)})),a.uuidv6=b=>a.check(new bJ({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...U(b)})),a.uuidv7=b=>a.check(new bJ({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...U(b)})),a.nanoid=b=>a.check(new bM({type:"string",format:"nanoid",check:"string_format",abort:!1,...U(b)})),a.guid=b=>a.check(bn(bI,b)),a.cuid=b=>a.check(new bN({type:"string",format:"cuid",check:"string_format",abort:!1,...U(b)})),a.cuid2=b=>a.check(new bO({type:"string",format:"cuid2",check:"string_format",abort:!1,...U(b)})),a.ulid=b=>a.check(new bP({type:"string",format:"ulid",check:"string_format",abort:!1,...U(b)})),a.base64=b=>a.check(new bW({type:"string",format:"base64",check:"string_format",abort:!1,...U(b)})),a.base64url=b=>a.check(new bX({type:"string",format:"base64url",check:"string_format",abort:!1,...U(b)})),a.xid=b=>a.check(new bQ({type:"string",format:"xid",check:"string_format",abort:!1,...U(b)})),a.ksuid=b=>a.check(new bR({type:"string",format:"ksuid",check:"string_format",abort:!1,...U(b)})),a.ipv4=b=>a.check(new bS({type:"string",format:"ipv4",check:"string_format",abort:!1,...U(b)})),a.ipv6=b=>a.check(new bT({type:"string",format:"ipv6",check:"string_format",abort:!1,...U(b)})),a.cidrv4=b=>a.check(new bU({type:"string",format:"cidrv4",check:"string_format",abort:!1,...U(b)})),a.cidrv6=b=>a.check(new bV({type:"string",format:"cidrv6",check:"string_format",abort:!1,...U(b)})),a.e164=b=>a.check(new bY({type:"string",format:"e164",check:"string_format",abort:!1,...U(b)})),a.datetime=b=>a.check(new bs({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...U(b)})),a.date=b=>a.check(new bt({type:"string",format:"date",check:"string_format",...U(b)})),a.time=b=>a.check(new bu({type:"string",format:"time",check:"string_format",precision:null,...U(b)})),a.duration=b=>a.check(new bv({type:"string",format:"duration",check:"string_format",...U(b)}))});function bF(a){return new bE({type:"string",...U(a)})}let bG=d("ZodStringFormat",(a,b)=>{aw.init(a,b),bD.init(a,b)}),bH=d("ZodEmail",(a,b)=>{az.init(a,b),bG.init(a,b)}),bI=d("ZodGUID",(a,b)=>{ax.init(a,b),bG.init(a,b)}),bJ=d("ZodUUID",(a,b)=>{ay.init(a,b),bG.init(a,b)}),bK=d("ZodURL",(a,b)=>{aA.init(a,b),bG.init(a,b)}),bL=d("ZodEmoji",(a,b)=>{aB.init(a,b),bG.init(a,b)}),bM=d("ZodNanoID",(a,b)=>{aC.init(a,b),bG.init(a,b)}),bN=d("ZodCUID",(a,b)=>{aD.init(a,b),bG.init(a,b)}),bO=d("ZodCUID2",(a,b)=>{aE.init(a,b),bG.init(a,b)}),bP=d("ZodULID",(a,b)=>{aF.init(a,b),bG.init(a,b)}),bQ=d("ZodXID",(a,b)=>{aG.init(a,b),bG.init(a,b)}),bR=d("ZodKSUID",(a,b)=>{aH.init(a,b),bG.init(a,b)}),bS=d("ZodIPv4",(a,b)=>{aM.init(a,b),bG.init(a,b)}),bT=d("ZodIPv6",(a,b)=>{aN.init(a,b),bG.init(a,b)}),bU=d("ZodCIDRv4",(a,b)=>{aO.init(a,b),bG.init(a,b)}),bV=d("ZodCIDRv6",(a,b)=>{aP.init(a,b),bG.init(a,b)}),bW=d("ZodBase64",(a,b)=>{aR.init(a,b),bG.init(a,b)}),bX=d("ZodBase64URL",(a,b)=>{aS.init(a,b),bG.init(a,b)}),bY=d("ZodE164",(a,b)=>{aT.init(a,b),bG.init(a,b)}),bZ=d("ZodJWT",(a,b)=>{aU.init(a,b),bG.init(a,b)}),b$=d("ZodBoolean",(a,b)=>{aV.init(a,b),bC.init(a,b)});function b_(a){return new b$({type:"boolean",...U(a)})}let b0=d("ZodUnknown",(a,b)=>{aW.init(a,b),bC.init(a,b)});function b1(){return new b0({type:"unknown"})}let b2=d("ZodNever",(a,b)=>{aX.init(a,b),bC.init(a,b)}),b3=d("ZodArray",(a,b)=>{aZ.init(a,b),bC.init(a,b),a.element=b.element,a.min=(b,c)=>a.check(bp(b,c)),a.nonempty=b=>a.check(bp(1,b)),a.max=(b,c)=>a.check(bo(b,c)),a.length=(b,c)=>a.check(bq(b,c)),a.unwrap=()=>a.element});function b4(a,b){return new b3({type:"array",element:a,...U(b)})}let b5=d("ZodObject",(a,b)=>{a_.init(a,b),bC.init(a,b),J(a,"shape",()=>b.shape),a.keyof=()=>ca(Object.keys(a._zod.def.shape)),a.catchall=b=>a.clone({...a._zod.def,catchall:b}),a.passthrough=()=>a.clone({...a._zod.def,catchall:b1()}),a.loose=()=>a.clone({...a._zod.def,catchall:b1()}),a.strict=()=>a.clone({...a._zod.def,catchall:new b2({type:"never",...U(void 0)})}),a.strip=()=>a.clone({...a._zod.def,catchall:void 0}),a.extend=b=>(function(a,b){if(!Q(b))throw Error("Invalid input to extend: expected a plain object");let c=L(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b};return K(this,"shape",c),c},checks:[]});return T(a,c)})(a,b),a.merge=b=>(function(a,b){let c=L(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b._zod.def.shape};return K(this,"shape",c),c},get catchall(){return b._zod.def.catchall},checks:[]});return T(a,c)})(a,b),a.pick=b=>(function(a,b){let c=a._zod.def,d=L(a._zod.def,{get shape(){let a={};for(let d in b){if(!(d in c.shape))throw Error(`Unrecognized key: "${d}"`);b[d]&&(a[d]=c.shape[d])}return K(this,"shape",a),a},checks:[]});return T(a,d)})(a,b),a.omit=b=>(function(a,b){let c=a._zod.def,d=L(a._zod.def,{get shape(){let d={...a._zod.def.shape};for(let a in b){if(!(a in c.shape))throw Error(`Unrecognized key: "${a}"`);b[a]&&delete d[a]}return K(this,"shape",d),d},checks:[]});return T(a,d)})(a,b),a.partial=(...b)=>(function(a,b,c){let d=L(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in d))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=a?new a({type:"optional",innerType:d[b]}):d[b])}else for(let b in d)e[b]=a?new a({type:"optional",innerType:d[b]}):d[b];return K(this,"shape",e),e},checks:[]});return T(b,d)})(cc,a,b[0]),a.required=(...b)=>(function(a,b,c){let d=L(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in e))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=new a({type:"nonoptional",innerType:d[b]}))}else for(let b in d)e[b]=new a({type:"nonoptional",innerType:d[b]});return K(this,"shape",e),e},checks:[]});return T(b,d)})(ci,a,b[0])});function b6(a,b){return new b5({type:"object",get shape(){return K(this,"shape",{...a}),this.shape},...U(b)})}let b7=d("ZodUnion",(a,b)=>{a1.init(a,b),bC.init(a,b),a.options=b.options}),b8=d("ZodIntersection",(a,b)=>{a2.init(a,b),bC.init(a,b)}),b9=d("ZodEnum",(a,b)=>{a4.init(a,b),bC.init(a,b),a.enum=b.entries,a.options=Object.values(b.entries);let c=new Set(Object.keys(b.entries));a.extract=(a,d)=>{let e={};for(let d of a)if(c.has(d))e[d]=b.entries[d];else throw Error(`Key ${d} not found in enum`);return new b9({...b,checks:[],...U(d),entries:e})},a.exclude=(a,d)=>{let e={...b.entries};for(let b of a)if(c.has(b))delete e[b];else throw Error(`Key ${b} not found in enum`);return new b9({...b,checks:[],...U(d),entries:e})}});function ca(a,b){return new b9({type:"enum",entries:Array.isArray(a)?Object.fromEntries(a.map(a=>[a,a])):a,...U(b)})}let cb=d("ZodTransform",(a,b)=>{a5.init(a,b),bC.init(a,b),a._zod.parse=(c,d)=>{c.addIssue=d=>{"string"==typeof d?c.issues.push($(d,c.value,b)):(d.fatal&&(d.continue=!1),d.code??(d.code="custom"),d.input??(d.input=c.value),d.inst??(d.inst=a),c.issues.push($(d)))};let e=b.transform(c.value,c);return e instanceof Promise?e.then(a=>(c.value=a,c)):(c.value=e,c)}}),cc=d("ZodOptional",(a,b)=>{a7.init(a,b),bC.init(a,b),a.unwrap=()=>a._zod.def.innerType});function cd(a){return new cc({type:"optional",innerType:a})}let ce=d("ZodNullable",(a,b)=>{a8.init(a,b),bC.init(a,b),a.unwrap=()=>a._zod.def.innerType});function cf(a){return new ce({type:"nullable",innerType:a})}let cg=d("ZodDefault",(a,b)=>{a9.init(a,b),bC.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeDefault=a.unwrap}),ch=d("ZodPrefault",(a,b)=>{bb.init(a,b),bC.init(a,b),a.unwrap=()=>a._zod.def.innerType}),ci=d("ZodNonOptional",(a,b)=>{bc.init(a,b),bC.init(a,b),a.unwrap=()=>a._zod.def.innerType}),cj=d("ZodCatch",(a,b)=>{be.init(a,b),bC.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeCatch=a.unwrap}),ck=d("ZodPipe",(a,b)=>{bf.init(a,b),bC.init(a,b),a.in=b.in,a.out=b.out});function cl(a,b){return new ck({type:"pipe",in:a,out:b})}let cm=d("ZodReadonly",(a,b)=>{bh.init(a,b),bC.init(a,b),a.unwrap=()=>a._zod.def.innerType}),cn=d("ZodCustom",(a,b)=>{bj.init(a,b),bC.init(a,b)})}};