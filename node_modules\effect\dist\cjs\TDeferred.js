"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.succeed = exports.poll = exports.make = exports.fail = exports.done = exports.await = exports.TDeferredTypeId = void 0;
var internal = _interopRequireWildcard(require("./internal/stm/tDeferred.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 * @category symbols
 */
const TDeferredTypeId = exports.TDeferredTypeId = internal.TDeferredTypeId;
const _await = exports.await = internal._await;
/**
 * @since 2.0.0
 * @category mutations
 */
const done = exports.done = internal.done;
/**
 * @since 2.0.0
 * @category mutations
 */
const fail = exports.fail = internal.fail;
/**
 * @since 2.0.0
 * @category constructors
 */
const make = exports.make = internal.make;
/**
 * @since 2.0.0
 * @category getters
 */
const poll = exports.poll = internal.poll;
/**
 * @since 2.0.0
 * @category mutations
 */
const succeed = exports.succeed = internal.succeed;
//# sourceMappingURL=TDeferred.js.map