(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[287],{251:(e,r,t)=>{Promise.resolve().then(t.bind(t,9296))},285:(e,r,t)=>{"use strict";t.d(r,{$:()=>i});var s=t(5155),a=t(2115),l=t(2085),n=t(9434);let d=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=a.forwardRef((e,r)=>{let{className:t,variant:a,size:l,asChild:i=!1,...c}=e;return(0,s.jsx)("button",{className:(0,n.cn)(d({variant:a,size:l,className:t})),ref:r,...c})});i.displayName="Button"},646:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6126:(e,r,t)=>{"use strict";t.d(r,{E:()=>d});var s=t(5155);t(2115);var a=t(2085),l=t(9434);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function d(e){let{className:r,variant:t,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:t}),r),...a})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>d});var s=t(5155),a=t(2115),l=t(9434);let n=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});n.displayName="Card";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});d.displayName="CardHeader";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});i.displayName="CardTitle";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...a})});o.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},9296:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(5155),a=t(2115),l=t(285),n=t(6695),d=t(6126),i=t(9946);let c=(0,i.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var o=t(9869),m=t(646);let u=(0,i.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),x=(0,i.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);function h(){let[e,r]=(0,a.useState)(null),[t,i]=(0,a.useState)(!1),[h,p]=(0,a.useState)(null),[f,g]=(0,a.useState)(!0),v=(0,a.useRef)(null),j=async()=>{if(e){i(!0);try{let r=new FormData;r.append("file",e),r.append("skipDuplicates",f.toString());let t=await fetch("/api/leads/bulk-import",{method:"POST",body:r}),s=await t.json();p(s)}catch(e){console.error("Import error:",e),p({success:!1,imported:0,errors:[{row:0,data:{},error:"Failed to upload file"}],duplicates:[]})}finally{i(!1)}}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Import Leads"}),(0,s.jsxs)(l.$,{variant:"outline",onClick:()=>{let e=new Blob(["name,email,phone,source\nJohn Doe,<EMAIL>,+1234567890,Website\nJane Smith,<EMAIL>,+0987654321,Referral\nBob Johnson,<EMAIL>,,Cold Call"],{type:"text/csv"}),r=window.URL.createObjectURL(e),t=document.createElement("a");t.href=r,t.download="sample-leads.csv",t.click(),window.URL.revokeObjectURL(r)},children:[(0,s.jsx)(c,{className:"mr-2 h-4 w-4"}),"Download Sample CSV"]})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"Import Instructions"}),(0,s.jsx)(n.BT,{children:"Upload a CSV or Excel file with your lead data"})]}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Required Columns:"}),(0,s.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"name"})," - Lead's full name (required)"]}),(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"email"})," - Email address (required)"]}),(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"phone"})," - Phone number (optional)"]}),(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"source"})," - Lead source (optional)"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Supported Formats:"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"CSV (.csv), Excel (.xlsx, .xls)"})]})]})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{children:"Upload File"})}),(0,s.jsxs)(n.Wu,{children:[(0,s.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer",onDrop:e=>{e.preventDefault();let t=e.dataTransfer.files[0];t&&(r(t),p(null))},onDragOver:e=>{e.preventDefault()},onClick:()=>{var e;return null==(e=v.current)?void 0:e.click()},children:[(0,s.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-400"}),e?(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-lg font-medium",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[(e.size/1024).toFixed(1)," KB"]})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-lg font-medium",children:"Drop your file here"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"or click to browse"})]})]}),(0,s.jsx)("input",{ref:v,type:"file",accept:".csv,.xlsx,.xls",onChange:e=>{var t;let s=null==(t=e.target.files)?void 0:t[0];s&&(r(s),p(null))},className:"hidden"}),e&&(0,s.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",id:"skipDuplicates",checked:f,onChange:e=>g(e.target.checked),className:"rounded"}),(0,s.jsx)("label",{htmlFor:"skipDuplicates",className:"text-sm",children:"Skip duplicate emails (recommended)"})]}),(0,s.jsx)(l.$,{onClick:j,disabled:t,className:"w-full",children:t?"Importing...":"Import Leads"})]})]})]}),h&&(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[h.success?(0,s.jsx)(m.A,{className:"h-5 w-5 text-green-600"}):(0,s.jsx)(u,{className:"h-5 w-5 text-red-600"}),"Import Results"]})}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:h.imported}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Successfully Imported"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:h.duplicates.length}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Duplicates Found"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:h.errors.length}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Errors"})]})]}),h.errors.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[(0,s.jsx)(x,{className:"h-4 w-4 text-red-600"}),"Errors"]}),(0,s.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:h.errors.map((e,r)=>(0,s.jsxs)("div",{className:"text-sm p-2 bg-red-50 rounded",children:[(0,s.jsxs)("strong",{children:["Row ",e.row,":"]})," ",e.error]},r))})]}),h.duplicates.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[(0,s.jsx)(x,{className:"h-4 w-4 text-yellow-600"}),"Duplicates ",f?"(Skipped)":"(Failed)"]}),(0,s.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:h.duplicates.map((e,r)=>(0,s.jsxs)("div",{className:"text-sm p-2 bg-yellow-50 rounded",children:[(0,s.jsxs)("strong",{children:["Row ",e.row,":"]})," ",e.email," already exists"]},r))})]}),h.success&&h.imported>0&&(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(d.E,{className:"bg-green-500 text-white",children:"Import completed successfully!"})})]})]})]})}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>l});var s=t(2596),a=t(9688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},9869:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9946:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(2115);let a=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},l=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,r)=>{let{color:t="currentColor",size:a=24,strokeWidth:d=2,absoluteStrokeWidth:i,className:c="",children:o,iconNode:m,...u}=e;return(0,s.createElement)("svg",{ref:r,...n,width:a,height:a,stroke:t,strokeWidth:i?24*Number(d)/Number(a):d,className:l("lucide",c),...!o&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(o)?o:[o]])}),i=(e,r)=>{let t=(0,s.forwardRef)((t,n)=>{let{className:i,...c}=t;return(0,s.createElement)(d,{ref:n,iconNode:r,className:l("lucide-".concat(a(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),i),...c})});return t.displayName=a(e),t}}},e=>{e.O(0,[3,441,964,358],()=>e(e.s=251)),_N_E=e.O()}]);