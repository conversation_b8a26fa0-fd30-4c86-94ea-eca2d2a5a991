"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.OP_WITH_STM_RUNTIME = exports.OP_TRACED = exports.OP_SYNC = exports.OP_SUCCEED = exports.OP_RETRY = exports.OP_PROVIDE = exports.OP_ON_SUCCESS = exports.OP_ON_RETRY = exports.OP_ON_FAILURE = exports.OP_INTERRUPT = exports.OP_FAIL = exports.OP_DIE = void 0;
/** @internal */
const OP_WITH_STM_RUNTIME = exports.OP_WITH_STM_RUNTIME = "WithSTMRuntime";
/** @internal */
const OP_ON_FAILURE = exports.OP_ON_FAILURE = "OnFailure";
/** @internal */
const OP_ON_RETRY = exports.OP_ON_RETRY = "OnRetry";
/** @internal */
const OP_ON_SUCCESS = exports.OP_ON_SUCCESS = "OnSuccess";
/** @internal */
const OP_PROVIDE = exports.OP_PROVIDE = "Provide";
/** @internal */
const OP_SYNC = exports.OP_SYNC = "Sync";
/** @internal */
const OP_SUCCEED = exports.OP_SUCCEED = "Succeed";
/** @internal */
const OP_RETRY = exports.OP_RETRY = "Retry";
/** @internal */
const OP_FAIL = exports.OP_FAIL = "Fail";
/** @internal */
const OP_DIE = exports.OP_DIE = "Die";
/** @internal */
const OP_INTERRUPT = exports.OP_INTERRUPT = "Interrupt";
/** @internal */
const OP_TRACED = exports.OP_TRACED = "Traced";
//# sourceMappingURL=stm.js.map