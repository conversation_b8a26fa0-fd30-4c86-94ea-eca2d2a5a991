(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[191],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(5155),s=r(2115),l=r(2085),n=r(9434);let i=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:r,variant:s,size:l,asChild:d=!1,...o}=e;return(0,a.jsx)("button",{className:(0,n.cn)(i({variant:s,size:l,className:r})),ref:t,...o})});d.displayName="Button"},343:(e,t,r)=>{"use strict";r.d(t,{EA:()=>d,Sd:()=>s,UY:()=>i,aE:()=>l,aq:()=>n});var a=r(8309);let s=a.Ik({name:a.Yj().min(2,"Name must be at least 2 characters"),email:a.Yj().email("Invalid email address"),password:a.Yj().min(6,"Password must be at least 6 characters")}),l=a.Ik({email:a.Yj().email("Invalid email address"),password:a.Yj().min(1,"Password is required")}),n=a.Ik({name:a.Yj().min(1,"Name is required"),email:a.Yj().email("Invalid email address"),phone:a.Yj().optional(),source:a.Yj().optional(),status:a.k5(["NEW","CONTACTED","INTERESTED","CONVERTED","LOST"]).default("NEW")});n.partial();let i=a.Ik({content:a.Yj().min(1,"Content is required"),leadId:a.Yj().min(1,"Lead ID is required")}),d=a.Ik({title:a.Yj().min(1,"Title is required"),description:a.Yj().optional(),dueDate:a.Yj().datetime("Invalid date format"),leadId:a.Yj().min(1,"Lead ID is required")});d.partial().extend({completed:a.zM().optional()}),a.Ik({leads:a.YO(n.omit({status:!0}))})},2525:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},4165:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>i,L3:()=>o,c7:()=>d,lG:()=>n});var a=r(5155),s=r(2115),l=r(9434);let n=e=>{let{open:t,onOpenChange:r,children:l}=e;return(s.useEffect(()=>{let e=e=>{"Escape"===e.key&&r&&r(!1)};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,r]),t)?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50",onClick:()=>null==r?void 0:r(!1)}),(0,a.jsx)("div",{className:"relative z-50 max-h-[90vh] overflow-auto",children:l})]}):null},i=s.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("bg-background p-6 shadow-lg rounded-lg border max-w-lg w-full mx-4",r),...n,children:s})});i.displayName="DialogContent";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",r),...s})});d.displayName="DialogHeader";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h2",{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",r),...s})});o.displayName="DialogTitle",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...s})}).displayName="DialogDescription"},4616:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4889:(e,t,r)=>{Promise.resolve().then(r.bind(r,9387))},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i});var a=r(5155),s=r(2115),l=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});n.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...s})});i.displayName="CardHeader";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});d.displayName="CardTitle";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...s})});o.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},7759:(e,t,r)=>{"use strict";r.d(t,{C5:()=>o,lR:()=>d,lV:()=>n,zB:()=>i});var a=r(5155),s=r(2115),l=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("form",{ref:t,className:(0,l.cn)("space-y-6",r),...s})});n.displayName="Form";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("space-y-2",r),...s})});i.displayName="FormField";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("label",{ref:t,className:(0,l.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",r),...s})});d.displayName="FormLabel";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm font-medium text-destructive",r),...s})});o.displayName="FormMessage"},9387:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(5155),s=r(2115),l=r(2177),n=r(221),i=r(343),d=r(285),o=r(9409),c=r(6695),m=r(4165),u=r(7759),f=r(4616);let h=(0,r(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var x=r(2525);function p(){let[e,t]=(0,s.useState)([]),[r,p]=(0,s.useState)([]),[g,v]=(0,s.useState)(!0),[j,y]=(0,s.useState)(!1),[N,b]=(0,s.useState)(""),w=(0,l.mN)({resolver:(0,n.u)(i.UY),defaultValues:{content:"",leadId:""}});(0,s.useEffect)(()=>{k(),C()},[]);let k=async()=>{try{let e=await fetch("/api/leads?limit=100");if(e.ok){let t=await e.json();p(t.leads)}}catch(e){console.error("Error fetching leads:",e)}},C=async()=>{try{let e=await fetch("/api/leads?limit=100");if(e.ok){let r=await e.json(),a=[];for(let e of r.leads)e.notes&&e.notes.length>0&&e.notes.forEach(t=>{a.push({...t,lead:{id:e.id,name:e.name,email:e.email}})});a.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime()),t(a)}}catch(e){console.error("Error fetching notes:",e)}finally{v(!1)}},E=async e=>{try{(await fetch("/api/notes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok&&(await C(),y(!1),w.reset())}catch(e){console.error("Error creating note:",e)}},R=async e=>{if(confirm("Are you sure you want to delete this note?"))try{(await fetch("/api/notes/".concat(e),{method:"DELETE"})).ok&&await C()}catch(e){console.error("Error deleting note:",e)}},A=N?e.filter(e=>e.leadId===N):e;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Notes"}),(0,a.jsxs)(d.$,{onClick:()=>y(!0),children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Add Note"]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Filter Notes"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-2",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Filter by Lead"}),(0,a.jsxs)(o.l,{value:N,onChange:e=>b(e.target.value),children:[(0,a.jsx)("option",{value:"",children:"All Leads"}),r.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.name," (",e.email,")"]},e.id))]})]})})})]}),(0,a.jsx)("div",{className:"space-y-4",children:g?(0,a.jsx)("p",{children:"Loading notes..."}):0===A.length?(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"text-center py-8",children:[(0,a.jsx)(h,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No notes found."})]})}):A.map(e=>{var t,r;return(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h3",{className:"font-semibold",children:(null==(t=e.lead)?void 0:t.name)||"Unknown Lead"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",(null==(r=e.lead)?void 0:r.email)||"No email",")"]})]}),(0,a.jsx)("p",{className:"text-gray-700 whitespace-pre-wrap",children:e.content}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:new Date(e.createdAt).toLocaleString()})]}),(0,a.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>R(e.id),children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})]})})},e.id)})}),(0,a.jsx)(m.lG,{open:j,onOpenChange:e=>{e||(y(!1),w.reset())},children:(0,a.jsxs)(m.Cf,{children:[(0,a.jsx)(m.c7,{children:(0,a.jsx)(m.L3,{children:"Add New Note"})}),(0,a.jsxs)(u.lV,{onSubmit:w.handleSubmit(E),children:[(0,a.jsxs)(u.zB,{children:[(0,a.jsx)(u.lR,{htmlFor:"leadId",children:"Lead"}),(0,a.jsxs)(o.l,{...w.register("leadId"),children:[(0,a.jsx)("option",{value:"",children:"Select a lead"}),r.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.name," (",e.email,")"]},e.id))]}),w.formState.errors.leadId&&(0,a.jsx)(u.C5,{children:w.formState.errors.leadId.message})]}),(0,a.jsxs)(u.zB,{children:[(0,a.jsx)(u.lR,{htmlFor:"content",children:"Note Content"}),(0,a.jsx)("textarea",{id:"content",rows:4,placeholder:"Enter your note...",className:"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",...w.register("content")}),w.formState.errors.content&&(0,a.jsx)(u.C5,{children:w.formState.errors.content.message})]}),(0,a.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,a.jsx)(d.$,{type:"submit",className:"flex-1",children:"Create Note"}),(0,a.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>{y(!1),w.reset()},children:"Cancel"})]})]})]})})]})}},9409:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});var a=r(5155),s=r(2115),l=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsx)("select",{className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n,children:s})});n.displayName="Select"},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var a=r(2596),s=r(9688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(2115);let s=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:d,className:o="",children:c,iconNode:m,...u}=e;return(0,a.createElement)("svg",{ref:t,...n,width:s,height:s,stroke:r,strokeWidth:d?24*Number(i)/Number(s):i,className:l("lucide",o),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),d=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:d,...o}=r;return(0,a.createElement)(i,{ref:n,iconNode:t,className:l("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...o})});return r.displayName=s(e),r}}},e=>{e.O(0,[3,889,441,964,358],()=>e(e.s=4889)),_N_E=e.O()}]);