{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n}\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    console.log('Verifying token:', {\n      tokenStart: token.substring(0, 20) + '...',\n      secretLength: JWT_SECRET.length,\n      secretStart: JWT_SECRET.substring(0, 10) + '...'\n    })\n    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload\n    console.log('Token verified successfully:', { userId: decoded.userId, email: decoded.email })\n    return decoded\n  } catch (error) {\n    console.error('Token verification failed:', error instanceof Error ? error.message : error)\n    return null\n  }\n}\n\nexport function getTokenFromRequest(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization')\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    console.log('Found token in Authorization header')\n    return authHeader.substring(7)\n  }\n\n  // Also check for token in cookies\n  const token = request.cookies.get('auth-token')?.value\n  console.log('Token from cookie:', token ? {\n    found: true,\n    length: token.length,\n    start: token.substring(0, 20) + '...'\n  } : { found: false })\n\n  return token || null\n}\n\nexport function getUserFromRequest(request: NextRequest): JWTPayload | null {\n  const token = getTokenFromRequest(request)\n  console.log('Auth check:', {\n    hasToken: !!token,\n    tokenLength: token?.length,\n    cookies: request.cookies.getAll().map(c => ({ name: c.name, hasValue: !!c.value }))\n  })\n\n  if (!token) return null\n\n  const user = verifyToken(token)\n  console.log('Token verification:', { isValid: !!user, userId: user?.userId })\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAOtC,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,QAAQ,GAAG,CAAC,oBAAoB;YAC9B,YAAY,MAAM,SAAS,CAAC,GAAG,MAAM;YACrC,cAAc,WAAW,MAAM;YAC/B,aAAa,WAAW,SAAS,CAAC,GAAG,MAAM;QAC7C;QACA,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,QAAQ,GAAG,CAAC,gCAAgC;YAAE,QAAQ,QAAQ,MAAM;YAAE,OAAO,QAAQ,KAAK;QAAC;QAC3F,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACrF,OAAO;IACT;AACF;AAEO,SAAS,oBAAoB,OAAoB;IACtD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,QAAQ,GAAG,CAAC;QACZ,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,kCAAkC;IAClC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,QAAQ,GAAG,CAAC,sBAAsB,QAAQ;QACxC,OAAO;QACP,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,SAAS,CAAC,GAAG,MAAM;IAClC,IAAI;QAAE,OAAO;IAAM;IAEnB,OAAO,SAAS;AAClB;AAEO,SAAS,mBAAmB,OAAoB;IACrD,MAAM,QAAQ,oBAAoB;IAClC,QAAQ,GAAG,CAAC,eAAe;QACzB,UAAU,CAAC,CAAC;QACZ,aAAa,OAAO;QACpB,SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,MAAM,EAAE,IAAI;gBAAE,UAAU,CAAC,CAAC,EAAE,KAAK;YAAC,CAAC;IACnF;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,OAAO,YAAY;IACzB,QAAQ,GAAG,CAAC,uBAAuB;QAAE,SAAS,CAAC,CAAC;QAAM,QAAQ,MAAM;IAAO;IAE3E,OAAO;AACT", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// Auth schemas\nexport const signUpSchema = z.object({\n  name: z.string().min(2, 'Name must be at least 2 characters'),\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n})\n\nexport const signInSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(1, 'Password is required'),\n})\n\n// Lead schemas\nexport const leadSchema = z.object({\n  name: z.string().min(1, 'Name is required'),\n  email: z.string().email('Invalid email address'),\n  phone: z.string().optional(),\n  source: z.string().optional(),\n  status: z.enum(['NEW', 'CONTACTED', 'INTERESTED', 'CONVERTED', 'LOST']).default('NEW'),\n})\n\nexport const updateLeadSchema = leadSchema.partial()\n\n// Note schemas\nexport const noteSchema = z.object({\n  content: z.string().min(1, 'Content is required'),\n  leadId: z.string().min(1, 'Lead ID is required'),\n})\n\n// Follow-up schemas\nexport const followUpSchema = z.object({\n  title: z.string().min(1, 'Title is required'),\n  description: z.string().optional(),\n  dueDate: z.string().datetime('Invalid date format'),\n  leadId: z.string().min(1, 'Lead ID is required'),\n})\n\nexport const updateFollowUpSchema = followUpSchema.partial().extend({\n  completed: z.boolean().optional(),\n})\n\n// Bulk import schema\nexport const bulkLeadSchema = z.object({\n  leads: z.array(leadSchema.omit({ status: true })),\n})\n\nexport type SignUpInput = z.infer<typeof signUpSchema>\nexport type SignInInput = z.infer<typeof signInSchema>\nexport type LeadInput = z.infer<typeof leadSchema>\nexport type UpdateLeadInput = z.infer<typeof updateLeadSchema>\nexport type NoteInput = z.infer<typeof noteSchema>\nexport type FollowUpInput = z.infer<typeof followUpSchema>\nexport type UpdateFollowUpInput = z.infer<typeof updateFollowUpSchema>\nexport type BulkLeadInput = z.infer<typeof bulkLeadSchema>\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGO,MAAM,eAAe,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,MAAM,eAAe,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAGO,MAAM,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,MAAM,+KAA<PERSON>,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,QAAQ,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,QAAQ,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;QAAa;QAAc;QAAa;KAAO,EAAE,OAAO,CAAC;AAClF;AAEO,MAAM,mBAAmB,WAAW,OAAO;AAG3C,MAAM,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,QAAQ,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC5B;AAGO,MAAM,iBAAiB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,QAAQ,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC5B;AAEO,MAAM,uBAAuB,eAAe,OAAO,GAAG,MAAM,CAAC;IAClE,WAAW,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;AACjC;AAGO,MAAM,iBAAiB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,+KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC;QAAE,QAAQ;IAAK;AAChD", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/app/api/leads/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { getUserFromRequest } from '@/lib/auth'\nimport { leadSchema } from '@/lib/validations'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const user = getUserFromRequest(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const status = searchParams.get('status')\n    const source = searchParams.get('source')\n    const sortBy = searchParams.get('sortBy') || 'createdAt'\n    const sortOrder = searchParams.get('sortOrder') || 'desc'\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const skip = (page - 1) * limit\n\n    // Build where clause\n    const where: any = {\n      userId: user.userId,\n    }\n\n    if (status) {\n      where.status = status\n    }\n\n    if (source) {\n      where.source = {\n        contains: source,\n        mode: 'insensitive',\n      }\n    }\n\n    // Get leads with pagination\n    const [leads, total] = await Promise.all([\n      prisma.lead.findMany({\n        where,\n        orderBy: {\n          [sortBy]: sortOrder,\n        },\n        skip,\n        take: limit,\n        include: {\n          notes: {\n            orderBy: { createdAt: 'desc' },\n            take: 3, // Include latest 3 notes\n          },\n          followUps: {\n            where: { completed: false },\n            orderBy: { dueDate: 'asc' },\n            take: 1, // Include next upcoming follow-up\n          },\n          _count: {\n            select: {\n              notes: true,\n              followUps: true,\n            },\n          },\n        },\n      }),\n      prisma.lead.count({ where }),\n    ])\n\n    return NextResponse.json({\n      leads,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit),\n      },\n    })\n  } catch (error) {\n    console.error('Get leads error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const user = getUserFromRequest(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const body = await request.json()\n    const validatedData = leadSchema.parse(body)\n\n    // Check if lead with same email already exists for this user\n    const existingLead = await prisma.lead.findFirst({\n      where: {\n        userId: user.userId,\n        email: validatedData.email,\n      },\n    })\n\n    if (existingLead) {\n      return NextResponse.json(\n        { error: 'Lead with this email already exists' },\n        { status: 400 }\n      )\n    }\n\n    const lead = await prisma.lead.create({\n      data: {\n        ...validatedData,\n        userId: user.userId,\n      },\n      include: {\n        notes: true,\n        followUps: true,\n        _count: {\n          select: {\n            notes: true,\n            followUps: true,\n          },\n        },\n      },\n    })\n\n    return NextResponse.json(lead, { status: 201 })\n  } catch (error) {\n    console.error('Create lead error:', error)\n\n    if (error instanceof Error && error.name === 'ZodError') {\n      return NextResponse.json(\n        { error: 'Invalid input data', details: error },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QAChC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QACnD,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,qBAAqB;QACrB,MAAM,QAAa;YACjB,QAAQ,KAAK,MAAM;QACrB;QAEA,IAAI,QAAQ;YACV,MAAM,MAAM,GAAG;QACjB;QAEA,IAAI,QAAQ;YACV,MAAM,MAAM,GAAG;gBACb,UAAU;gBACV,MAAM;YACR;QACF;QAEA,4BAA4B;QAC5B,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YACvC,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnB;gBACA,SAAS;oBACP,CAAC,OAAO,EAAE;gBACZ;gBACA;gBACA,MAAM;gBACN,SAAS;oBACP,OAAO;wBACL,SAAS;4BAAE,WAAW;wBAAO;wBAC7B,MAAM;oBACR;oBACA,WAAW;wBACT,OAAO;4BAAE,WAAW;wBAAM;wBAC1B,SAAS;4BAAE,SAAS;wBAAM;wBAC1B,MAAM;oBACR;oBACA,QAAQ;wBACN,QAAQ;4BACN,OAAO;4BACP,WAAW;wBACb;oBACF;gBACF;YACF;YACA,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC3B;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QAChC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,2HAAA,CAAA,aAAU,CAAC,KAAK,CAAC;QAEvC,6DAA6D;QAC7D,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/C,OAAO;gBACL,QAAQ,KAAK,MAAM;gBACnB,OAAO,cAAc,KAAK;YAC5B;QACF;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsC,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,GAAG,aAAa;gBAChB,QAAQ,KAAK,MAAM;YACrB;YACA,SAAS;gBACP,OAAO;gBACP,WAAW;gBACX,QAAQ;oBACN,QAAQ;wBACN,OAAO;wBACP,WAAW;oBACb;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QAEpC,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,YAAY;YACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAsB,SAAS;YAAM,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}