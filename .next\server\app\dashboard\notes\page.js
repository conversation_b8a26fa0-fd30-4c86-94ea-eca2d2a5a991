(()=>{var a={};a.id=191,a.ids=[191],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1471:(a,b,c)=>{Promise.resolve().then(c.bind(c,84770))},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(a,b,c)=>{"use strict";c.d(b,{l:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsx)("select",{className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c,children:b}));g.displayName="Select"},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21571:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["notes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,52413)),"C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\dashboard\\notes\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,63144)),"C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\dashboard\\notes\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/notes/page",pathname:"/dashboard/notes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/notes/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29465:(a,b,c)=>{"use strict";c.d(b,{EA:()=>i,Sd:()=>e,UY:()=>h,aE:()=>f,aq:()=>g});var d=c(37566);let e=d.Ik({name:d.Yj().min(2,"Name must be at least 2 characters"),email:d.Yj().email("Invalid email address"),password:d.Yj().min(6,"Password must be at least 6 characters")}),f=d.Ik({email:d.Yj().email("Invalid email address"),password:d.Yj().min(1,"Password is required")}),g=d.Ik({name:d.Yj().min(1,"Name is required"),email:d.Yj().email("Invalid email address"),phone:d.Yj().optional(),source:d.Yj().optional(),status:d.k5(["NEW","CONTACTED","INTERESTED","CONVERTED","LOST"]).default("NEW")});g.partial();let h=d.Ik({content:d.Yj().min(1,"Content is required"),leadId:d.Yj().min(1,"Lead ID is required")}),i=d.Ik({title:d.Yj().min(1,"Title is required"),description:d.Yj().optional(),dueDate:d.Yj().datetime("Invalid date format"),leadId:d.Yj().min(1,"Lead ID is required")});i.partial().extend({completed:d.zM().optional()}),d.Ik({leads:d.YO(g.omit({status:!0}))})},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},52413:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\project\\\\leads\\\\src\\\\app\\\\dashboard\\\\notes\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\dashboard\\notes\\page.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(a,b,c)=>{"use strict";c.d(b,{Cf:()=>h,L3:()=>j,c7:()=>i,lG:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=({open:a,onOpenChange:b,children:c})=>(e.useEffect(()=>{let c=a=>{"Escape"===a.key&&b&&b(!1)};return a&&(document.addEventListener("keydown",c),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",c),document.body.style.overflow="unset"}},[a,b]),a)?(0,d.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,d.jsx)("div",{className:"fixed inset-0 bg-black/50",onClick:()=>b?.(!1)}),(0,d.jsx)("div",{className:"relative z-50 max-h-[90vh] overflow-auto",children:c})]}):null,h=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsx)("div",{ref:e,className:(0,f.cn)("bg-background p-6 shadow-lg rounded-lg border max-w-lg w-full mx-4",a),...c,children:b}));h.displayName="DialogContent";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...b}));i.displayName="DialogHeader";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h2",{ref:c,className:(0,f.cn)("text-lg font-semibold leading-none tracking-tight",a),...b}));j.displayName="DialogTitle",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b})).displayName="DialogDescription"},71207:(a,b,c)=>{Promise.resolve().then(c.bind(c,52413))},71669:(a,b,c)=>{"use strict";c.d(b,{C5:()=>j,lR:()=>i,lV:()=>g,zB:()=>h});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("form",{ref:c,className:(0,f.cn)("space-y-6",a),...b}));g.displayName="Form";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("space-y-2",a),...b}));h.displayName="FormField";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("label",{ref:c,className:(0,f.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",a),...b}));i.displayName="FormLabel";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm font-medium text-destructive",a),...b}));j.displayName="FormMessage"},84770:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>q});var d=c(60687),e=c(43210),f=c(27605),g=c(63442),h=c(29465),i=c(29523),j=c(15079),k=c(44493),l=c(63503),m=c(71669),n=c(96474);let o=(0,c(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var p=c(88233);function q(){let[a,b]=(0,e.useState)([]),[c,q]=(0,e.useState)([]),[r,s]=(0,e.useState)(!0),[t,u]=(0,e.useState)(!1),[v,w]=(0,e.useState)(""),x=(0,f.mN)({resolver:(0,g.u)(h.UY),defaultValues:{content:"",leadId:""}}),y=async()=>{try{let a=await fetch("/api/leads?limit=100");if(a.ok){let c=await a.json(),d=[];for(let a of c.leads)a.notes&&a.notes.length>0&&a.notes.forEach(b=>{d.push({...b,lead:{id:a.id,name:a.name,email:a.email}})});d.sort((a,b)=>new Date(b.createdAt).getTime()-new Date(a.createdAt).getTime()),b(d)}}catch(a){console.error("Error fetching notes:",a)}finally{s(!1)}},z=async a=>{try{(await fetch("/api/notes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).ok&&(await y(),u(!1),x.reset())}catch(a){console.error("Error creating note:",a)}},A=async a=>{if(confirm("Are you sure you want to delete this note?"))try{(await fetch(`/api/notes/${a}`,{method:"DELETE"})).ok&&await y()}catch(a){console.error("Error deleting note:",a)}},B=v?a.filter(a=>a.leadId===v):a;return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Notes"}),(0,d.jsxs)(i.$,{onClick:()=>u(!0),children:[(0,d.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Add Note"]})]}),(0,d.jsxs)(k.Zp,{children:[(0,d.jsx)(k.aR,{children:(0,d.jsx)(k.ZB,{children:"Filter Notes"})}),(0,d.jsx)(k.Wu,{children:(0,d.jsx)("div",{className:"grid gap-4 md:grid-cols-2",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium",children:"Filter by Lead"}),(0,d.jsxs)(j.l,{value:v,onChange:a=>w(a.target.value),children:[(0,d.jsx)("option",{value:"",children:"All Leads"}),c.map(a=>(0,d.jsxs)("option",{value:a.id,children:[a.name," (",a.email,")"]},a.id))]})]})})})]}),(0,d.jsx)("div",{className:"space-y-4",children:r?(0,d.jsx)("p",{children:"Loading notes..."}):0===B.length?(0,d.jsx)(k.Zp,{children:(0,d.jsxs)(k.Wu,{className:"text-center py-8",children:[(0,d.jsx)(o,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"No notes found."})]})}):B.map(a=>(0,d.jsx)(k.Zp,{children:(0,d.jsx)(k.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("h3",{className:"font-semibold",children:a.lead?.name||"Unknown Lead"}),(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",a.lead?.email||"No email",")"]})]}),(0,d.jsx)("p",{className:"text-gray-700 whitespace-pre-wrap",children:a.content}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:new Date(a.createdAt).toLocaleString()})]}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>A(a.id),children:(0,d.jsx)(p.A,{className:"h-4 w-4"})})]})})},a.id))}),(0,d.jsx)(l.lG,{open:t,onOpenChange:a=>{a||(u(!1),x.reset())},children:(0,d.jsxs)(l.Cf,{children:[(0,d.jsx)(l.c7,{children:(0,d.jsx)(l.L3,{children:"Add New Note"})}),(0,d.jsxs)(m.lV,{onSubmit:x.handleSubmit(z),children:[(0,d.jsxs)(m.zB,{children:[(0,d.jsx)(m.lR,{htmlFor:"leadId",children:"Lead"}),(0,d.jsxs)(j.l,{...x.register("leadId"),children:[(0,d.jsx)("option",{value:"",children:"Select a lead"}),c.map(a=>(0,d.jsxs)("option",{value:a.id,children:[a.name," (",a.email,")"]},a.id))]}),x.formState.errors.leadId&&(0,d.jsx)(m.C5,{children:x.formState.errors.leadId.message})]}),(0,d.jsxs)(m.zB,{children:[(0,d.jsx)(m.lR,{htmlFor:"content",children:"Note Content"}),(0,d.jsx)("textarea",{id:"content",rows:4,placeholder:"Enter your note...",className:"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",...x.register("content")}),x.formState.errors.content&&(0,d.jsx)(m.C5,{children:x.formState.errors.content.message})]}),(0,d.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,d.jsx)(i.$,{type:"submit",className:"flex-1",children:"Create Note"}),(0,d.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>{u(!1),x.reset()},children:"Cancel"})]})]})]})})]})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88233:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,647,984,633,394],()=>b(b.s=21571));module.exports=c})();