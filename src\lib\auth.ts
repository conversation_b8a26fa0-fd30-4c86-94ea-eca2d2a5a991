import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { NextRequest } from 'next/server'

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'

export interface JWTPayload {
  userId: string
  email: string
}

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

export function generateToken(payload: JWTPayload): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })
}

export function verifyToken(token: string): JWTPayload | null {
  try {
    console.log('Verifying token:', {
      tokenStart: token.substring(0, 20) + '...',
      secretLength: JWT_SECRET.length,
      secretStart: JWT_SECRET.substring(0, 10) + '...'
    })
    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload
    console.log('Token verified successfully:', { userId: decoded.userId, email: decoded.email })
    return decoded
  } catch (error) {
    console.error('Token verification failed:', error instanceof Error ? error.message : error)
    return null
  }
}

export function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    console.log('Found token in Authorization header')
    return authHeader.substring(7)
  }

  // Also check for token in cookies
  const token = request.cookies.get('auth-token')?.value
  console.log('Token from cookie:', token ? {
    found: true,
    length: token.length,
    start: token.substring(0, 20) + '...'
  } : { found: false })

  return token || null
}

export function getUserFromRequest(request: NextRequest): JWTPayload | null {
  const token = getTokenFromRequest(request)
  console.log('Auth check:', {
    hasToken: !!token,
    tokenLength: token?.length,
    cookies: request.cookies.getAll().map(c => ({ name: c.name, hasValue: !!c.value }))
  })

  if (!token) return null

  const user = verifyToken(token)
  console.log('Token verification:', { isValid: !!user, userId: user?.userId })

  return user
}
