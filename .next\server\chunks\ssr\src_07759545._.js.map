{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-6 w-6',\n    lg: 'h-8 w-8',\n  }\n\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        sizeClasses[size],\n        className\n      )}\n    />\n  )\n}\n\ninterface LoadingCardProps {\n  title?: string\n  description?: string\n  className?: string\n}\n\nexport function LoadingCard({ title = 'Loading...', description, className }: LoadingCardProps) {\n  return (\n    <div className={cn('flex items-center justify-center p-8', className)}>\n      <div className=\"text-center space-y-4\">\n        <LoadingSpinner size=\"lg\" className=\"mx-auto\" />\n        <div>\n          <h3 className=\"text-lg font-medium\">{title}</h3>\n          {description && (\n            <p className=\"text-sm text-muted-foreground\">{description}</p>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingPage() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <LoadingCard title=\"Loading application...\" />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;AAQO,SAAS,YAAY,EAAE,QAAQ,YAAY,EAAE,WAAW,EAAE,SAAS,EAAoB;IAC5F,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;kBACzD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAe,MAAK;oBAAK,WAAU;;;;;;8BACpC,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAuB;;;;;;wBACpC,6BACC,8OAAC;4BAAE,WAAU;sCAAiC;;;;;;;;;;;;;;;;;;;;;;;AAM1D;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAY,OAAM;;;;;;;;;;;AAGzB", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/protected-route.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/components/providers/auth-provider'\nimport { LoadingPage } from '@/components/ui/loading'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n}\n\nexport function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/signin')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return <LoadingPage />\n  }\n\n  if (!user) {\n    return null // Will redirect in useEffect\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWO,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBAAO,8OAAC,mIAAA,CAAA,cAAW;;;;;IACrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,6BAA6B;;IAC3C;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/app/dashboard/layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname, useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\nimport { ProtectedRoute } from '@/components/protected-route'\nimport { useAuth } from '@/components/providers/auth-provider'\nimport {\n  LayoutDashboard,\n  Users,\n  FileText,\n  Calendar,\n  Upload,\n  LogOut,\n  Menu,\n  X\n} from 'lucide-react'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },\n  { name: 'Leads', href: '/dashboard/leads', icon: Users },\n  { name: 'Notes', href: '/dashboard/notes', icon: FileText },\n  { name: 'Follow-ups', href: '/dashboard/follow-ups', icon: Calendar },\n  { name: 'Import', href: '/dashboard/import', icon: Upload },\n]\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n  const { logout } = useAuth()\n\n  const handleSignOut = async () => {\n    try {\n      await logout()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    }\n  }\n\n  return (\n    <ProtectedRoute>\n      <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={cn(\n        \"fixed inset-0 z-50 lg:hidden\",\n        sidebarOpen ? \"block\" : \"hidden\"\n      )}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <h1 className=\"text-xl font-bold\">Lead Tracker</h1>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-6 w-6\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    \"group flex items-center px-2 py-2 text-sm font-medium rounded-md\",\n                    isActive\n                      ? \"bg-gray-100 text-gray-900\"\n                      : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                  )}\n                  onClick={() => setSidebarOpen(false)}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </nav>\n          <div className=\"p-4\">\n            <Button\n              variant=\"outline\"\n              className=\"w-full\"\n              onClick={handleSignOut}\n            >\n              <LogOut className=\"mr-2 h-4 w-4\" />\n              Sign Out\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-4\">\n            <h1 className=\"text-xl font-bold\">Lead Tracker</h1>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    \"group flex items-center px-2 py-2 text-sm font-medium rounded-md\",\n                    isActive\n                      ? \"bg-gray-100 text-gray-900\"\n                      : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                  )}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </nav>\n          <div className=\"p-4\">\n            <Button\n              variant=\"outline\"\n              className=\"w-full\"\n              onClick={handleSignOut}\n            >\n              <LogOut className=\"mr-2 h-4 w-4\" />\n              Sign Out\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Mobile header */}\n        <div className=\"flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 lg:hidden\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-6 w-6\" />\n          </Button>\n          <h1 className=\"text-xl font-bold\">Lead Tracker</h1>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-8\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAoBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,4NAAA,CAAA,kBAAe;IAAC;IAC/D;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACvD;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,8MAAA,CAAA,WAAQ;IAAC;IAC1D;QAAE,MAAM;QAAc,MAAM;QAAyB,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACpE;QAAE,MAAM;QAAU,MAAM;QAAqB,MAAM,sMAAA,CAAA,SAAM;IAAC;CAC3D;AAEc,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD;IAEzB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,qBACE,8OAAC,wIAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEf,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,gCACA,cAAc,UAAU;;sCAExB,8OAAC;4BAAI,WAAU;4BAA0C,SAAS,IAAM,eAAe;;;;;;sCACvF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoB;;;;;;sDAClC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,eAAe;sDAE9B,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI;wCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA,WACI,8BACA;4CAEN,SAAS,IAAM,eAAe;;8DAE9B,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CAXL,KAAK,IAAI;;;;;oCAcpB;;;;;;8CAEF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS;;0DAET,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAQ3C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoB;;;;;;;;;;;0CAEpC,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA,WACI,8BACA;;0DAGN,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;;uCAVL,KAAK,IAAI;;;;;gCAapB;;;;;;0CAEF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;;sDAET,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAQ3C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe;8CAE9B,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCAAG,WAAU;8CAAoB;;;;;;;;;;;;sCAIpC,8OAAC;4BAAK,WAAU;sCACd,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOb", "debugId": null}}]}