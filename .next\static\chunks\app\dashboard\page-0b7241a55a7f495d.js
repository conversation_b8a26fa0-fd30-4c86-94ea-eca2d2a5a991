(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{4351:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(5155),r=t(6695),d=t(6126),n=t(7580),l=t(5670),c=t(3109),i=t(9074),o=t(3540),x=t(170),m=t(4117),h=t(4811),u=t(4021),j=t(6046),f=t(4754),v=t(6025),p=t(2071),N=t(22),b=t(2960);function g(){let{data:e,isLoading:s,error:t}=(0,b.I)({queryKey:["dashboard-stats"],queryFn:async()=>{let e=await fetch("/api/dashboard/stats");if(!e.ok)throw Error("Failed to fetch dashboard stats");return e.json()},staleTime:3e5});if(s)return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Dashboard"}),(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Loading..."})}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:"-"})})]},s))})]});if(t||!e)return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Dashboard"}),(0,a.jsxs)("p",{children:["Failed to load dashboard data. ",null==t?void 0:t.message]})]});let{overview:g}=e;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Dashboard"}),(0,a.jsxs)(d.E,{variant:"outline",className:"text-sm",children:[g.conversionRate,"% conversion rate"]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Total Leads"}),(0,a.jsx)(n.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:g.totalLeads}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[g.leadsThisMonth," this month"]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"New Leads"}),(0,a.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:g.newLeads}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[g.leadsThisWeek," this week"]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Converted"}),(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:g.convertedLeads}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[g.conversionRate,"% conversion rate"]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Follow-ups"}),(0,a.jsx)(i.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:g.upcomingFollowUps}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"upcoming tasks"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Lead Status Distribution"}),(0,a.jsx)(r.BT,{children:"Current breakdown of lead statuses"})]}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)("div",{className:"h-80",children:(0,a.jsx)(o.u,{width:"100%",height:"100%",children:(0,a.jsxs)(x.r,{children:[(0,a.jsx)(m.F,{data:[{name:"New",value:g.newLeads,color:"#3b82f6"},{name:"Contacted",value:g.contactedLeads,color:"#6b7280"},{name:"Interested",value:g.interestedLeads,color:"#eab308"},{name:"Converted",value:g.convertedLeads,color:"#22c55e"},{name:"Lost",value:g.lostLeads,color:"#ef4444"}].filter(e=>e.value>0),cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:s,percent:t}=e;return"".concat(s," ").concat((100*t).toFixed(0),"%")},outerRadius:80,fill:"#8884d8",dataKey:"value",children:[{name:"New",value:g.newLeads,color:"#3b82f6"},{name:"Contacted",value:g.contactedLeads,color:"#6b7280"},{name:"Interested",value:g.interestedLeads,color:"#eab308"},{name:"Converted",value:g.convertedLeads,color:"#22c55e"},{name:"Lost",value:g.lostLeads,color:"#ef4444"}].filter(e=>e.value>0).map((e,s)=>(0,a.jsx)(h.f,{fill:e.color},"cell-".concat(s)))}),(0,a.jsx)(u.m,{})]})})})})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Lead Conversion Trend"}),(0,a.jsx)(r.BT,{children:"Monthly lead conversion over time"})]}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)("div",{className:"h-80",children:(0,a.jsx)(o.u,{width:"100%",height:"100%",children:(0,a.jsxs)(j.b,{data:e.conversionChart,children:[(0,a.jsx)(f.d,{strokeDasharray:"3 3"}),(0,a.jsx)(v.W,{dataKey:"month"}),(0,a.jsx)(p.h,{}),(0,a.jsx)(u.m,{}),(0,a.jsx)(N.N,{type:"monotone",dataKey:"new",stroke:"#3b82f6",name:"New"}),(0,a.jsx)(N.N,{type:"monotone",dataKey:"converted",stroke:"#22c55e",name:"Converted"}),(0,a.jsx)(N.N,{type:"monotone",dataKey:"lost",stroke:"#ef4444",name:"Lost"})]})})})})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Lead Status Breakdown"}),(0,a.jsx)(r.BT,{children:"Current distribution of lead statuses"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"New"}),(0,a.jsx)(d.E,{variant:"secondary",children:g.newLeads})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Contacted"}),(0,a.jsx)(d.E,{variant:"outline",children:g.contactedLeads})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Interested"}),(0,a.jsx)(d.E,{className:"bg-yellow-500 text-white",children:g.interestedLeads})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Converted"}),(0,a.jsx)(d.E,{className:"bg-green-500 text-white",children:g.convertedLeads})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Lost"}),(0,a.jsx)(d.E,{variant:"destructive",children:g.lostLeads})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Recent Activity"}),(0,a.jsx)(r.BT,{children:"Summary of recent lead activity"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{className:"font-medium",children:"This Month"}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:[g.leadsThisMonth," new leads added"]})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{className:"font-medium",children:"This Week"}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:[g.leadsThisWeek," new leads added"]})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{className:"font-medium",children:"Upcoming"}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:[g.upcomingFollowUps," follow-ups scheduled"]})]})]})]})]})]})}},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(5155);t(2115);var r=t(2085),d=t(9434);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,d.cn)(n({variant:t}),s),...r})}},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>i,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>l});var a=t(5155),r=t(2115),d=t(9434);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});n.displayName="Card";let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",t),...r})});l.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,d.cn)("text-sm text-muted-foreground",t),...r})});i.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,d.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,d.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},7304:(e,s,t)=>{Promise.resolve().then(t.bind(t,4351))},9434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>d});var a=t(2596),r=t(9688);function d(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}}},e=>{e.O(0,[3,967,652,441,964,358],()=>e(e.s=7304)),_N_E=e.O()}]);