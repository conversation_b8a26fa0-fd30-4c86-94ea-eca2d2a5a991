{"version": 3, "sources": [], "sections": [{"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getUserFromRequestEdge } from '@/lib/auth-edge'\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // Skip middleware for static files and Next.js internals\n  if (\n    pathname.startsWith('/_next/') ||\n    pathname.startsWith('/api/_next/') ||\n    pathname.includes('favicon.ico') ||\n    pathname.includes('.') && !pathname.includes('/api/')\n  ) {\n    return NextResponse.next()\n  }\n\n  // Only protect API routes (except auth routes)\n  if (pathname.startsWith('/api/')) {\n    const publicApiRoutes = ['/api/auth/', '/api/test-auth', '/api/debug-auth']\n    const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route))\n\n    if (!isPublicApiRoute) {\n      const user = getUserFromRequest(request)\n      console.log('Middleware API protection:', {\n        pathname,\n        hasUser: !!user,\n        userId: user?.userId,\n        isPublicApiRoute\n      })\n\n      if (!user) {\n        console.log('Blocking API request - no valid user')\n        return NextResponse.json(\n          { error: 'Unauthorized' },\n          { status: 401 }\n        )\n      }\n    }\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|public/).*)',\n  ],\n  runtime: 'nodejs',\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,yDAAyD;IACzD,IACE,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,QAAQ,CAAC,kBAClB,SAAS,QAAQ,CAAC,QAAQ,CAAC,SAAS,QAAQ,CAAC,UAC7C;QACA,OAAO,8HAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,+CAA+C;IAC/C,IAAI,SAAS,UAAU,CAAC,UAAU;QAChC,MAAM,kBAAkB;YAAC;YAAc;YAAkB;SAAkB;QAC3E,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC;QAE3E,IAAI,CAAC,kBAAkB;YACrB,MAAM,OAAO,mBAAmB;YAChC,QAAQ,GAAG,CAAC,8BAA8B;gBACxC;gBACA,SAAS,CAAC,CAAC;gBACX,QAAQ,MAAM;gBACd;YACF;YAEA,IAAI,CAAC,MAAM;gBACT,QAAQ,GAAG,CAAC;gBACZ,OAAO,8HAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAe,GACxB;oBAAE,QAAQ;gBAAI;YAElB;QACF;IACF;IAEA,OAAO,8HAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;IACD,SAAS;AACX", "debugId": null}}]}