const CHUNK_PUBLIC_PATH = "server/middleware.js";
const runtime = require("./chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_e4ccf2f3._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__7d195473._.js");
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/middleware.js { INNER_MIDDLEWARE_MODULE => \"[project]/src/middleware.ts [middleware] (ecmascript)\" } [middleware] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/middleware.js { INNER_MIDDLEWARE_MODULE => \"[project]/src/middleware.ts [middleware] (ecmascript)\" } [middleware] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
