(()=>{var a={};a.id=105,a.ids=[105],a.modules={175:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if(!a||"object"!=typeof a)return!1;let b=Object.getPrototypeOf(a);return(null===b||b===Object.prototype||null===Object.getPrototypeOf(b))&&"[object Object]"===Object.prototype.toString.call(a)}},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1640:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObject=function(a){return null!==a&&("object"==typeof a||"function"==typeof a)}},1706:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.eq=function(a,b){return a===b||Number.isNaN(a)&&Number.isNaN(b)}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3567:(a,b,c)=>{"use strict";var d=c(43210);"function"==typeof Object.is&&Object.is,d.useSyncExternalStore,d.useRef,d.useEffect,d.useMemo,d.useDebugValue},5664:(a,b,c)=>{a.exports=c(87509).get},6895:(a,b,c)=>{"use strict";c(3567)},8384:(a,b,c)=>{Promise.resolve().then(c.bind(c,44714))},9474:(a,b,c)=>{a.exports=c(33731).last},10687:(a,b,c)=>{a.exports=c(75446).sortBy},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10907:(a,b,c)=>{"use strict";var d=c(43210),e=c(57379),f="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},g=e.useSyncExternalStore,h=d.useRef,i=d.useEffect,j=d.useMemo,k=d.useDebugValue;b.useSyncExternalStoreWithSelector=function(a,b,c,d,e){var l=h(null);if(null===l.current){var m={hasValue:!1,value:null};l.current=m}else m=l.current;var n=g(a,(l=j(function(){function a(a){if(!i){if(i=!0,g=a,a=d(a),void 0!==e&&m.hasValue){var b=m.value;if(e(b,a))return h=b}return h=a}if(b=h,f(g,a))return b;var c=d(a);return void 0!==e&&e(b,c)?(g=a,b):(g=a,h=c)}var g,h,i=!1,j=void 0===c?null:c;return[function(){return a(b())},null===j?void 0:function(){return a(j())}]},[b,c,d,e]))[0],l[1]);return i(function(){m.hasValue=!0,m.value=n},[n]),k(n),n}},11117:a=>{"use strict";var b=Object.prototype.hasOwnProperty,c="~";function d(){}function e(a,b,c){this.fn=a,this.context=b,this.once=c||!1}function f(a,b,d,f,g){if("function"!=typeof d)throw TypeError("The listener must be a function");var h=new e(d,f||a,g),i=c?c+b:b;return a._events[i]?a._events[i].fn?a._events[i]=[a._events[i],h]:a._events[i].push(h):(a._events[i]=h,a._eventsCount++),a}function g(a,b){0==--a._eventsCount?a._events=new d:delete a._events[b]}function h(){this._events=new d,this._eventsCount=0}Object.create&&(d.prototype=Object.create(null),new d().__proto__||(c=!1)),h.prototype.eventNames=function(){var a,d,e=[];if(0===this._eventsCount)return e;for(d in a=this._events)b.call(a,d)&&e.push(c?d.slice(1):d);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(a)):e},h.prototype.listeners=function(a){var b=c?c+a:a,d=this._events[b];if(!d)return[];if(d.fn)return[d.fn];for(var e=0,f=d.length,g=Array(f);e<f;e++)g[e]=d[e].fn;return g},h.prototype.listenerCount=function(a){var b=c?c+a:a,d=this._events[b];return d?d.fn?1:d.length:0},h.prototype.emit=function(a,b,d,e,f,g){var h=c?c+a:a;if(!this._events[h])return!1;var i,j,k=this._events[h],l=arguments.length;if(k.fn){switch(k.once&&this.removeListener(a,k.fn,void 0,!0),l){case 1:return k.fn.call(k.context),!0;case 2:return k.fn.call(k.context,b),!0;case 3:return k.fn.call(k.context,b,d),!0;case 4:return k.fn.call(k.context,b,d,e),!0;case 5:return k.fn.call(k.context,b,d,e,f),!0;case 6:return k.fn.call(k.context,b,d,e,f,g),!0}for(j=1,i=Array(l-1);j<l;j++)i[j-1]=arguments[j];k.fn.apply(k.context,i)}else{var m,n=k.length;for(j=0;j<n;j++)switch(k[j].once&&this.removeListener(a,k[j].fn,void 0,!0),l){case 1:k[j].fn.call(k[j].context);break;case 2:k[j].fn.call(k[j].context,b);break;case 3:k[j].fn.call(k[j].context,b,d);break;case 4:k[j].fn.call(k[j].context,b,d,e);break;default:if(!i)for(m=1,i=Array(l-1);m<l;m++)i[m-1]=arguments[m];k[j].fn.apply(k[j].context,i)}}return!0},h.prototype.on=function(a,b,c){return f(this,a,b,c,!1)},h.prototype.once=function(a,b,c){return f(this,a,b,c,!0)},h.prototype.removeListener=function(a,b,d,e){var f=c?c+a:a;if(!this._events[f])return this;if(!b)return g(this,f),this;var h=this._events[f];if(h.fn)h.fn!==b||e&&!h.once||d&&h.context!==d||g(this,f);else{for(var i=0,j=[],k=h.length;i<k;i++)(h[i].fn!==b||e&&!h[i].once||d&&h[i].context!==d)&&j.push(h[i]);j.length?this._events[f]=1===j.length?j[0]:j:g(this,f)}return this},h.prototype.removeAllListeners=function(a){var b;return a?(b=c?c+a:a,this._events[b]&&g(this,b)):(this._events=new d,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=c,h.EventEmitter=h,a.exports=h},12728:(a,b,c)=>{a.exports=c(92292).isEqual},14454:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(42066),e=c(1640),f=c(23457),g=c(1706);function h(a,b,c,d){if(b===a)return!0;switch(typeof b){case"object":return function(a,b,c,d){if(null==b)return!0;if(Array.isArray(b))return i(a,b,c,d);if(b instanceof Map){var e=a,g=b,h=c,k=d;if(0===g.size)return!0;if(!(e instanceof Map))return!1;for(let[a,b]of g.entries())if(!1===h(e.get(a),b,a,e,g,k))return!1;return!0}if(b instanceof Set)return j(a,b,c,d);let l=Object.keys(b);if(null==a)return 0===l.length;if(0===l.length)return!0;if(d&&d.has(b))return d.get(b)===a;d&&d.set(b,a);try{for(let e=0;e<l.length;e++){let g=l[e];if(!f.isPrimitive(a)&&!(g in a)||void 0===b[g]&&void 0!==a[g]||null===b[g]&&null!==a[g]||!c(a[g],b[g],g,a,b,d))return!1}return!0}finally{d&&d.delete(b)}}(a,b,c,d);case"function":if(Object.keys(b).length>0)return h(a,{...b},c,d);return g.eq(a,b);default:if(!e.isObject(a))return g.eq(a,b);if("string"==typeof b)return""===b;return!0}}function i(a,b,c,d){if(0===b.length)return!0;if(!Array.isArray(a))return!1;let e=new Set;for(let f=0;f<b.length;f++){let g=b[f],h=!1;for(let i=0;i<a.length;i++){if(e.has(i))continue;let j=a[i],k=!1;if(c(j,g,f,a,b,d)&&(k=!0),k){e.add(i),h=!0;break}}if(!h)return!1}return!0}function j(a,b,c,d){return 0===b.size||a instanceof Set&&i([...a],[...b],c,d)}b.isMatchWith=function(a,b,c){return"function"!=typeof c?d.isMatch(a,b):h(a,b,function a(b,d,e,f,g,i){let j=c(b,d,e,f,g,i);return void 0!==j?!!j:h(b,d,a,i)},new Map)},b.isSetMatch=j},15708:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(95819);b.toNumber=function(a){return d.isSymbol(a)?NaN:Number(a)}},17617:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(92681),e=c(40144),f=c(74838),g=c(30415);b.iteratee=function(a){if(null==a)return d.identity;switch(typeof a){case"function":return a;case"object":if(Array.isArray(a)&&2===a.length)return g.matchesProperty(a[0],a[1]);return f.matches(a);case"string":case"symbol":case"number":return e.property(a)}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20911:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(55100);b.debounce=function(a,b=0,c={}){let e;"object"!=typeof c&&(c={});let{leading:f=!1,trailing:g=!0,maxWait:h}=c,i=[,,];f&&(i[0]="leading"),g&&(i[1]="trailing");let j=null,k=d.debounce(function(...b){e=a.apply(this,b),j=null},b,{edges:i}),l=function(...b){return null!=h&&(null===j&&(j=Date.now()),Date.now()-j>=h)?(e=a.apply(this,b),j=Date.now(),k.cancel(),k.schedule(),e):(k.apply(this,b),e)};return l.cancel=k.cancel,l.flush=()=>(k.flush(),e),l}},21251:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isTypedArray=function(a){return ArrayBuffer.isView(a)&&!(a instanceof DataView)}},21424:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toArray=function(a){return Array.isArray(a)?a:Array.from(a)}},21536:(a,b,c)=>{Promise.resolve().then(c.bind(c,80559))},23457:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPrimitive=function(a){return null==a||"object"!=typeof a&&"function"!=typeof a}},23854:(a,b,c)=>{a.exports=c(45263).uniqBy},26349:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(48130);b.isArrayLike=function(a){return null!=a&&"function"!=typeof a&&d.isLength(a.length)}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27469:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.argumentsTag="[object Arguments]",b.arrayBufferTag="[object ArrayBuffer]",b.arrayTag="[object Array]",b.bigInt64ArrayTag="[object BigInt64Array]",b.bigUint64ArrayTag="[object BigUint64Array]",b.booleanTag="[object Boolean]",b.dataViewTag="[object DataView]",b.dateTag="[object Date]",b.errorTag="[object Error]",b.float32ArrayTag="[object Float32Array]",b.float64ArrayTag="[object Float64Array]",b.functionTag="[object Function]",b.int16ArrayTag="[object Int16Array]",b.int32ArrayTag="[object Int32Array]",b.int8ArrayTag="[object Int8Array]",b.mapTag="[object Map]",b.numberTag="[object Number]",b.objectTag="[object Object]",b.regexpTag="[object RegExp]",b.setTag="[object Set]",b.stringTag="[object String]",b.symbolTag="[object Symbol]",b.uint16ArrayTag="[object Uint16Array]",b.uint32ArrayTag="[object Uint32Array]",b.uint8ArrayTag="[object Uint8Array]",b.uint8ClampedArrayTag="[object Uint8ClampedArray]"},28354:a=>{"use strict";a.exports=require("util")},28382:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.flatten=function(a,b=1){let c=[],d=Math.floor(b),e=(a,b)=>{for(let f=0;f<a.length;f++){let g=a[f];Array.isArray(g)&&b<d?e(g,b+1):c.push(g)}};return e(a,0),c}},29243:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(91428);b.isArguments=function(a){return null!==a&&"object"==typeof a&&"[object Arguments]"===d.getTag(a)}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29632:(a,b,c)=>{"use strict";a.exports=c(97668)},29862:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(92923),e=c(27469);b.cloneDeepWith=function(a,b){return d.cloneDeepWith(a,(c,f,g,h)=>{let i=b?.(c,f,g,h);if(null!=i)return i;if("object"==typeof a)switch(Object.prototype.toString.call(a)){case e.numberTag:case e.stringTag:case e.booleanTag:{let b=new a.constructor(a?.valueOf());return d.copyProperties(b,a),b}case e.argumentsTag:{let b={};return d.copyProperties(b,a),b.length=a.length,b[Symbol.iterator]=a[Symbol.iterator],b}default:return}})}},30415:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(42066),e=c(30657),f=c(59138),g=c(87509),h=c(57841);b.matchesProperty=function(a,b){switch(typeof a){case"object":Object.is(a?.valueOf(),-0)&&(a="-0");break;case"number":a=e.toKey(a)}return b=f.cloneDeep(b),function(c){let e=g.get(c,a);return void 0===e?h.has(c,a):void 0===b?void 0===e:d.isMatch(e,b)}}},30657:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toKey=function(a){return"string"==typeof a||"symbol"==typeof a?a:Object.is(a?.valueOf?.(),-0)?"-0":String(a)}},30921:(a,b,c)=>{a.exports=c(71337).range},33731:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(97766),e=c(21424),f=c(26349);b.last=function(a){if(f.isArrayLike(a))return d.last(e.toArray(a))}},33873:a=>{"use strict";a.exports=require("path")},35314:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isUnsafeProperty=function(a){return"__proto__"===a}},36023:(a,b)=>{"use strict";function c(a){return"symbol"==typeof a?1:null===a?2:void 0===a?3:4*(a!=a)}Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.compareValues=(a,b,d)=>{if(a!==b){let e=c(a),f=c(b);if(e===f&&0===e){if(a<b)return"desc"===d?1:-1;if(a>b)return"desc"===d?-1:1}return"desc"===d?f-e:e-f}return 0}},37586:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(36023),e=c(76021),f=c(43574);b.orderBy=function(a,b,c,g){if(null==a)return[];c=g?void 0:c,Array.isArray(a)||(a=Object.values(a)),Array.isArray(b)||(b=null==b?[null]:[b]),0===b.length&&(b=[null]),Array.isArray(c)||(c=null==c?[]:[c]),c=c.map(a=>String(a));let h=(a,b)=>{let c=a;for(let a=0;a<b.length&&null!=c;++a)c=c[b[a]];return c},i=b.map(a=>(Array.isArray(a)&&1===a.length&&(a=a[0]),null==a||"function"==typeof a||Array.isArray(a)||e.isKey(a))?a:{key:a,path:f.toPath(a)});return a.map(a=>({original:a,criteria:i.map(b=>{var c,d;return c=b,null==(d=a)||null==c?d:"object"==typeof c&&"key"in c?Object.hasOwn(d,c.key)?d[c.key]:h(d,c.path):"function"==typeof c?c(d):Array.isArray(c)?h(d,c):"object"==typeof d?d[c]:d})})).slice().sort((a,b)=>{for(let e=0;e<i.length;e++){let f=d.compareValues(a.criteria[e],b.criteria[e],c[e]);if(0!==f)return f}return 0}).map(a=>a.original)}},39733:(a,b,c)=>{"use strict";a.exports=c(10907)},40144:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(87509);b.property=function(a){return function(b){return d.get(b,a)}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42066:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(14454);b.isMatch=function(a,b){return d.isMatchWith(a,b,()=>void 0)}},42750:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(15708);b.toFinite=function(a){return a?(a=d.toNumber(a))===1/0||a===-1/0?(a<0?-1:1)*Number.MAX_VALUE:a==a?a:0:0===a?a:0}},43084:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(20911);b.throttle=function(a,b=0,c={}){let{leading:e=!0,trailing:f=!0}=c;return d.debounce(a,b,{leading:e,maxWait:b,trailing:f})}},43574:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toPath=function(a){let b=[],c=a.length;if(0===c)return b;let d=0,e="",f="",g=!1;for(46===a.charCodeAt(0)&&(b.push(""),d++);d<c;){let h=a[d];f?"\\"===h&&d+1<c?e+=a[++d]:h===f?f="":e+=h:g?'"'===h||"'"===h?f=h:"]"===h?(g=!1,b.push(e),e=""):e+=h:"["===h?(g=!0,e&&(b.push(e),e="")):"."===h?e&&(b.push(e),e=""):e+=h,d++}return e&&b.push(e),b}},44714:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>rX});var d={};c.r(d),c.d(d,{scaleBand:()=>dg,scaleDiverging:()=>function a(){var b=ev(gK()(ed));return b.copy=function(){return gH(b,a())},da.apply(b,arguments)},scaleDivergingLog:()=>function a(){var b=eD(gK()).domain([.1,1,10]);return b.copy=function(){return gH(b,a()).base(b.base())},da.apply(b,arguments)},scaleDivergingPow:()=>gL,scaleDivergingSqrt:()=>gM,scaleDivergingSymlog:()=>function a(){var b=eG(gK());return b.copy=function(){return gH(b,a()).constant(b.constant())},da.apply(b,arguments)},scaleIdentity:()=>function a(b){var c;function d(a){return null==a||isNaN(a*=1)?c:a}return d.invert=d,d.domain=d.range=function(a){return arguments.length?(b=Array.from(a,eb),d):b.slice()},d.unknown=function(a){return arguments.length?(c=a,d):c},d.copy=function(){return a(b).unknown(c)},b=arguments.length?Array.from(b,eb):[0,1],ev(d)},scaleImplicit:()=>de,scaleLinear:()=>function a(){var b=ej();return b.copy=function(){return eh(b,a())},c9.apply(b,arguments),ev(b)},scaleLog:()=>function a(){let b=eD(ei()).domain([1,10]);return b.copy=()=>eh(b,a()).base(b.base()),c9.apply(b,arguments),b},scaleOrdinal:()=>df,scalePoint:()=>dh,scalePow:()=>eL,scaleQuantile:()=>function a(){var b,c=[],d=[],e=[];function f(){var a=0,b=Math.max(1,d.length);for(e=Array(b-1);++a<b;)e[a-1]=function(a,b,c=du){if(!(!(d=a.length)||isNaN(b*=1))){if(b<=0||d<2)return+c(a[0],0,a);if(b>=1)return+c(a[d-1],d-1,a);var d,e=(d-1)*b,f=Math.floor(e),g=+c(a[f],f,a);return g+(c(a[f+1],f+1,a)-g)*(e-f)}}(c,a/b);return g}function g(a){return null==a||isNaN(a*=1)?b:d[dw(e,a)]}return g.invertExtent=function(a){var b=d.indexOf(a);return b<0?[NaN,NaN]:[b>0?e[b-1]:c[0],b<e.length?e[b]:c[c.length-1]]},g.domain=function(a){if(!arguments.length)return c.slice();for(let b of(c=[],a))null==b||isNaN(b*=1)||c.push(b);return c.sort(dq),f()},g.range=function(a){return arguments.length?(d=Array.from(a),f()):d.slice()},g.unknown=function(a){return arguments.length?(b=a,g):b},g.quantiles=function(){return e.slice()},g.copy=function(){return a().domain(c).range(d).unknown(b)},c9.apply(g,arguments)},scaleQuantize:()=>function a(){var b,c=0,d=1,e=1,f=[.5],g=[0,1];function h(a){return null!=a&&a<=a?g[dw(f,a,0,e)]:b}function i(){var a=-1;for(f=Array(e);++a<e;)f[a]=((a+1)*d-(a-e)*c)/(e+1);return h}return h.domain=function(a){return arguments.length?([c,d]=a,c*=1,d*=1,i()):[c,d]},h.range=function(a){return arguments.length?(e=(g=Array.from(a)).length-1,i()):g.slice()},h.invertExtent=function(a){var b=g.indexOf(a);return b<0?[NaN,NaN]:b<1?[c,f[0]]:b>=e?[f[e-1],d]:[f[b-1],f[b]]},h.unknown=function(a){return arguments.length&&(b=a),h},h.thresholds=function(){return f.slice()},h.copy=function(){return a().domain([c,d]).range(g).unknown(b)},c9.apply(ev(h),arguments)},scaleRadial:()=>function a(){var b,c=ej(),d=[0,1],e=!1;function f(a){var d,f=Math.sign(d=c(a))*Math.sqrt(Math.abs(d));return isNaN(f)?b:e?Math.round(f):f}return f.invert=function(a){return c.invert(eN(a))},f.domain=function(a){return arguments.length?(c.domain(a),f):c.domain()},f.range=function(a){return arguments.length?(c.range((d=Array.from(a,eb)).map(eN)),f):d.slice()},f.rangeRound=function(a){return f.range(a).round(!0)},f.round=function(a){return arguments.length?(e=!!a,f):e},f.clamp=function(a){return arguments.length?(c.clamp(a),f):c.clamp()},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a(c.domain(),d).round(e).clamp(c.clamp()).unknown(b)},c9.apply(f,arguments),ev(f)},scaleSequential:()=>function a(){var b=ev(gG()(ed));return b.copy=function(){return gH(b,a())},da.apply(b,arguments)},scaleSequentialLog:()=>function a(){var b=eD(gG()).domain([1,10]);return b.copy=function(){return gH(b,a()).base(b.base())},da.apply(b,arguments)},scaleSequentialPow:()=>gI,scaleSequentialQuantile:()=>function a(){var b=[],c=ed;function d(a){if(null!=a&&!isNaN(a*=1))return c((dw(b,a,1)-1)/(b.length-1))}return d.domain=function(a){if(!arguments.length)return b.slice();for(let c of(b=[],a))null==c||isNaN(c*=1)||b.push(c);return b.sort(dq),d},d.interpolator=function(a){return arguments.length?(c=a,d):c},d.range=function(){return b.map((a,d)=>c(d/(b.length-1)))},d.quantiles=function(a){return Array.from({length:a+1},(c,d)=>(function(a,b,c){if(!(!(d=(a=Float64Array.from(function*(a,b){if(void 0===b)for(let b of a)null!=b&&(b*=1)>=b&&(yield b);else{let c=-1;for(let d of a)null!=(d=b(d,++c,a))&&(d*=1)>=d&&(yield d)}}(a,void 0))).length)||isNaN(b*=1))){if(b<=0||d<2)return eP(a);if(b>=1)return eO(a);var d,e=(d-1)*b,f=Math.floor(e),g=eO((function a(b,c,d=0,e=1/0,f){if(c=Math.floor(c),d=Math.floor(Math.max(0,d)),e=Math.floor(Math.min(b.length-1,e)),!(d<=c&&c<=e))return b;for(f=void 0===f?eQ:function(a=dq){if(a===dq)return eQ;if("function"!=typeof a)throw TypeError("compare is not a function");return(b,c)=>{let d=a(b,c);return d||0===d?d:(0===a(c,c))-(0===a(b,b))}}(f);e>d;){if(e-d>600){let g=e-d+1,h=c-d+1,i=Math.log(g),j=.5*Math.exp(2*i/3),k=.5*Math.sqrt(i*j*(g-j)/g)*(h-g/2<0?-1:1),l=Math.max(d,Math.floor(c-h*j/g+k)),m=Math.min(e,Math.floor(c+(g-h)*j/g+k));a(b,c,l,m,f)}let g=b[c],h=d,i=e;for(eR(b,d,c),f(b[e],g)>0&&eR(b,d,e);h<i;){for(eR(b,h,i),++h,--i;0>f(b[h],g);)++h;for(;f(b[i],g)>0;)--i}0===f(b[d],g)?eR(b,d,i):eR(b,++i,e),i<=c&&(d=i+1),c<=i&&(e=i-1)}return b})(a,f).subarray(0,f+1));return g+(eP(a.subarray(f+1))-g)*(e-f)}})(b,d/a))},d.copy=function(){return a(c).domain(b)},da.apply(d,arguments)},scaleSequentialSqrt:()=>gJ,scaleSequentialSymlog:()=>function a(){var b=eG(gG());return b.copy=function(){return gH(b,a()).constant(b.constant())},da.apply(b,arguments)},scaleSqrt:()=>eM,scaleSymlog:()=>function a(){var b=eG(ei());return b.copy=function(){return eh(b,a()).constant(b.constant())},c9.apply(b,arguments)},scaleThreshold:()=>function a(){var b,c=[.5],d=[0,1],e=1;function f(a){return null!=a&&a<=a?d[dw(c,a,0,e)]:b}return f.domain=function(a){return arguments.length?(e=Math.min((c=Array.from(a)).length,d.length-1),f):c.slice()},f.range=function(a){return arguments.length?(d=Array.from(a),e=Math.min(c.length,d.length-1),f):d.slice()},f.invertExtent=function(a){var b=d.indexOf(a);return[c[b-1],c[b]]},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a().domain(c).range(d).unknown(b)},c9.apply(f,arguments)},scaleTime:()=>gE,scaleUtc:()=>gF,tickFormat:()=>eu});var e=c(60687),f=c(44493),g=c(96834),h=c(41312),i=c(62688);let j=(0,i.A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),k=(0,i.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var l=c(40228),m=c(49384),n=c(43210),o=c(67766),p=c.n(o),q=c(5664),r=c.n(q),s=a=>0===a?0:a>0?1:-1,t=a=>"number"==typeof a&&a!=+a,u=a=>"string"==typeof a&&a.indexOf("%")===a.length-1,v=a=>("number"==typeof a||a instanceof Number)&&!t(a),w=a=>v(a)||"string"==typeof a,x=0,y=a=>{var b=++x;return"".concat(a||"").concat(b)},z=function(a,b){var c,d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,e=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!v(a)&&"string"!=typeof a)return d;if(u(a)){if(null==b)return d;var f=a.indexOf("%");c=b*parseFloat(a.slice(0,f))/100}else c=+a;return t(c)&&(c=d),e&&null!=b&&c>b&&(c=b),c},A=a=>{if(!Array.isArray(a))return!1;for(var b=a.length,c={},d=0;d<b;d++)if(c[a[d]])return!0;else c[a[d]]=!0;return!1},B=(a,b)=>v(a)&&v(b)?c=>a+c*(b-a):()=>b;function C(a,b,c){if(a&&a.length)return a.find(a=>a&&("function"==typeof b?b(a):r()(a,b))===c)}var D=a=>null==a?a:"".concat(a.charAt(0).toUpperCase()).concat(a.slice(1)),E=function(a,b){for(var c=arguments.length,d=Array(c>2?c-2:0),e=2;e<c;e++)d[e-2]=arguments[e]};function F(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function G(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?F(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):F(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var H=(0,n.forwardRef)((a,b)=>{var{aspect:c,initialDimension:d={width:-1,height:-1},width:e="100%",height:f="100%",minWidth:g=0,minHeight:h,maxHeight:i,children:j,debounce:k=0,id:l,className:o,onResize:q,style:r={}}=a,s=(0,n.useRef)(null),t=(0,n.useRef)();t.current=q,(0,n.useImperativeHandle)(b,()=>s.current);var[v,w]=(0,n.useState)({containerWidth:d.width,containerHeight:d.height}),x=(0,n.useCallback)((a,b)=>{w(c=>{var d=Math.round(a),e=Math.round(b);return c.containerWidth===d&&c.containerHeight===e?c:{containerWidth:d,containerHeight:e}})},[]);(0,n.useEffect)(()=>{var a=a=>{var b,{width:c,height:d}=a[0].contentRect;x(c,d),null==(b=t.current)||b.call(t,c,d)};k>0&&(a=p()(a,k,{trailing:!0,leading:!1}));var b=new ResizeObserver(a),{width:c,height:d}=s.current.getBoundingClientRect();return x(c,d),b.observe(s.current),()=>{b.disconnect()}},[x,k]);var y=(0,n.useMemo)(()=>{var{containerWidth:a,containerHeight:b}=v;if(a<0||b<0)return null;E(u(e)||u(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",e,f),E(!c||c>0,"The aspect(%s) must be greater than zero.",c);var d=u(e)?a:e,k=u(f)?b:f;return c&&c>0&&(d?k=d/c:k&&(d=k*c),i&&k>i&&(k=i)),E(d>0||k>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",d,k,e,f,g,h,c),n.Children.map(j,a=>(0,n.cloneElement)(a,{width:d,height:k,style:G({width:d,height:k},a.props.style)}))},[c,j,f,i,h,g,v,e]);return n.createElement("div",{id:l?"".concat(l):void 0,className:(0,m.$)("recharts-responsive-container",o),style:G(G({},r),{},{width:e,height:f,minWidth:g,minHeight:h,maxHeight:i}),ref:s},n.createElement("div",{style:{width:0,height:0,overflow:"visible"}},y))});function I(a){return`Minified Redux error #${a}; visit https://redux.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}var J="function"==typeof Symbol&&Symbol.observable||"@@observable",K=()=>Math.random().toString(36).substring(7).split("").join("."),L={INIT:`@@redux/INIT${K()}`,REPLACE:`@@redux/REPLACE${K()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${K()}`};function M(a){if("object"!=typeof a||null===a)return!1;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b||null===Object.getPrototypeOf(a)}function N(a){let b,c=Object.keys(a),d={};for(let b=0;b<c.length;b++){let e=c[b];"function"==typeof a[e]&&(d[e]=a[e])}let e=Object.keys(d);try{Object.keys(d).forEach(a=>{let b=d[a];if(void 0===b(void 0,{type:L.INIT}))throw Error(I(12));if(void 0===b(void 0,{type:L.PROBE_UNKNOWN_ACTION()}))throw Error(I(13))})}catch(a){b=a}return function(a={},c){if(b)throw b;let f=!1,g={};for(let b=0;b<e.length;b++){let h=e[b],i=d[h],j=a[h],k=i(j,c);if(void 0===k)throw c&&c.type,Error(I(14));g[h]=k,f=f||k!==j}return(f=f||e.length!==Object.keys(a).length)?g:a}}function O(...a){return 0===a.length?a=>a:1===a.length?a[0]:a.reduce((a,b)=>(...c)=>a(b(...c)))}function P(a){return M(a)&&"type"in a&&"string"==typeof a.type}function Q(a){return({dispatch:b,getState:c})=>d=>e=>"function"==typeof e?e(b,c,a):d(e)}var R=Q(),S=Symbol.for("immer-nothing"),T=Symbol.for("immer-draftable"),U=Symbol.for("immer-state");function V(a){throw Error(`[Immer] minified error nr: ${a}. Full error at: https://bit.ly/3cXEKWf`)}var W=Object.getPrototypeOf;function X(a){return!!a&&!!a[U]}function Y(a){return!!a&&($(a)||Array.isArray(a)||!!a[T]||!!a.constructor?.[T]||ad(a)||ae(a))}var Z=Object.prototype.constructor.toString();function $(a){if(!a||"object"!=typeof a)return!1;let b=W(a);if(null===b)return!0;let c=Object.hasOwnProperty.call(b,"constructor")&&b.constructor;return c===Object||"function"==typeof c&&Function.toString.call(c)===Z}function _(a,b){0===aa(a)?Reflect.ownKeys(a).forEach(c=>{b(c,a[c],a)}):a.forEach((c,d)=>b(d,c,a))}function aa(a){let b=a[U];return b?b.type_:Array.isArray(a)?1:ad(a)?2:3*!!ae(a)}function ab(a,b){return 2===aa(a)?a.has(b):Object.prototype.hasOwnProperty.call(a,b)}function ac(a,b,c){let d=aa(a);2===d?a.set(b,c):3===d?a.add(c):a[b]=c}function ad(a){return a instanceof Map}function ae(a){return a instanceof Set}function af(a){return a.copy_||a.base_}function ag(a,b){if(ad(a))return new Map(a);if(ae(a))return new Set(a);if(Array.isArray(a))return Array.prototype.slice.call(a);let c=$(a);if(!0!==b&&("class_only"!==b||c)){let b=W(a);return null!==b&&c?{...a}:Object.assign(Object.create(b),a)}{let b=Object.getOwnPropertyDescriptors(a);delete b[U];let c=Reflect.ownKeys(b);for(let d=0;d<c.length;d++){let e=c[d],f=b[e];!1===f.writable&&(f.writable=!0,f.configurable=!0),(f.get||f.set)&&(b[e]={configurable:!0,writable:!0,enumerable:f.enumerable,value:a[e]})}return Object.create(W(a),b)}}function ah(a,b=!1){return aj(a)||X(a)||!Y(a)||(aa(a)>1&&(a.set=a.add=a.clear=a.delete=ai),Object.freeze(a),b&&Object.entries(a).forEach(([a,b])=>ah(b,!0))),a}function ai(){V(2)}function aj(a){return Object.isFrozen(a)}var ak={};function al(a){let b=ak[a];return b||V(0,a),b}function am(a,b){b&&(al("Patches"),a.patches_=[],a.inversePatches_=[],a.patchListener_=b)}function an(a){ao(a),a.drafts_.forEach(aq),a.drafts_=null}function ao(a){a===gU&&(gU=a.parent_)}function ap(a){return gU={drafts_:[],parent_:gU,immer_:a,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function aq(a){let b=a[U];0===b.type_||1===b.type_?b.revoke_():b.revoked_=!0}function ar(a,b){b.unfinalizedDrafts_=b.drafts_.length;let c=b.drafts_[0];return void 0!==a&&a!==c?(c[U].modified_&&(an(b),V(4)),Y(a)&&(a=as(b,a),b.parent_||au(b,a)),b.patches_&&al("Patches").generateReplacementPatches_(c[U].base_,a,b.patches_,b.inversePatches_)):a=as(b,c,[]),an(b),b.patches_&&b.patchListener_(b.patches_,b.inversePatches_),a!==S?a:void 0}function as(a,b,c){if(aj(b))return b;let d=b[U];if(!d)return _(b,(e,f)=>at(a,d,b,e,f,c)),b;if(d.scope_!==a)return b;if(!d.modified_)return au(a,d.base_,!0),d.base_;if(!d.finalized_){d.finalized_=!0,d.scope_.unfinalizedDrafts_--;let b=d.copy_,e=b,f=!1;3===d.type_&&(e=new Set(b),b.clear(),f=!0),_(e,(e,g)=>at(a,d,b,e,g,c,f)),au(a,b,!1),c&&a.patches_&&al("Patches").generatePatches_(d,c,a.patches_,a.inversePatches_)}return d.copy_}function at(a,b,c,d,e,f,g){if(X(e)){let g=as(a,e,f&&b&&3!==b.type_&&!ab(b.assigned_,d)?f.concat(d):void 0);if(ac(c,d,g),!X(g))return;a.canAutoFreeze_=!1}else g&&c.add(e);if(Y(e)&&!aj(e)){if(!a.immer_.autoFreeze_&&a.unfinalizedDrafts_<1)return;as(a,e),(!b||!b.scope_.parent_)&&"symbol"!=typeof d&&Object.prototype.propertyIsEnumerable.call(c,d)&&au(a,e)}}function au(a,b,c=!1){!a.parent_&&a.immer_.autoFreeze_&&a.canAutoFreeze_&&ah(b,c)}var av={get(a,b){if(b===U)return a;let c=af(a);if(!ab(c,b)){var d=a,e=c,f=b;let g=ay(e,f);return g?"value"in g?g.value:g.get?.call(d.draft_):void 0}let g=c[b];return a.finalized_||!Y(g)?g:g===ax(a.base_,b)?(aA(a),a.copy_[b]=aB(g,a)):g},has:(a,b)=>b in af(a),ownKeys:a=>Reflect.ownKeys(af(a)),set(a,b,c){let d=ay(af(a),b);if(d?.set)return d.set.call(a.draft_,c),!0;if(!a.modified_){let d=ax(af(a),b),e=d?.[U];if(e&&e.base_===c)return a.copy_[b]=c,a.assigned_[b]=!1,!0;if((c===d?0!==c||1/c==1/d:c!=c&&d!=d)&&(void 0!==c||ab(a.base_,b)))return!0;aA(a),az(a)}return!!(a.copy_[b]===c&&(void 0!==c||b in a.copy_)||Number.isNaN(c)&&Number.isNaN(a.copy_[b]))||(a.copy_[b]=c,a.assigned_[b]=!0,!0)},deleteProperty:(a,b)=>(void 0!==ax(a.base_,b)||b in a.base_?(a.assigned_[b]=!1,aA(a),az(a)):delete a.assigned_[b],a.copy_&&delete a.copy_[b],!0),getOwnPropertyDescriptor(a,b){let c=af(a),d=Reflect.getOwnPropertyDescriptor(c,b);return d?{writable:!0,configurable:1!==a.type_||"length"!==b,enumerable:d.enumerable,value:c[b]}:d},defineProperty(){V(11)},getPrototypeOf:a=>W(a.base_),setPrototypeOf(){V(12)}},aw={};function ax(a,b){let c=a[U];return(c?af(c):a)[b]}function ay(a,b){if(!(b in a))return;let c=W(a);for(;c;){let a=Object.getOwnPropertyDescriptor(c,b);if(a)return a;c=W(c)}}function az(a){!a.modified_&&(a.modified_=!0,a.parent_&&az(a.parent_))}function aA(a){a.copy_||(a.copy_=ag(a.base_,a.scope_.immer_.useStrictShallowCopy_))}function aB(a,b){let c=ad(a)?al("MapSet").proxyMap_(a,b):ae(a)?al("MapSet").proxySet_(a,b):function(a,b){let c=Array.isArray(a),d={type_:+!!c,scope_:b?b.scope_:gU,modified_:!1,finalized_:!1,assigned_:{},parent_:b,base_:a,draft_:null,copy_:null,revoke_:null,isManual_:!1},e=d,f=av;c&&(e=[d],f=aw);let{revoke:g,proxy:h}=Proxy.revocable(e,f);return d.draft_=h,d.revoke_=g,h}(a,b);return(b?b.scope_:gU).drafts_.push(c),c}function aC(a){return X(a)||V(10,a),function a(b){let c;if(!Y(b)||aj(b))return b;let d=b[U];if(d){if(!d.modified_)return d.base_;d.finalized_=!0,c=ag(b,d.scope_.immer_.useStrictShallowCopy_)}else c=ag(b,!0);return _(c,(b,d)=>{ac(c,b,a(d))}),d&&(d.finalized_=!1),c}(a)}_(av,(a,b)=>{aw[a]=function(){return arguments[0]=arguments[0][0],b.apply(this,arguments)}}),aw.deleteProperty=function(a,b){return aw.set.call(this,a,b,void 0)},aw.set=function(a,b,c){return av.set.call(this,a[0],b,c,a[0])};var aD=new class{constructor(a){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(a,b,c)=>{let d;if("function"==typeof a&&"function"!=typeof b){let c=b;b=a;let d=this;return function(a=c,...e){return d.produce(a,a=>b.call(this,a,...e))}}if("function"!=typeof b&&V(6),void 0!==c&&"function"!=typeof c&&V(7),Y(a)){let e=ap(this),f=aB(a,void 0),g=!0;try{d=b(f),g=!1}finally{g?an(e):ao(e)}return am(e,c),ar(d,e)}if(a&&"object"==typeof a)V(1,a);else{if(void 0===(d=b(a))&&(d=a),d===S&&(d=void 0),this.autoFreeze_&&ah(d,!0),c){let b=[],e=[];al("Patches").generateReplacementPatches_(a,d,b,e),c(b,e)}return d}},this.produceWithPatches=(a,b)=>{let c,d;return"function"==typeof a?(b,...c)=>this.produceWithPatches(b,b=>a(b,...c)):[this.produce(a,b,(a,b)=>{c=a,d=b}),c,d]},"boolean"==typeof a?.autoFreeze&&this.setAutoFreeze(a.autoFreeze),"boolean"==typeof a?.useStrictShallowCopy&&this.setUseStrictShallowCopy(a.useStrictShallowCopy)}createDraft(a){Y(a)||V(8),X(a)&&(a=aC(a));let b=ap(this),c=aB(a,void 0);return c[U].isManual_=!0,ao(b),c}finishDraft(a,b){let c=a&&a[U];c&&c.isManual_||V(9);let{scope_:d}=c;return am(d,b),ar(void 0,d)}setAutoFreeze(a){this.autoFreeze_=a}setUseStrictShallowCopy(a){this.useStrictShallowCopy_=a}applyPatches(a,b){let c;for(c=b.length-1;c>=0;c--){let d=b[c];if(0===d.path.length&&"replace"===d.op){a=d.value;break}}c>-1&&(b=b.slice(c+1));let d=al("Patches").applyPatches_;return X(a)?d(a,b):this.produce(a,a=>d(a,b))}},aE=aD.produce;aD.produceWithPatches.bind(aD),aD.setAutoFreeze.bind(aD),aD.setUseStrictShallowCopy.bind(aD),aD.applyPatches.bind(aD),aD.createDraft.bind(aD),aD.finishDraft.bind(aD);var aF="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?O:O.apply(null,arguments)};function aG(a,b){function c(...d){if(b){let c=b(...d);if(!c)throw Error(bj(0));return{type:a,payload:c.payload,..."meta"in c&&{meta:c.meta},..."error"in c&&{error:c.error}}}return{type:a,payload:d[0]}}return c.toString=()=>`${a}`,c.type=a,c.match=b=>P(b)&&b.type===a,c}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var aH=class a extends Array{constructor(...b){super(...b),Object.setPrototypeOf(this,a.prototype)}static get[Symbol.species](){return a}concat(...a){return super.concat.apply(this,a)}prepend(...b){return 1===b.length&&Array.isArray(b[0])?new a(...b[0].concat(this)):new a(...b.concat(this))}};function aI(a){return Y(a)?aE(a,()=>{}):a}function aJ(a,b,c){return a.has(b)?a.get(b):a.set(b,c(b)).get(b)}var aK=a=>b=>{setTimeout(b,a)};function aL(a){let b,c={},d=[],e={addCase(a,b){let d="string"==typeof a?a:a.type;if(!d)throw Error(bj(28));if(d in c)throw Error(bj(29));return c[d]=b,e},addMatcher:(a,b)=>(d.push({matcher:a,reducer:b}),e),addDefaultCase:a=>(b=a,e)};return a(e),[c,d,b]}var aM=Symbol.for("rtk-slice-createasyncthunk"),aN=(a=>(a.reducer="reducer",a.reducerWithPrepare="reducerWithPrepare",a.asyncThunk="asyncThunk",a))(aN||{}),aO=function({creators:a}={}){let b=a?.asyncThunk?.[aM];return function(a){let c,{name:d,reducerPath:e=d}=a;if(!d)throw Error(bj(11));let f=("function"==typeof a.reducers?a.reducers(function(){function a(a,b){return{_reducerDefinitionType:"asyncThunk",payloadCreator:a,...b}}return a.withTypes=()=>a,{reducer:a=>Object.assign({[a.name]:(...b)=>a(...b)}[a.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(a,b)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:a,reducer:b}),asyncThunk:a}}()):a.reducers)||{},g=Object.keys(f),h={},i={},j={},k=[],l={addCase(a,b){let c="string"==typeof a?a:a.type;if(!c)throw Error(bj(12));if(c in i)throw Error(bj(13));return i[c]=b,l},addMatcher:(a,b)=>(k.push({matcher:a,reducer:b}),l),exposeAction:(a,b)=>(j[a]=b,l),exposeCaseReducer:(a,b)=>(h[a]=b,l)};function m(){let[b={},c=[],d]="function"==typeof a.extraReducers?aL(a.extraReducers):[a.extraReducers],e={...b,...i};return function(a,b){let c,[d,e,f]=aL(b);if("function"==typeof a)c=()=>aI(a());else{let b=aI(a);c=()=>b}function g(a=c(),b){let h=[d[b.type],...e.filter(({matcher:a})=>a(b)).map(({reducer:a})=>a)];return 0===h.filter(a=>!!a).length&&(h=[f]),h.reduce((a,c)=>{if(c)if(X(a)){let d=c(a,b);return void 0===d?a:d}else{if(Y(a))return aE(a,a=>c(a,b));let d=c(a,b);if(void 0===d){if(null===a)return a;throw Error("A case reducer on a non-draftable value must not return undefined")}return d}return a},a)}return g.getInitialState=c,g}(a.initialState,a=>{for(let b in e)a.addCase(b,e[b]);for(let b of k)a.addMatcher(b.matcher,b.reducer);for(let b of c)a.addMatcher(b.matcher,b.reducer);d&&a.addDefaultCase(d)})}g.forEach(c=>{let e=f[c],g={reducerName:c,type:`${d}/${c}`,createNotation:"function"==typeof a.reducers};"asyncThunk"===e._reducerDefinitionType?function({type:a,reducerName:b},c,d,e){if(!e)throw Error(bj(18));let{payloadCreator:f,fulfilled:g,pending:h,rejected:i,settled:j,options:k}=c,l=e(a,f,k);d.exposeAction(b,l),g&&d.addCase(l.fulfilled,g),h&&d.addCase(l.pending,h),i&&d.addCase(l.rejected,i),j&&d.addMatcher(l.settled,j),d.exposeCaseReducer(b,{fulfilled:g||aP,pending:h||aP,rejected:i||aP,settled:j||aP})}(g,e,l,b):function({type:a,reducerName:b,createNotation:c},d,e){let f,g;if("reducer"in d){if(c&&"reducerWithPrepare"!==d._reducerDefinitionType)throw Error(bj(17));f=d.reducer,g=d.prepare}else f=d;e.addCase(a,f).exposeCaseReducer(b,f).exposeAction(b,g?aG(a,g):aG(a))}(g,e,l)});let n=a=>a,o=new Map,p=new WeakMap;function q(a,b){return c||(c=m()),c(a,b)}function r(){return c||(c=m()),c.getInitialState()}function s(b,c=!1){function d(a){let e=a[b];return void 0===e&&c&&(e=aJ(p,d,r)),e}function e(b=n){let d=aJ(o,c,()=>new WeakMap);return aJ(d,b,()=>{let d={};for(let[e,f]of Object.entries(a.selectors??{}))d[e]=function(a,b,c,d){function e(f,...g){let h=b(f);return void 0===h&&d&&(h=c()),a(h,...g)}return e.unwrapped=a,e}(f,b,()=>aJ(p,b,r),c);return d})}return{reducerPath:b,getSelectors:e,get selectors(){return e(d)},selectSlice:d}}let t={name:d,reducer:q,actions:j,caseReducers:h,getInitialState:r,...s(e),injectInto(a,{reducerPath:b,...c}={}){let d=b??e;return a.inject({reducerPath:d,reducer:q},c),{...t,...s(d,!0)}}};return t}}();function aP(){}var aQ="listener",aR="completed",aS="cancelled",aT=`task-${aS}`,aU=`task-${aR}`,aV=`${aQ}-${aS}`,aW=`${aQ}-${aR}`,aX=class{constructor(a){this.code=a,this.message=`task ${aS} (reason: ${a})`}name="TaskAbortError";message},aY=(a,b)=>{if("function"!=typeof a)throw TypeError(bj(32))},aZ=()=>{},a$=(a,b=aZ)=>(a.catch(b),a),a_=(a,b)=>(a.addEventListener("abort",b,{once:!0}),()=>a.removeEventListener("abort",b)),a0=(a,b)=>{let c=a.signal;c.aborted||("reason"in c||Object.defineProperty(c,"reason",{enumerable:!0,value:b,configurable:!0,writable:!0}),a.abort(b))},a1=a=>{if(a.aborted){let{reason:b}=a;throw new aX(b)}};function a2(a,b){let c=aZ;return new Promise((d,e)=>{let f=()=>e(new aX(a.reason));if(a.aborted)return void f();c=a_(a,f),b.finally(()=>c()).then(d,e)}).finally(()=>{c=aZ})}var a3=async(a,b)=>{try{await Promise.resolve();let b=await a();return{status:"ok",value:b}}catch(a){return{status:a instanceof aX?"cancelled":"rejected",error:a}}finally{b?.()}},a4=a=>b=>a$(a2(a,b).then(b=>(a1(a),b))),a5=a=>{let b=a4(a);return a=>b(new Promise(b=>setTimeout(b,a)))},{assign:a6}=Object,a7={},a8="listenerMiddleware",a9=a=>{let{type:b,actionCreator:c,matcher:d,predicate:e,effect:f}=a;if(b)e=aG(b).match;else if(c)b=c.type,e=c.match;else if(d)e=d;else if(e);else throw Error(bj(21));return aY(f,"options.listener"),{predicate:e,type:b,effect:f}},ba=a6(a=>{let{type:b,predicate:c,effect:d}=a9(a);return{id:((a=21)=>{let b="",c=a;for(;c--;)b+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return b})(),effect:d,type:b,predicate:c,pending:new Set,unsubscribe:()=>{throw Error(bj(22))}}},{withTypes:()=>ba}),bb=(a,b)=>{let{type:c,effect:d,predicate:e}=a9(b);return Array.from(a.values()).find(a=>("string"==typeof c?a.type===c:a.predicate===e)&&a.effect===d)},bc=a=>{a.pending.forEach(a=>{a0(a,aV)})},bd=(a,b,c)=>{try{a(b,c)}catch(a){setTimeout(()=>{throw a},0)}},be=a6(aG(`${a8}/add`),{withTypes:()=>be}),bf=aG(`${a8}/removeAll`),bg=a6(aG(`${a8}/remove`),{withTypes:()=>bg}),bh=(...a)=>{console.error(`${a8}/error`,...a)},bi=(a={})=>{let b=new Map,{extra:c,onError:d=bh}=a;aY(d,"onError");let e=a=>(a=>(a.unsubscribe=()=>b.delete(a.id),b.set(a.id,a),b=>{a.unsubscribe(),b?.cancelActive&&bc(a)}))(bb(b,a)??ba(a));a6(e,{withTypes:()=>e});let f=a=>{let c=bb(b,a);return c&&(c.unsubscribe(),a.cancelActive&&bc(c)),!!c};a6(f,{withTypes:()=>f});let g=async(a,f,g,h)=>{let i=new AbortController,j=((a,b)=>{let c=async(c,d)=>{a1(b);let e=()=>{},f=[new Promise((b,d)=>{let f=a({predicate:c,effect:(a,c)=>{c.unsubscribe(),b([a,c.getState(),c.getOriginalState()])}});e=()=>{f(),d()}})];null!=d&&f.push(new Promise(a=>setTimeout(a,d,null)));try{let a=await a2(b,Promise.race(f));return a1(b),a}finally{e()}};return(a,b)=>a$(c(a,b))})(e,i.signal),k=[];try{a.pending.add(i),await Promise.resolve(a.effect(f,a6({},g,{getOriginalState:h,condition:(a,b)=>j(a,b).then(Boolean),take:j,delay:a5(i.signal),pause:a4(i.signal),extra:c,signal:i.signal,fork:((a,b)=>(c,d)=>{aY(c,"taskExecutor");let e=new AbortController;a_(a,()=>a0(e,a.reason));let f=a3(async()=>{a1(a),a1(e.signal);let b=await c({pause:a4(e.signal),delay:a5(e.signal),signal:e.signal});return a1(e.signal),b},()=>a0(e,aU));return d?.autoJoin&&b.push(f.catch(aZ)),{result:a4(a)(f),cancel(){a0(e,aT)}}})(i.signal,k),unsubscribe:a.unsubscribe,subscribe:()=>{b.set(a.id,a)},cancelActiveListeners:()=>{a.pending.forEach((a,b,c)=>{a!==i&&(a0(a,aV),c.delete(a))})},cancel:()=>{a0(i,aV),a.pending.delete(i)},throwIfCancelled:()=>{a1(i.signal)}})))}catch(a){a instanceof aX||bd(d,a,{raisedBy:"effect"})}finally{await Promise.all(k),a0(i,aW),a.pending.delete(i)}},h=(a=>()=>{a.forEach(bc),a.clear()})(b);return{middleware:a=>c=>i=>{let j;if(!P(i))return c(i);if(be.match(i))return e(i.payload);if(bf.match(i))return void h();if(bg.match(i))return f(i.payload);let k=a.getState(),l=()=>{if(k===a7)throw Error(bj(23));return k};try{if(j=c(i),b.size>0){let c=a.getState();for(let e of Array.from(b.values())){let b=!1;try{b=e.predicate(i,c,k)}catch(a){b=!1,bd(d,a,{raisedBy:"predicate"})}b&&g(e,i,a,l)}}}finally{k=a7}return j},startListening:e,stopListening:f,clearListeners:h}};function bj(a){return`Minified Redux Toolkit error #${a}; visit https://redux-toolkit.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}function bk(a,b){if(b){var c=Number.parseInt(b,10);if(!t(c))return null==a?void 0:a[c]}}Symbol.for("rtk-state-proxy-original");var bl=aO({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:a=>{null==a.eventEmitter&&(a.eventEmitter=Symbol("rechartsEventEmitter"))}}}),bm=bl.reducer,{createEventEmitter:bn}=bl.actions;c(6895);var bo={notify(){},get:()=>[]},bp="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,bq="undefined"!=typeof navigator&&"ReactNative"===navigator.product,br=bp||bq?n.useLayoutEffect:n.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var bs=Symbol.for("react-redux-context"),bt="undefined"!=typeof globalThis?globalThis:{},bu=function(){if(!n.createContext)return{};let a=bt[bs]??=new Map,b=a.get(n.createContext);return b||(b=n.createContext(null),a.set(n.createContext,b)),b}(),bv=function(a){let{children:b,context:c,serverState:d,store:e}=a,f=n.useMemo(()=>{let a=function(a,b){let c,d=bo,e=0,f=!1;function g(){j.onStateChange&&j.onStateChange()}function h(){if(e++,!c){let b,e;c=a.subscribe(g),b=null,e=null,d={clear(){b=null,e=null},notify(){let a=b;for(;a;)a.callback(),a=a.next},get(){let a=[],c=b;for(;c;)a.push(c),c=c.next;return a},subscribe(a){let c=!0,d=e={callback:a,next:null,prev:e};return d.prev?d.prev.next=d:b=d,function(){c&&null!==b&&(c=!1,d.next?d.next.prev=d.prev:e=d.prev,d.prev?d.prev.next=d.next:b=d.next)}}}}}function i(){e--,c&&0===e&&(c(),c=void 0,d.clear(),d=bo)}let j={addNestedSub:function(a){h();let b=d.subscribe(a),c=!1;return()=>{c||(c=!0,b(),i())}},notifyNestedSubs:function(){d.notify()},handleChangeWrapper:g,isSubscribed:function(){return f},trySubscribe:function(){f||(f=!0,h())},tryUnsubscribe:function(){f&&(f=!1,i())},getListeners:()=>d};return j}(e);return{store:e,subscription:a,getServerState:d?()=>d:void 0}},[e,d]),g=n.useMemo(()=>e.getState(),[e]);return br(()=>{let{subscription:a}=f;return a.onStateChange=a.notifyNestedSubs,a.trySubscribe(),g!==e.getState()&&a.notifyNestedSubs(),()=>{a.tryUnsubscribe(),a.onStateChange=void 0}},[f,g]),n.createElement((c||bu).Provider,{value:f},b)},bw={active:!1,index:null,dataKey:void 0,coordinate:void 0},bx=aO({name:"tooltip",initialState:{itemInteraction:{click:bw,hover:bw},axisInteraction:{click:bw,hover:bw},keyboardInteraction:bw,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(a,b){a.tooltipItemPayloads.push(b.payload)},removeTooltipEntrySettings(a,b){var c=aC(a).tooltipItemPayloads.indexOf(b.payload);c>-1&&a.tooltipItemPayloads.splice(c,1)},setTooltipSettingsState(a,b){a.settings=b.payload},setActiveMouseOverItemIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.itemInteraction.hover.active=!0,a.itemInteraction.hover.index=b.payload.activeIndex,a.itemInteraction.hover.dataKey=b.payload.activeDataKey,a.itemInteraction.hover.coordinate=b.payload.activeCoordinate},mouseLeaveChart(a){a.itemInteraction.hover.active=!1,a.axisInteraction.hover.active=!1},mouseLeaveItem(a){a.itemInteraction.hover.active=!1},setActiveClickItemIndex(a,b){a.syncInteraction.active=!1,a.itemInteraction.click.active=!0,a.keyboardInteraction.active=!1,a.itemInteraction.click.index=b.payload.activeIndex,a.itemInteraction.click.dataKey=b.payload.activeDataKey,a.itemInteraction.click.coordinate=b.payload.activeCoordinate},setMouseOverAxisIndex(a,b){a.syncInteraction.active=!1,a.axisInteraction.hover.active=!0,a.keyboardInteraction.active=!1,a.axisInteraction.hover.index=b.payload.activeIndex,a.axisInteraction.hover.dataKey=b.payload.activeDataKey,a.axisInteraction.hover.coordinate=b.payload.activeCoordinate},setMouseClickAxisIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.axisInteraction.click.active=!0,a.axisInteraction.click.index=b.payload.activeIndex,a.axisInteraction.click.dataKey=b.payload.activeDataKey,a.axisInteraction.click.coordinate=b.payload.activeCoordinate},setSyncInteraction(a,b){a.syncInteraction=b.payload},setKeyboardInteraction(a,b){a.keyboardInteraction.active=b.payload.active,a.keyboardInteraction.index=b.payload.activeIndex,a.keyboardInteraction.coordinate=b.payload.activeCoordinate,a.keyboardInteraction.dataKey=b.payload.activeDataKey}}}),{addTooltipEntrySettings:by,removeTooltipEntrySettings:bz,setTooltipSettingsState:bA,setActiveMouseOverItemIndex:bB,mouseLeaveItem:bC,mouseLeaveChart:bD,setActiveClickItemIndex:bE,setMouseOverAxisIndex:bF,setMouseClickAxisIndex:bG,setSyncInteraction:bH,setKeyboardInteraction:bI}=bx.actions,bJ=bx.reducer,bK=aO({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(a,b){if(a.chartData=b.payload,null==b.payload){a.dataStartIndex=0,a.dataEndIndex=0;return}b.payload.length>0&&a.dataEndIndex!==b.payload.length-1&&(a.dataEndIndex=b.payload.length-1)},setComputedData(a,b){a.computedData=b.payload},setDataStartEndIndexes(a,b){var{startIndex:c,endIndex:d}=b.payload;null!=c&&(a.dataStartIndex=c),null!=d&&(a.dataEndIndex=d)}}}),{setChartData:bL,setDataStartEndIndexes:bM,setComputedData:bN}=bK.actions,bO=bK.reducer,bP=aO({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(a,b){a.layoutType=b.payload},setChartSize(a,b){a.width=b.payload.width,a.height=b.payload.height},setMargin(a,b){a.margin.top=b.payload.top,a.margin.right=b.payload.right,a.margin.bottom=b.payload.bottom,a.margin.left=b.payload.left},setScale(a,b){a.scale=b.payload}}}),{setMargin:bQ,setLayout:bR,setChartSize:bS,setScale:bT}=bP.actions,bU=bP.reducer,bV=a=>Array.isArray(a)?a:[a],bW=0,bX=class{revision=bW;_value;_lastValue;_isEqual=bY;constructor(a,b=bY){this._value=this._lastValue=a,this._isEqual=b}get value(){return this._value}set value(a){this.value!==a&&(this._value=a,this.revision=++bW)}};function bY(a,b){return a===b}function bZ(a){return a instanceof bX||console.warn("Not a valid cell! ",a),a.value}var b$=(a,b)=>!1;function b_(){return function(a,b=bY){return new bX(null,b)}(0,b$)}var b0=a=>{let b=a.collectionTag;null===b&&(b=a.collectionTag=b_()),bZ(b)};Symbol();var b1=0,b2=Object.getPrototypeOf({}),b3=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy(this,b4);tag=b_();tags={};children={};collectionTag=null;id=b1++},b4={get:(a,b)=>(function(){let{value:c}=a,d=Reflect.get(c,b);if("symbol"==typeof b||b in b2)return d;if("object"==typeof d&&null!==d){var e;let c=a.children[b];return void 0===c&&(c=a.children[b]=Array.isArray(e=d)?new b5(e):new b3(e)),c.tag&&bZ(c.tag),c.proxy}{let c=a.tags[b];return void 0===c&&((c=a.tags[b]=b_()).value=d),bZ(c),d}})(),ownKeys:a=>(b0(a),Reflect.ownKeys(a.value)),getOwnPropertyDescriptor:(a,b)=>Reflect.getOwnPropertyDescriptor(a.value,b),has:(a,b)=>Reflect.has(a.value,b)},b5=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy([this],b6);tag=b_();tags={};children={};collectionTag=null;id=b1++},b6={get:([a],b)=>("length"===b&&b0(a),b4.get(a,b)),ownKeys:([a])=>b4.ownKeys(a),getOwnPropertyDescriptor:([a],b)=>b4.getOwnPropertyDescriptor(a,b),has:([a],b)=>b4.has(a,b)},b7="undefined"!=typeof WeakRef?WeakRef:class{constructor(a){this.value=a}deref(){return this.value}};function b8(){return{s:0,v:void 0,o:null,p:null}}function b9(a,b={}){let c,d=b8(),{resultEqualityCheck:e}=b,f=0;function g(){let b,g=d,{length:h}=arguments;for(let a=0;a<h;a++){let b=arguments[a];if("function"==typeof b||"object"==typeof b&&null!==b){let a=g.o;null===a&&(g.o=a=new WeakMap);let c=a.get(b);void 0===c?(g=b8(),a.set(b,g)):g=c}else{let a=g.p;null===a&&(g.p=a=new Map);let c=a.get(b);void 0===c?(g=b8(),a.set(b,g)):g=c}}let i=g;if(1===g.s)b=g.v;else if(b=a.apply(null,arguments),f++,e){let a=c?.deref?.()??c;null!=a&&e(a,b)&&(b=a,0!==f&&f--),c="object"==typeof b&&null!==b||"function"==typeof b?new b7(b):b}return i.s=1,i.v=b,b}return g.clearCache=()=>{d=b8(),g.resetResultsCount()},g.resultsCount=()=>f,g.resetResultsCount=()=>{f=0},g}var ca=function(a,...b){let c="function"==typeof a?{memoize:a,memoizeOptions:b}:a,d=(...a)=>{let b,d=0,e=0,f={},g=a.pop();"object"==typeof g&&(f=g,g=a.pop()),function(a,b=`expected a function, instead received ${typeof a}`){if("function"!=typeof a)throw TypeError(b)}(g,`createSelector expects an output function after the inputs, but received: [${typeof g}]`);let{memoize:h,memoizeOptions:i=[],argsMemoize:j=b9,argsMemoizeOptions:k=[],devModeChecks:l={}}={...c,...f},m=bV(i),n=bV(k),o=function(a){let b=Array.isArray(a[0])?a[0]:a;return!function(a,b="expected all items to be functions, instead received the following types: "){if(!a.every(a=>"function"==typeof a)){let c=a.map(a=>"function"==typeof a?`function ${a.name||"unnamed"}()`:typeof a).join(", ");throw TypeError(`${b}[${c}]`)}}(b,"createSelector expects all input-selectors to be functions, but received the following types: "),b}(a),p=h(function(){return d++,g.apply(null,arguments)},...m);return Object.assign(j(function(){e++;let a=function(a,b){let c=[],{length:d}=a;for(let e=0;e<d;e++)c.push(a[e].apply(null,b));return c}(o,arguments);return b=p.apply(null,a)},...n),{resultFunc:g,memoizedResultFunc:p,dependencies:o,dependencyRecomputations:()=>e,resetDependencyRecomputations:()=>{e=0},lastResult:()=>b,recomputations:()=>d,resetRecomputations:()=>{d=0},memoize:h,argsMemoize:j})};return Object.assign(d,{withTypes:()=>d}),d}(b9),cb=Object.assign((a,b=ca)=>{!function(a,b=`expected an object, instead received ${typeof a}`){if("object"!=typeof a)throw TypeError(b)}(a,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof a}`);let c=Object.keys(a);return b(c.map(b=>a[b]),(...a)=>a.reduce((a,b,d)=>(a[c[d]]=b,a),{}))},{withTypes:()=>cb}),cc=c(39733),cd=(0,n.createContext)(null),ce=a=>a,cf=()=>{var a=(0,n.useContext)(cd);return a?a.store.dispatch:ce},cg=()=>{},ch=()=>cg,ci=(a,b)=>a===b;function cj(a){var b=(0,n.useContext)(cd);return(0,cc.useSyncExternalStoreWithSelector)(b?b.subscription.addNestedSub:ch,b?b.store.getState:cg,b?b.store.getState:cg,b?a:cg,ci)}var ck=c(10687),cl=c.n(ck),cm=a=>a.legend.settings;function cn(a,b){if((e=a.length)>1)for(var c,d,e,f=1,g=a[b[0]],h=g.length;f<e;++f)for(d=g,g=a[b[f]],c=0;c<h;++c)g[c][1]+=g[c][0]=isNaN(d[c][1])?d[c][0]:d[c][1]}function co(a){return"object"==typeof a&&"length"in a?a:Array.from(a)}function cp(a){return function(){return a}}function cq(a){for(var b=a.length,c=Array(b);--b>=0;)c[b]=b;return c}function cr(a,b){return a[b]}function cs(a){let b=[];return b.key=a,b}function ct(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cu(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ct(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ct(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}ca([a=>a.legend.payload,cm],(a,b)=>{var{itemSorter:c}=b,d=a.flat(1);return c?cl()(d,c):d}),Array.prototype.slice;var cv=Math.PI/180,cw=(a,b,c,d)=>({x:a+Math.cos(-cv*d)*c,y:b+Math.sin(-cv*d)*c}),cx=function(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(a-(c.left||0)-(c.right||0)),Math.abs(b-(c.top||0)-(c.bottom||0)))/2};function cy(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cz(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cy(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cy(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function cA(a,b,c){return null==a||null==b?c:w(b)?r()(a,b,c):"function"==typeof b?b(a):c}var cB=(a,b)=>"horizontal"===a&&"xAxis"===b||"vertical"===a&&"yAxis"===b||"centric"===a&&"angleAxis"===b||"radial"===a&&"radiusAxis"===b,cC=(a,b,c,d)=>{if(d)return a.map(a=>a.coordinate);var e,f,g=a.map(a=>(a.coordinate===b&&(e=!0),a.coordinate===c&&(f=!0),a.coordinate));return e||g.push(b),f||g.push(c),g},cD=(a,b,c)=>{if(!a)return null;var{duplicateDomain:d,type:e,range:f,scale:g,realScaleType:h,isCategorical:i,categoricalDomain:j,tickCount:k,ticks:l,niceTicks:m,axisType:n}=a;if(!g)return null;var o="scaleBand"===h&&g.bandwidth?g.bandwidth()/2:2,p=(b||c)&&"category"===e&&g.bandwidth?g.bandwidth()/o:0;return(p="angleAxis"===n&&f&&f.length>=2?2*s(f[0]-f[1])*p:p,b&&(l||m))?(l||m||[]).map((a,b)=>({coordinate:g(d?d.indexOf(a):a)+p,value:a,offset:p,index:b})).filter(a=>!t(a.coordinate)):i&&j?j.map((a,b)=>({coordinate:g(a)+p,value:a,index:b,offset:p})):g.ticks&&!c&&null!=k?g.ticks(k).map((a,b)=>({coordinate:g(a)+p,value:a,offset:p,index:b})):g.domain().map((a,b)=>({coordinate:g(a)+p,value:d?d[a]:a,index:b,offset:p}))},cE={sign:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0,g=0;g<b;++g){var h=t(a[g][c][1])?a[g][c][0]:a[g][c][1];h>=0?(a[g][c][0]=e,a[g][c][1]=e+h,e=a[g][c][1]):(a[g][c][0]=f,a[g][c][1]=f+h,f=a[g][c][1])}},expand:function(a,b){if((d=a.length)>0){for(var c,d,e,f=0,g=a[0].length;f<g;++f){for(e=c=0;c<d;++c)e+=a[c][f][1]||0;if(e)for(c=0;c<d;++c)a[c][f][1]/=e}cn(a,b)}},none:cn,silhouette:function(a,b){if((c=a.length)>0){for(var c,d=0,e=a[b[0]],f=e.length;d<f;++d){for(var g=0,h=0;g<c;++g)h+=a[g][d][1]||0;e[d][1]+=e[d][0]=-h/2}cn(a,b)}},wiggle:function(a,b){if((e=a.length)>0&&(d=(c=a[b[0]]).length)>0){for(var c,d,e,f=0,g=1;g<d;++g){for(var h=0,i=0,j=0;h<e;++h){for(var k=a[b[h]],l=k[g][1]||0,m=(l-(k[g-1][1]||0))/2,n=0;n<h;++n){var o=a[b[n]];m+=(o[g][1]||0)-(o[g-1][1]||0)}i+=l,j+=m*l}c[g-1][1]+=c[g-1][0]=f,i&&(f-=j/i)}c[g-1][1]+=c[g-1][0]=f,cn(a,b)}},positive:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0;f<b;++f){var g=t(a[f][c][1])?a[f][c][0]:a[f][c][1];g>=0?(a[f][c][0]=e,a[f][c][1]=e+g,e=a[f][c][1]):(a[f][c][0]=0,a[f][c][1]=0)}}};function cF(a){var{axis:b,ticks:c,bandSize:d,entry:e,index:f,dataKey:g}=a;if("category"===b.type){if(!b.allowDuplicatedCategory&&b.dataKey&&null!=e[b.dataKey]){var h=C(c,"value",e[b.dataKey]);if(h)return h.coordinate+d/2}return c[f]?c[f].coordinate+d/2:null}var i=cA(e,null==g?b.dataKey:g);return null==i?null:b.scale(i)}var cG=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,cH=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,cI=(a,b,c)=>{if(a&&a.scale&&a.scale.bandwidth){var d=a.scale.bandwidth();if(!c||d>0)return d}if(a&&b&&b.length>=2){for(var e=cl()(b,a=>a.coordinate),f=1/0,g=1,h=e.length;g<h;g++){var i=e[g],j=e[g-1];f=Math.min((i.coordinate||0)-(j.coordinate||0),f)}return f===1/0?0:f}return c?void 0:0};function cJ(a){var{tooltipEntrySettings:b,dataKey:c,payload:d,value:e,name:f}=a;return cz(cz({},b),{},{dataKey:c,payload:d,value:e,name:f})}function cK(a,b){return a?String(a):"string"==typeof b?b:void 0}var cL=a=>a.layout.width,cM=a=>a.layout.height,cN=a=>a.layout.scale,cO=a=>a.layout.margin,cP=ca(a=>a.cartesianAxis.xAxis,a=>Object.values(a)),cQ=ca(a=>a.cartesianAxis.yAxis,a=>Object.values(a)),cR="data-recharts-item-index",cS="data-recharts-item-data-key";function cT(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cU(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cT(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cT(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var cV=ca([cL,cM,cO,a=>a.brush.height,cP,cQ,cm,a=>a.legend.size],(a,b,c,d,e,f,g,h)=>{var i=f.reduce((a,b)=>{var{orientation:c}=b;if(!b.mirror&&!b.hide){var d="number"==typeof b.width?b.width:60;return cU(cU({},a),{},{[c]:a[c]+d})}return a},{left:c.left||0,right:c.right||0}),j=e.reduce((a,b)=>{var{orientation:c}=b;return b.mirror||b.hide?a:cU(cU({},a),{},{[c]:r()(a,"".concat(c))+b.height})},{top:c.top||0,bottom:c.bottom||0}),k=cU(cU({},j),i),l=k.bottom;k.bottom+=d;var m=a-(k=((a,b,c)=>{if(b&&c){var{width:d,height:e}=c,{align:f,verticalAlign:g,layout:h}=b;if(("vertical"===h||"horizontal"===h&&"middle"===g)&&"center"!==f&&v(a[f]))return cz(cz({},a),{},{[f]:a[f]+(d||0)});if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==g&&v(a[g]))return cz(cz({},a),{},{[g]:a[g]+(e||0)})}return a})(k,g,h)).left-k.right,n=b-k.top-k.bottom;return cU(cU({brushBottom:l},k),{},{width:Math.max(m,0),height:Math.max(n,0)})}),cW=ca(cV,a=>({x:a.left,y:a.top,width:a.width,height:a.height})),cX=ca(cL,cM,(a,b)=>({x:0,y:0,width:a,height:b})),cY=(0,n.createContext)(null),cZ=()=>null!=(0,n.useContext)(cY),c$=a=>a.brush,c_=ca([c$,cV,cO],(a,b,c)=>({height:a.height,x:v(a.x)?a.x:b.left,y:v(a.y)?a.y:b.top+b.height+b.brushBottom-((null==c?void 0:c.bottom)||0),width:v(a.width)?a.width:b.width})),c0=()=>{var a,b=cZ(),c=cj(cW),d=cj(c_),e=null==(a=cj(c$))?void 0:a.padding;return b&&d&&e?{width:d.width-e.left-e.right,height:d.height-e.top-e.bottom,x:e.left,y:e.top}:c},c1={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},c2=()=>{var a;return null!=(a=cj(cV))?a:c1},c3=()=>cj(cL),c4=()=>cj(cM),c5=a=>a.layout.layoutType,c6=()=>cj(c5),c7=c(30921),c8=c.n(c7);function c9(a,b){switch(arguments.length){case 0:break;case 1:this.range(a);break;default:this.range(b).domain(a)}return this}function da(a,b){switch(arguments.length){case 0:break;case 1:"function"==typeof a?this.interpolator(a):this.range(a);break;default:this.domain(a),"function"==typeof b?this.interpolator(b):this.range(b)}return this}class db extends Map{constructor(a,b=dd){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:b}}),null!=a)for(let[b,c]of a)this.set(b,c)}get(a){return super.get(dc(this,a))}has(a){return super.has(dc(this,a))}set(a,b){return super.set(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):(a.set(d,c),c)}(this,a),b)}delete(a){return super.delete(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)&&(c=a.get(d),a.delete(d)),c}(this,a))}}function dc({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):c}function dd(a){return null!==a&&"object"==typeof a?a.valueOf():a}let de=Symbol("implicit");function df(){var a=new db,b=[],c=[],d=de;function e(e){let f=a.get(e);if(void 0===f){if(d!==de)return d;a.set(e,f=b.push(e)-1)}return c[f%c.length]}return e.domain=function(c){if(!arguments.length)return b.slice();for(let d of(b=[],a=new db,c))a.has(d)||a.set(d,b.push(d)-1);return e},e.range=function(a){return arguments.length?(c=Array.from(a),e):c.slice()},e.unknown=function(a){return arguments.length?(d=a,e):d},e.copy=function(){return df(b,c).unknown(d)},c9.apply(e,arguments),e}function dg(){var a,b,c=df().unknown(void 0),d=c.domain,e=c.range,f=0,g=1,h=!1,i=0,j=0,k=.5;function l(){var c=d().length,l=g<f,m=l?g:f,n=l?f:g;a=(n-m)/Math.max(1,c-i+2*j),h&&(a=Math.floor(a)),m+=(n-m-a*(c-i))*k,b=a*(1-i),h&&(m=Math.round(m),b=Math.round(b));var o=(function(a,b,c){a*=1,b*=1,c=(e=arguments.length)<2?(b=a,a=0,1):e<3?1:+c;for(var d=-1,e=0|Math.max(0,Math.ceil((b-a)/c)),f=Array(e);++d<e;)f[d]=a+d*c;return f})(c).map(function(b){return m+a*b});return e(l?o.reverse():o)}return delete c.unknown,c.domain=function(a){return arguments.length?(d(a),l()):d()},c.range=function(a){return arguments.length?([f,g]=a,f*=1,g*=1,l()):[f,g]},c.rangeRound=function(a){return[f,g]=a,f*=1,g*=1,h=!0,l()},c.bandwidth=function(){return b},c.step=function(){return a},c.round=function(a){return arguments.length?(h=!!a,l()):h},c.padding=function(a){return arguments.length?(i=Math.min(1,j=+a),l()):i},c.paddingInner=function(a){return arguments.length?(i=Math.min(1,a),l()):i},c.paddingOuter=function(a){return arguments.length?(j=+a,l()):j},c.align=function(a){return arguments.length?(k=Math.max(0,Math.min(1,a)),l()):k},c.copy=function(){return dg(d(),[f,g]).round(h).paddingInner(i).paddingOuter(j).align(k)},c9.apply(l(),arguments)}function dh(){return function a(b){var c=b.copy;return b.padding=b.paddingOuter,delete b.paddingInner,delete b.paddingOuter,b.copy=function(){return a(c())},b}(dg.apply(null,arguments).paddingInner(1))}let di=Math.sqrt(50),dj=Math.sqrt(10),dk=Math.sqrt(2);function dl(a,b,c){let d,e,f,g=(b-a)/Math.max(0,c),h=Math.floor(Math.log10(g)),i=g/Math.pow(10,h),j=i>=di?10:i>=dj?5:i>=dk?2:1;return(h<0?(d=Math.round(a*(f=Math.pow(10,-h)/j)),e=Math.round(b*f),d/f<a&&++d,e/f>b&&--e,f=-f):(d=Math.round(a/(f=Math.pow(10,h)*j)),e=Math.round(b/f),d*f<a&&++d,e*f>b&&--e),e<d&&.5<=c&&c<2)?dl(a,b,2*c):[d,e,f]}function dm(a,b,c){if(b*=1,a*=1,!((c*=1)>0))return[];if(a===b)return[a];let d=b<a,[e,f,g]=d?dl(b,a,c):dl(a,b,c);if(!(f>=e))return[];let h=f-e+1,i=Array(h);if(d)if(g<0)for(let a=0;a<h;++a)i[a]=-((f-a)/g);else for(let a=0;a<h;++a)i[a]=(f-a)*g;else if(g<0)for(let a=0;a<h;++a)i[a]=-((e+a)/g);else for(let a=0;a<h;++a)i[a]=(e+a)*g;return i}function dn(a,b,c){return dl(a*=1,b*=1,c*=1)[2]}function dp(a,b,c){b*=1,a*=1,c*=1;let d=b<a,e=d?dn(b,a,c):dn(a,b,c);return(d?-1:1)*(e<0?-(1/e):e)}function dq(a,b){return null==a||null==b?NaN:a<b?-1:a>b?1:a>=b?0:NaN}function dr(a,b){return null==a||null==b?NaN:b<a?-1:b>a?1:b>=a?0:NaN}function ds(a){let b,c,d;function e(a,d,f=0,g=a.length){if(f<g){if(0!==b(d,d))return g;do{let b=f+g>>>1;0>c(a[b],d)?f=b+1:g=b}while(f<g)}return f}return 2!==a.length?(b=dq,c=(b,c)=>dq(a(b),c),d=(b,c)=>a(b)-c):(b=a===dq||a===dr?a:dt,c=a,d=a),{left:e,center:function(a,b,c=0,f=a.length){let g=e(a,b,c,f-1);return g>c&&d(a[g-1],b)>-d(a[g],b)?g-1:g},right:function(a,d,e=0,f=a.length){if(e<f){if(0!==b(d,d))return f;do{let b=e+f>>>1;0>=c(a[b],d)?e=b+1:f=b}while(e<f)}return e}}}function dt(){return 0}function du(a){return null===a?NaN:+a}let dv=ds(dq),dw=dv.right;function dx(a,b,c){a.prototype=b.prototype=c,c.constructor=a}function dy(a,b){var c=Object.create(a.prototype);for(var d in b)c[d]=b[d];return c}function dz(){}dv.left,ds(du).center;var dA="\\s*([+-]?\\d+)\\s*",dB="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",dC="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",dD=/^#([0-9a-f]{3,8})$/,dE=RegExp(`^rgb\\(${dA},${dA},${dA}\\)$`),dF=RegExp(`^rgb\\(${dC},${dC},${dC}\\)$`),dG=RegExp(`^rgba\\(${dA},${dA},${dA},${dB}\\)$`),dH=RegExp(`^rgba\\(${dC},${dC},${dC},${dB}\\)$`),dI=RegExp(`^hsl\\(${dB},${dC},${dC}\\)$`),dJ=RegExp(`^hsla\\(${dB},${dC},${dC},${dB}\\)$`),dK={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function dL(){return this.rgb().formatHex()}function dM(){return this.rgb().formatRgb()}function dN(a){var b,c;return a=(a+"").trim().toLowerCase(),(b=dD.exec(a))?(c=b[1].length,b=parseInt(b[1],16),6===c?dO(b):3===c?new dR(b>>8&15|b>>4&240,b>>4&15|240&b,(15&b)<<4|15&b,1):8===c?dP(b>>24&255,b>>16&255,b>>8&255,(255&b)/255):4===c?dP(b>>12&15|b>>8&240,b>>8&15|b>>4&240,b>>4&15|240&b,((15&b)<<4|15&b)/255):null):(b=dE.exec(a))?new dR(b[1],b[2],b[3],1):(b=dF.exec(a))?new dR(255*b[1]/100,255*b[2]/100,255*b[3]/100,1):(b=dG.exec(a))?dP(b[1],b[2],b[3],b[4]):(b=dH.exec(a))?dP(255*b[1]/100,255*b[2]/100,255*b[3]/100,b[4]):(b=dI.exec(a))?dX(b[1],b[2]/100,b[3]/100,1):(b=dJ.exec(a))?dX(b[1],b[2]/100,b[3]/100,b[4]):dK.hasOwnProperty(a)?dO(dK[a]):"transparent"===a?new dR(NaN,NaN,NaN,0):null}function dO(a){return new dR(a>>16&255,a>>8&255,255&a,1)}function dP(a,b,c,d){return d<=0&&(a=b=c=NaN),new dR(a,b,c,d)}function dQ(a,b,c,d){var e;return 1==arguments.length?((e=a)instanceof dz||(e=dN(e)),e)?new dR((e=e.rgb()).r,e.g,e.b,e.opacity):new dR:new dR(a,b,c,null==d?1:d)}function dR(a,b,c,d){this.r=+a,this.g=+b,this.b=+c,this.opacity=+d}function dS(){return`#${dW(this.r)}${dW(this.g)}${dW(this.b)}`}function dT(){let a=dU(this.opacity);return`${1===a?"rgb(":"rgba("}${dV(this.r)}, ${dV(this.g)}, ${dV(this.b)}${1===a?")":`, ${a})`}`}function dU(a){return isNaN(a)?1:Math.max(0,Math.min(1,a))}function dV(a){return Math.max(0,Math.min(255,Math.round(a)||0))}function dW(a){return((a=dV(a))<16?"0":"")+a.toString(16)}function dX(a,b,c,d){return d<=0?a=b=c=NaN:c<=0||c>=1?a=b=NaN:b<=0&&(a=NaN),new dZ(a,b,c,d)}function dY(a){if(a instanceof dZ)return new dZ(a.h,a.s,a.l,a.opacity);if(a instanceof dz||(a=dN(a)),!a)return new dZ;if(a instanceof dZ)return a;var b=(a=a.rgb()).r/255,c=a.g/255,d=a.b/255,e=Math.min(b,c,d),f=Math.max(b,c,d),g=NaN,h=f-e,i=(f+e)/2;return h?(g=b===f?(c-d)/h+(c<d)*6:c===f?(d-b)/h+2:(b-c)/h+4,h/=i<.5?f+e:2-f-e,g*=60):h=i>0&&i<1?0:g,new dZ(g,h,i,a.opacity)}function dZ(a,b,c,d){this.h=+a,this.s=+b,this.l=+c,this.opacity=+d}function d$(a){return(a=(a||0)%360)<0?a+360:a}function d_(a){return Math.max(0,Math.min(1,a||0))}function d0(a,b,c){return(a<60?b+(c-b)*a/60:a<180?c:a<240?b+(c-b)*(240-a)/60:b)*255}function d1(a,b,c,d,e){var f=a*a,g=f*a;return((1-3*a+3*f-g)*b+(4-6*f+3*g)*c+(1+3*a+3*f-3*g)*d+g*e)/6}dx(dz,dN,{copy(a){return Object.assign(new this.constructor,this,a)},displayable(){return this.rgb().displayable()},hex:dL,formatHex:dL,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return dY(this).formatHsl()},formatRgb:dM,toString:dM}),dx(dR,dQ,dy(dz,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new dR(this.r*a,this.g*a,this.b*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new dR(this.r*a,this.g*a,this.b*a,this.opacity)},rgb(){return this},clamp(){return new dR(dV(this.r),dV(this.g),dV(this.b),dU(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:dS,formatHex:dS,formatHex8:function(){return`#${dW(this.r)}${dW(this.g)}${dW(this.b)}${dW((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:dT,toString:dT})),dx(dZ,function(a,b,c,d){return 1==arguments.length?dY(a):new dZ(a,b,c,null==d?1:d)},dy(dz,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new dZ(this.h,this.s,this.l*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new dZ(this.h,this.s,this.l*a,this.opacity)},rgb(){var a=this.h%360+(this.h<0)*360,b=isNaN(a)||isNaN(this.s)?0:this.s,c=this.l,d=c+(c<.5?c:1-c)*b,e=2*c-d;return new dR(d0(a>=240?a-240:a+120,e,d),d0(a,e,d),d0(a<120?a+240:a-120,e,d),this.opacity)},clamp(){return new dZ(d$(this.h),d_(this.s),d_(this.l),dU(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let a=dU(this.opacity);return`${1===a?"hsl(":"hsla("}${d$(this.h)}, ${100*d_(this.s)}%, ${100*d_(this.l)}%${1===a?")":`, ${a})`}`}}));let d2=a=>()=>a;function d3(a,b){var c=b-a;return c?function(b){return a+b*c}:d2(isNaN(a)?b:a)}let d4=function a(b){var c,d=1==(c=+b)?d3:function(a,b){var d,e,f;return b-a?(d=a,e=b,d=Math.pow(d,f=c),e=Math.pow(e,f)-d,f=1/f,function(a){return Math.pow(d+a*e,f)}):d2(isNaN(a)?b:a)};function e(a,b){var c=d((a=dQ(a)).r,(b=dQ(b)).r),e=d(a.g,b.g),f=d(a.b,b.b),g=d3(a.opacity,b.opacity);return function(b){return a.r=c(b),a.g=e(b),a.b=f(b),a.opacity=g(b),a+""}}return e.gamma=a,e}(1);function d5(a){return function(b){var c,d,e=b.length,f=Array(e),g=Array(e),h=Array(e);for(c=0;c<e;++c)d=dQ(b[c]),f[c]=d.r||0,g[c]=d.g||0,h[c]=d.b||0;return f=a(f),g=a(g),h=a(h),d.opacity=1,function(a){return d.r=f(a),d.g=g(a),d.b=h(a),d+""}}}function d6(a,b){return a*=1,b*=1,function(c){return a*(1-c)+b*c}}d5(function(a){var b=a.length-1;return function(c){var d=c<=0?c=0:c>=1?(c=1,b-1):Math.floor(c*b),e=a[d],f=a[d+1],g=d>0?a[d-1]:2*e-f,h=d<b-1?a[d+2]:2*f-e;return d1((c-d/b)*b,g,e,f,h)}}),d5(function(a){var b=a.length;return function(c){var d=Math.floor(((c%=1)<0?++c:c)*b),e=a[(d+b-1)%b],f=a[d%b],g=a[(d+1)%b],h=a[(d+2)%b];return d1((c-d/b)*b,e,f,g,h)}});var d7=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,d8=RegExp(d7.source,"g");function d9(a,b){var c,d,e=typeof b;return null==b||"boolean"===e?d2(b):("number"===e?d6:"string"===e?(d=dN(b))?(b=d,d4):function(a,b){var c,d,e,f,g,h=d7.lastIndex=d8.lastIndex=0,i=-1,j=[],k=[];for(a+="",b+="";(e=d7.exec(a))&&(f=d8.exec(b));)(g=f.index)>h&&(g=b.slice(h,g),j[i]?j[i]+=g:j[++i]=g),(e=e[0])===(f=f[0])?j[i]?j[i]+=f:j[++i]=f:(j[++i]=null,k.push({i:i,x:d6(e,f)})),h=d8.lastIndex;return h<b.length&&(g=b.slice(h),j[i]?j[i]+=g:j[++i]=g),j.length<2?k[0]?(c=k[0].x,function(a){return c(a)+""}):(d=b,function(){return d}):(b=k.length,function(a){for(var c,d=0;d<b;++d)j[(c=k[d]).i]=c.x(a);return j.join("")})}:b instanceof dN?d4:b instanceof Date?function(a,b){var c=new Date;return a*=1,b*=1,function(d){return c.setTime(a*(1-d)+b*d),c}}:!ArrayBuffer.isView(c=b)||c instanceof DataView?Array.isArray(b)?function(a,b){var c,d=b?b.length:0,e=a?Math.min(d,a.length):0,f=Array(e),g=Array(d);for(c=0;c<e;++c)f[c]=d9(a[c],b[c]);for(;c<d;++c)g[c]=b[c];return function(a){for(c=0;c<e;++c)g[c]=f[c](a);return g}}:"function"!=typeof b.valueOf&&"function"!=typeof b.toString||isNaN(b)?function(a,b){var c,d={},e={};for(c in(null===a||"object"!=typeof a)&&(a={}),(null===b||"object"!=typeof b)&&(b={}),b)c in a?d[c]=d9(a[c],b[c]):e[c]=b[c];return function(a){for(c in d)e[c]=d[c](a);return e}}:d6:function(a,b){b||(b=[]);var c,d=a?Math.min(b.length,a.length):0,e=b.slice();return function(f){for(c=0;c<d;++c)e[c]=a[c]*(1-f)+b[c]*f;return e}})(a,b)}function ea(a,b){return a*=1,b*=1,function(c){return Math.round(a*(1-c)+b*c)}}function eb(a){return+a}var ec=[0,1];function ed(a){return a}function ee(a,b){var c;return(b-=a*=1)?function(c){return(c-a)/b}:(c=isNaN(b)?NaN:.5,function(){return c})}function ef(a,b,c){var d=a[0],e=a[1],f=b[0],g=b[1];return e<d?(d=ee(e,d),f=c(g,f)):(d=ee(d,e),f=c(f,g)),function(a){return f(d(a))}}function eg(a,b,c){var d=Math.min(a.length,b.length)-1,e=Array(d),f=Array(d),g=-1;for(a[d]<a[0]&&(a=a.slice().reverse(),b=b.slice().reverse());++g<d;)e[g]=ee(a[g],a[g+1]),f[g]=c(b[g],b[g+1]);return function(b){var c=dw(a,b,1,d)-1;return f[c](e[c](b))}}function eh(a,b){return b.domain(a.domain()).range(a.range()).interpolate(a.interpolate()).clamp(a.clamp()).unknown(a.unknown())}function ei(){var a,b,c,d,e,f,g=ec,h=ec,i=d9,j=ed;function k(){var a,b,c,i=Math.min(g.length,h.length);return j!==ed&&(a=g[0],b=g[i-1],a>b&&(c=a,a=b,b=c),j=function(c){return Math.max(a,Math.min(b,c))}),d=i>2?eg:ef,e=f=null,l}function l(b){return null==b||isNaN(b*=1)?c:(e||(e=d(g.map(a),h,i)))(a(j(b)))}return l.invert=function(c){return j(b((f||(f=d(h,g.map(a),d6)))(c)))},l.domain=function(a){return arguments.length?(g=Array.from(a,eb),k()):g.slice()},l.range=function(a){return arguments.length?(h=Array.from(a),k()):h.slice()},l.rangeRound=function(a){return h=Array.from(a),i=ea,k()},l.clamp=function(a){return arguments.length?(j=!!a||ed,k()):j!==ed},l.interpolate=function(a){return arguments.length?(i=a,k()):i},l.unknown=function(a){return arguments.length?(c=a,l):c},function(c,d){return a=c,b=d,k()}}function ej(){return ei()(ed,ed)}var ek=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function el(a){var b;if(!(b=ek.exec(a)))throw Error("invalid format: "+a);return new em({fill:b[1],align:b[2],sign:b[3],symbol:b[4],zero:b[5],width:b[6],comma:b[7],precision:b[8]&&b[8].slice(1),trim:b[9],type:b[10]})}function em(a){this.fill=void 0===a.fill?" ":a.fill+"",this.align=void 0===a.align?">":a.align+"",this.sign=void 0===a.sign?"-":a.sign+"",this.symbol=void 0===a.symbol?"":a.symbol+"",this.zero=!!a.zero,this.width=void 0===a.width?void 0:+a.width,this.comma=!!a.comma,this.precision=void 0===a.precision?void 0:+a.precision,this.trim=!!a.trim,this.type=void 0===a.type?"":a.type+""}function en(a,b){if((c=(a=b?a.toExponential(b-1):a.toExponential()).indexOf("e"))<0)return null;var c,d=a.slice(0,c);return[d.length>1?d[0]+d.slice(2):d,+a.slice(c+1)]}function eo(a){return(a=en(Math.abs(a)))?a[1]:NaN}function ep(a,b){var c=en(a,b);if(!c)return a+"";var d=c[0],e=c[1];return e<0?"0."+Array(-e).join("0")+d:d.length>e+1?d.slice(0,e+1)+"."+d.slice(e+1):d+Array(e-d.length+2).join("0")}el.prototype=em.prototype,em.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let eq={"%":(a,b)=>(100*a).toFixed(b),b:a=>Math.round(a).toString(2),c:a=>a+"",d:function(a){return Math.abs(a=Math.round(a))>=1e21?a.toLocaleString("en").replace(/,/g,""):a.toString(10)},e:(a,b)=>a.toExponential(b),f:(a,b)=>a.toFixed(b),g:(a,b)=>a.toPrecision(b),o:a=>Math.round(a).toString(8),p:(a,b)=>ep(100*a,b),r:ep,s:function(a,b){var c=en(a,b);if(!c)return a+"";var d=c[0],e=c[1],f=e-(gV=3*Math.max(-8,Math.min(8,Math.floor(e/3))))+1,g=d.length;return f===g?d:f>g?d+Array(f-g+1).join("0"):f>0?d.slice(0,f)+"."+d.slice(f):"0."+Array(1-f).join("0")+en(a,Math.max(0,b+f-1))[0]},X:a=>Math.round(a).toString(16).toUpperCase(),x:a=>Math.round(a).toString(16)};function er(a){return a}var es=Array.prototype.map,et=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eu(a,b,c,d){var e,f,g=dp(a,b,c);switch((d=el(null==d?",f":d)).type){case"s":var h=Math.max(Math.abs(a),Math.abs(b));return null!=d.precision||isNaN(f=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(eo(h)/3)))-eo(Math.abs(g))))||(d.precision=f),gY(d,h);case"":case"e":case"g":case"p":case"r":null!=d.precision||isNaN(f=Math.max(0,eo(Math.abs(Math.max(Math.abs(a),Math.abs(b)))-(e=Math.abs(e=g)))-eo(e))+1)||(d.precision=f-("e"===d.type));break;case"f":case"%":null!=d.precision||isNaN(f=Math.max(0,-eo(Math.abs(g))))||(d.precision=f-("%"===d.type)*2)}return gX(d)}function ev(a){var b=a.domain;return a.ticks=function(a){var c=b();return dm(c[0],c[c.length-1],null==a?10:a)},a.tickFormat=function(a,c){var d=b();return eu(d[0],d[d.length-1],null==a?10:a,c)},a.nice=function(c){null==c&&(c=10);var d,e,f=b(),g=0,h=f.length-1,i=f[g],j=f[h],k=10;for(j<i&&(e=i,i=j,j=e,e=g,g=h,h=e);k-- >0;){if((e=dn(i,j,c))===d)return f[g]=i,f[h]=j,b(f);if(e>0)i=Math.floor(i/e)*e,j=Math.ceil(j/e)*e;else if(e<0)i=Math.ceil(i*e)/e,j=Math.floor(j*e)/e;else break;d=e}return a},a}function ew(a,b){a=a.slice();var c,d=0,e=a.length-1,f=a[d],g=a[e];return g<f&&(c=d,d=e,e=c,c=f,f=g,g=c),a[d]=b.floor(f),a[e]=b.ceil(g),a}function ex(a){return Math.log(a)}function ey(a){return Math.exp(a)}function ez(a){return-Math.log(-a)}function eA(a){return-Math.exp(-a)}function eB(a){return isFinite(a)?+("1e"+a):a<0?0:a}function eC(a){return(b,c)=>-a(-b,c)}function eD(a){let b,c,d=a(ex,ey),e=d.domain,f=10;function g(){var g,h;return b=(g=f)===Math.E?Math.log:10===g&&Math.log10||2===g&&Math.log2||(g=Math.log(g),a=>Math.log(a)/g),c=10===(h=f)?eB:h===Math.E?Math.exp:a=>Math.pow(h,a),e()[0]<0?(b=eC(b),c=eC(c),a(ez,eA)):a(ex,ey),d}return d.base=function(a){return arguments.length?(f=+a,g()):f},d.domain=function(a){return arguments.length?(e(a),g()):e()},d.ticks=a=>{let d,g,h=e(),i=h[0],j=h[h.length-1],k=j<i;k&&([i,j]=[j,i]);let l=b(i),m=b(j),n=null==a?10:+a,o=[];if(!(f%1)&&m-l<n){if(l=Math.floor(l),m=Math.ceil(m),i>0){for(;l<=m;++l)for(d=1;d<f;++d)if(!((g=l<0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}}else for(;l<=m;++l)for(d=f-1;d>=1;--d)if(!((g=l>0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}2*o.length<n&&(o=dm(i,j,n))}else o=dm(l,m,Math.min(m-l,n)).map(c);return k?o.reverse():o},d.tickFormat=(a,e)=>{if(null==a&&(a=10),null==e&&(e=10===f?"s":","),"function"!=typeof e&&(f%1||null!=(e=el(e)).precision||(e.trim=!0),e=gX(e)),a===1/0)return e;let g=Math.max(1,f*a/d.ticks().length);return a=>{let d=a/c(Math.round(b(a)));return d*f<f-.5&&(d*=f),d<=g?e(a):""}},d.nice=()=>e(ew(e(),{floor:a=>c(Math.floor(b(a))),ceil:a=>c(Math.ceil(b(a)))})),d}function eE(a){return function(b){return Math.sign(b)*Math.log1p(Math.abs(b/a))}}function eF(a){return function(b){return Math.sign(b)*Math.expm1(Math.abs(b))*a}}function eG(a){var b=1,c=a(eE(1),eF(b));return c.constant=function(c){return arguments.length?a(eE(b=+c),eF(b)):b},ev(c)}function eH(a){return function(b){return b<0?-Math.pow(-b,a):Math.pow(b,a)}}function eI(a){return a<0?-Math.sqrt(-a):Math.sqrt(a)}function eJ(a){return a<0?-a*a:a*a}function eK(a){var b=a(ed,ed),c=1;return b.exponent=function(b){return arguments.length?1==(c=+b)?a(ed,ed):.5===c?a(eI,eJ):a(eH(c),eH(1/c)):c},ev(b)}function eL(){var a=eK(ei());return a.copy=function(){return eh(a,eL()).exponent(a.exponent())},c9.apply(a,arguments),a}function eM(){return eL.apply(null,arguments).exponent(.5)}function eN(a){return Math.sign(a)*a*a}function eO(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c<b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c<e||void 0===c&&e>=e)&&(c=e)}return c}function eP(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c>b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c>e||void 0===c&&e>=e)&&(c=e)}return c}function eQ(a,b){return(null==a||!(a>=a))-(null==b||!(b>=b))||(a<b?-1:+(a>b))}function eR(a,b,c){let d=a[b];a[b]=a[c],a[c]=d}gX=(gW=function(a){var b,c,d,e=void 0===a.grouping||void 0===a.thousands?er:(b=es.call(a.grouping,Number),c=a.thousands+"",function(a,d){for(var e=a.length,f=[],g=0,h=b[0],i=0;e>0&&h>0&&(i+h+1>d&&(h=Math.max(1,d-i)),f.push(a.substring(e-=h,e+h)),!((i+=h+1)>d));)h=b[g=(g+1)%b.length];return f.reverse().join(c)}),f=void 0===a.currency?"":a.currency[0]+"",g=void 0===a.currency?"":a.currency[1]+"",h=void 0===a.decimal?".":a.decimal+"",i=void 0===a.numerals?er:(d=es.call(a.numerals,String),function(a){return a.replace(/[0-9]/g,function(a){return d[+a]})}),j=void 0===a.percent?"%":a.percent+"",k=void 0===a.minus?"−":a.minus+"",l=void 0===a.nan?"NaN":a.nan+"";function m(a){var b=(a=el(a)).fill,c=a.align,d=a.sign,m=a.symbol,n=a.zero,o=a.width,p=a.comma,q=a.precision,r=a.trim,s=a.type;"n"===s?(p=!0,s="g"):eq[s]||(void 0===q&&(q=12),r=!0,s="g"),(n||"0"===b&&"="===c)&&(n=!0,b="0",c="=");var t="$"===m?f:"#"===m&&/[boxX]/.test(s)?"0"+s.toLowerCase():"",u="$"===m?g:/[%p]/.test(s)?j:"",v=eq[s],w=/[defgprs%]/.test(s);function x(a){var f,g,j,m=t,x=u;if("c"===s)x=v(a)+x,a="";else{var y=(a*=1)<0||1/a<0;if(a=isNaN(a)?l:v(Math.abs(a),q),r&&(a=function(a){a:for(var b,c=a.length,d=1,e=-1;d<c;++d)switch(a[d]){case".":e=b=d;break;case"0":0===e&&(e=d),b=d;break;default:if(!+a[d])break a;e>0&&(e=0)}return e>0?a.slice(0,e)+a.slice(b+1):a}(a)),y&&0==+a&&"+"!==d&&(y=!1),m=(y?"("===d?d:k:"-"===d||"("===d?"":d)+m,x=("s"===s?et[8+gV/3]:"")+x+(y&&"("===d?")":""),w){for(f=-1,g=a.length;++f<g;)if(48>(j=a.charCodeAt(f))||j>57){x=(46===j?h+a.slice(f+1):a.slice(f))+x,a=a.slice(0,f);break}}}p&&!n&&(a=e(a,1/0));var z=m.length+a.length+x.length,A=z<o?Array(o-z+1).join(b):"";switch(p&&n&&(a=e(A+a,A.length?o-x.length:1/0),A=""),c){case"<":a=m+a+x+A;break;case"=":a=m+A+a+x;break;case"^":a=A.slice(0,z=A.length>>1)+m+a+x+A.slice(z);break;default:a=A+m+a+x}return i(a)}return q=void 0===q?6:/[gprs]/.test(s)?Math.max(1,Math.min(21,q)):Math.max(0,Math.min(20,q)),x.toString=function(){return a+""},x}return{format:m,formatPrefix:function(a,b){var c=m(((a=el(a)).type="f",a)),d=3*Math.max(-8,Math.min(8,Math.floor(eo(b)/3))),e=Math.pow(10,-d),f=et[8+d/3];return function(a){return c(e*a)+f}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,gY=gW.formatPrefix;let eS=new Date,eT=new Date;function eU(a,b,c,d){function e(b){return a(b=0==arguments.length?new Date:new Date(+b)),b}return e.floor=b=>(a(b=new Date(+b)),b),e.ceil=c=>(a(c=new Date(c-1)),b(c,1),a(c),c),e.round=a=>{let b=e(a),c=e.ceil(a);return a-b<c-a?b:c},e.offset=(a,c)=>(b(a=new Date(+a),null==c?1:Math.floor(c)),a),e.range=(c,d,f)=>{let g,h=[];if(c=e.ceil(c),f=null==f?1:Math.floor(f),!(c<d)||!(f>0))return h;do h.push(g=new Date(+c)),b(c,f),a(c);while(g<c&&c<d);return h},e.filter=c=>eU(b=>{if(b>=b)for(;a(b),!c(b);)b.setTime(b-1)},(a,d)=>{if(a>=a)if(d<0)for(;++d<=0;)for(;b(a,-1),!c(a););else for(;--d>=0;)for(;b(a,1),!c(a););}),c&&(e.count=(b,d)=>(eS.setTime(+b),eT.setTime(+d),a(eS),a(eT),Math.floor(c(eS,eT))),e.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?e.filter(d?b=>d(b)%a==0:b=>e.count(0,b)%a==0):e:null),e}let eV=eU(()=>{},(a,b)=>{a.setTime(+a+b)},(a,b)=>b-a);eV.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?eU(b=>{b.setTime(Math.floor(b/a)*a)},(b,c)=>{b.setTime(+b+c*a)},(b,c)=>(c-b)/a):eV:null,eV.range;let eW=eU(a=>{a.setTime(a-a.getMilliseconds())},(a,b)=>{a.setTime(+a+1e3*b)},(a,b)=>(b-a)/1e3,a=>a.getUTCSeconds());eW.range;let eX=eU(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds())},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getMinutes());eX.range;let eY=eU(a=>{a.setUTCSeconds(0,0)},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getUTCMinutes());eY.range;let eZ=eU(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds()-6e4*a.getMinutes())},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getHours());eZ.range;let e$=eU(a=>{a.setUTCMinutes(0,0,0)},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getUTCHours());e$.range;let e_=eU(a=>a.setHours(0,0,0,0),(a,b)=>a.setDate(a.getDate()+b),(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/864e5,a=>a.getDate()-1);e_.range;let e0=eU(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>a.getUTCDate()-1);e0.range;let e1=eU(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>Math.floor(a/864e5));function e2(a){return eU(b=>{b.setDate(b.getDate()-(b.getDay()+7-a)%7),b.setHours(0,0,0,0)},(a,b)=>{a.setDate(a.getDate()+7*b)},(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/6048e5)}e1.range;let e3=e2(0),e4=e2(1),e5=e2(2),e6=e2(3),e7=e2(4),e8=e2(5),e9=e2(6);function fa(a){return eU(b=>{b.setUTCDate(b.getUTCDate()-(b.getUTCDay()+7-a)%7),b.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+7*b)},(a,b)=>(b-a)/6048e5)}e3.range,e4.range,e5.range,e6.range,e7.range,e8.range,e9.range;let fb=fa(0),fc=fa(1),fd=fa(2),fe=fa(3),ff=fa(4),fg=fa(5),fh=fa(6);fb.range,fc.range,fd.range,fe.range,ff.range,fg.range,fh.range;let fi=eU(a=>{a.setDate(1),a.setHours(0,0,0,0)},(a,b)=>{a.setMonth(a.getMonth()+b)},(a,b)=>b.getMonth()-a.getMonth()+(b.getFullYear()-a.getFullYear())*12,a=>a.getMonth());fi.range;let fj=eU(a=>{a.setUTCDate(1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCMonth(a.getUTCMonth()+b)},(a,b)=>b.getUTCMonth()-a.getUTCMonth()+(b.getUTCFullYear()-a.getUTCFullYear())*12,a=>a.getUTCMonth());fj.range;let fk=eU(a=>{a.setMonth(0,1),a.setHours(0,0,0,0)},(a,b)=>{a.setFullYear(a.getFullYear()+b)},(a,b)=>b.getFullYear()-a.getFullYear(),a=>a.getFullYear());fk.every=a=>isFinite(a=Math.floor(a))&&a>0?eU(b=>{b.setFullYear(Math.floor(b.getFullYear()/a)*a),b.setMonth(0,1),b.setHours(0,0,0,0)},(b,c)=>{b.setFullYear(b.getFullYear()+c*a)}):null,fk.range;let fl=eU(a=>{a.setUTCMonth(0,1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCFullYear(a.getUTCFullYear()+b)},(a,b)=>b.getUTCFullYear()-a.getUTCFullYear(),a=>a.getUTCFullYear());function fm(a,b,c,d,e,f){let g=[[eW,1,1e3],[eW,5,5e3],[eW,15,15e3],[eW,30,3e4],[f,1,6e4],[f,5,3e5],[f,15,9e5],[f,30,18e5],[e,1,36e5],[e,3,108e5],[e,6,216e5],[e,12,432e5],[d,1,864e5],[d,2,1728e5],[c,1,6048e5],[b,1,2592e6],[b,3,7776e6],[a,1,31536e6]];function h(b,c,d){let e=Math.abs(c-b)/d,f=ds(([,,a])=>a).right(g,e);if(f===g.length)return a.every(dp(b/31536e6,c/31536e6,d));if(0===f)return eV.every(Math.max(dp(b,c,d),1));let[h,i]=g[e/g[f-1][2]<g[f][2]/e?f-1:f];return h.every(i)}return[function(a,b,c){let d=b<a;d&&([a,b]=[b,a]);let e=c&&"function"==typeof c.range?c:h(a,b,c),f=e?e.range(a,+b+1):[];return d?f.reverse():f},h]}fl.every=a=>isFinite(a=Math.floor(a))&&a>0?eU(b=>{b.setUTCFullYear(Math.floor(b.getUTCFullYear()/a)*a),b.setUTCMonth(0,1),b.setUTCHours(0,0,0,0)},(b,c)=>{b.setUTCFullYear(b.getUTCFullYear()+c*a)}):null,fl.range;let[fn,fo]=fm(fl,fj,fb,e1,e$,eY),[fp,fq]=fm(fk,fi,e3,e_,eZ,eX);function fr(a){if(0<=a.y&&a.y<100){var b=new Date(-1,a.m,a.d,a.H,a.M,a.S,a.L);return b.setFullYear(a.y),b}return new Date(a.y,a.m,a.d,a.H,a.M,a.S,a.L)}function fs(a){if(0<=a.y&&a.y<100){var b=new Date(Date.UTC(-1,a.m,a.d,a.H,a.M,a.S,a.L));return b.setUTCFullYear(a.y),b}return new Date(Date.UTC(a.y,a.m,a.d,a.H,a.M,a.S,a.L))}function ft(a,b,c){return{y:a,m:b,d:c,H:0,M:0,S:0,L:0}}var fu={"-":"",_:" ",0:"0"},fv=/^\s*\d+/,fw=/^%/,fx=/[\\^$*+?|[\]().{}]/g;function fy(a,b,c){var d=a<0?"-":"",e=(d?-a:a)+"",f=e.length;return d+(f<c?Array(c-f+1).join(b)+e:e)}function fz(a){return a.replace(fx,"\\$&")}function fA(a){return RegExp("^(?:"+a.map(fz).join("|")+")","i")}function fB(a){return new Map(a.map((a,b)=>[a.toLowerCase(),b]))}function fC(a,b,c){var d=fv.exec(b.slice(c,c+1));return d?(a.w=+d[0],c+d[0].length):-1}function fD(a,b,c){var d=fv.exec(b.slice(c,c+1));return d?(a.u=+d[0],c+d[0].length):-1}function fE(a,b,c){var d=fv.exec(b.slice(c,c+2));return d?(a.U=+d[0],c+d[0].length):-1}function fF(a,b,c){var d=fv.exec(b.slice(c,c+2));return d?(a.V=+d[0],c+d[0].length):-1}function fG(a,b,c){var d=fv.exec(b.slice(c,c+2));return d?(a.W=+d[0],c+d[0].length):-1}function fH(a,b,c){var d=fv.exec(b.slice(c,c+4));return d?(a.y=+d[0],c+d[0].length):-1}function fI(a,b,c){var d=fv.exec(b.slice(c,c+2));return d?(a.y=+d[0]+(+d[0]>68?1900:2e3),c+d[0].length):-1}function fJ(a,b,c){var d=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(b.slice(c,c+6));return d?(a.Z=d[1]?0:-(d[2]+(d[3]||"00")),c+d[0].length):-1}function fK(a,b,c){var d=fv.exec(b.slice(c,c+1));return d?(a.q=3*d[0]-3,c+d[0].length):-1}function fL(a,b,c){var d=fv.exec(b.slice(c,c+2));return d?(a.m=d[0]-1,c+d[0].length):-1}function fM(a,b,c){var d=fv.exec(b.slice(c,c+2));return d?(a.d=+d[0],c+d[0].length):-1}function fN(a,b,c){var d=fv.exec(b.slice(c,c+3));return d?(a.m=0,a.d=+d[0],c+d[0].length):-1}function fO(a,b,c){var d=fv.exec(b.slice(c,c+2));return d?(a.H=+d[0],c+d[0].length):-1}function fP(a,b,c){var d=fv.exec(b.slice(c,c+2));return d?(a.M=+d[0],c+d[0].length):-1}function fQ(a,b,c){var d=fv.exec(b.slice(c,c+2));return d?(a.S=+d[0],c+d[0].length):-1}function fR(a,b,c){var d=fv.exec(b.slice(c,c+3));return d?(a.L=+d[0],c+d[0].length):-1}function fS(a,b,c){var d=fv.exec(b.slice(c,c+6));return d?(a.L=Math.floor(d[0]/1e3),c+d[0].length):-1}function fT(a,b,c){var d=fw.exec(b.slice(c,c+1));return d?c+d[0].length:-1}function fU(a,b,c){var d=fv.exec(b.slice(c));return d?(a.Q=+d[0],c+d[0].length):-1}function fV(a,b,c){var d=fv.exec(b.slice(c));return d?(a.s=+d[0],c+d[0].length):-1}function fW(a,b){return fy(a.getDate(),b,2)}function fX(a,b){return fy(a.getHours(),b,2)}function fY(a,b){return fy(a.getHours()%12||12,b,2)}function fZ(a,b){return fy(1+e_.count(fk(a),a),b,3)}function f$(a,b){return fy(a.getMilliseconds(),b,3)}function f_(a,b){return f$(a,b)+"000"}function f0(a,b){return fy(a.getMonth()+1,b,2)}function f1(a,b){return fy(a.getMinutes(),b,2)}function f2(a,b){return fy(a.getSeconds(),b,2)}function f3(a){var b=a.getDay();return 0===b?7:b}function f4(a,b){return fy(e3.count(fk(a)-1,a),b,2)}function f5(a){var b=a.getDay();return b>=4||0===b?e7(a):e7.ceil(a)}function f6(a,b){return a=f5(a),fy(e7.count(fk(a),a)+(4===fk(a).getDay()),b,2)}function f7(a){return a.getDay()}function f8(a,b){return fy(e4.count(fk(a)-1,a),b,2)}function f9(a,b){return fy(a.getFullYear()%100,b,2)}function ga(a,b){return fy((a=f5(a)).getFullYear()%100,b,2)}function gb(a,b){return fy(a.getFullYear()%1e4,b,4)}function gc(a,b){var c=a.getDay();return fy((a=c>=4||0===c?e7(a):e7.ceil(a)).getFullYear()%1e4,b,4)}function gd(a){var b=a.getTimezoneOffset();return(b>0?"-":(b*=-1,"+"))+fy(b/60|0,"0",2)+fy(b%60,"0",2)}function ge(a,b){return fy(a.getUTCDate(),b,2)}function gf(a,b){return fy(a.getUTCHours(),b,2)}function gg(a,b){return fy(a.getUTCHours()%12||12,b,2)}function gh(a,b){return fy(1+e0.count(fl(a),a),b,3)}function gi(a,b){return fy(a.getUTCMilliseconds(),b,3)}function gj(a,b){return gi(a,b)+"000"}function gk(a,b){return fy(a.getUTCMonth()+1,b,2)}function gl(a,b){return fy(a.getUTCMinutes(),b,2)}function gm(a,b){return fy(a.getUTCSeconds(),b,2)}function gn(a){var b=a.getUTCDay();return 0===b?7:b}function go(a,b){return fy(fb.count(fl(a)-1,a),b,2)}function gp(a){var b=a.getUTCDay();return b>=4||0===b?ff(a):ff.ceil(a)}function gq(a,b){return a=gp(a),fy(ff.count(fl(a),a)+(4===fl(a).getUTCDay()),b,2)}function gr(a){return a.getUTCDay()}function gs(a,b){return fy(fc.count(fl(a)-1,a),b,2)}function gt(a,b){return fy(a.getUTCFullYear()%100,b,2)}function gu(a,b){return fy((a=gp(a)).getUTCFullYear()%100,b,2)}function gv(a,b){return fy(a.getUTCFullYear()%1e4,b,4)}function gw(a,b){var c=a.getUTCDay();return fy((a=c>=4||0===c?ff(a):ff.ceil(a)).getUTCFullYear()%1e4,b,4)}function gx(){return"+0000"}function gy(){return"%"}function gz(a){return+a}function gA(a){return Math.floor(a/1e3)}function gB(a){return new Date(a)}function gC(a){return a instanceof Date?+a:+new Date(+a)}function gD(a,b,c,d,e,f,g,h,i,j){var k=ej(),l=k.invert,m=k.domain,n=j(".%L"),o=j(":%S"),p=j("%I:%M"),q=j("%I %p"),r=j("%a %d"),s=j("%b %d"),t=j("%B"),u=j("%Y");function v(a){return(i(a)<a?n:h(a)<a?o:g(a)<a?p:f(a)<a?q:d(a)<a?e(a)<a?r:s:c(a)<a?t:u)(a)}return k.invert=function(a){return new Date(l(a))},k.domain=function(a){return arguments.length?m(Array.from(a,gC)):m().map(gB)},k.ticks=function(b){var c=m();return a(c[0],c[c.length-1],null==b?10:b)},k.tickFormat=function(a,b){return null==b?v:j(b)},k.nice=function(a){var c=m();return a&&"function"==typeof a.range||(a=b(c[0],c[c.length-1],null==a?10:a)),a?m(ew(c,a)):k},k.copy=function(){return eh(k,gD(a,b,c,d,e,f,g,h,i,j))},k}function gE(){return c9.apply(gD(fp,fq,fk,fi,e3,e_,eZ,eX,eW,g$).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function gF(){return c9.apply(gD(fn,fo,fl,fj,fb,e0,e$,eY,eW,g_).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function gG(){var a,b,c,d,e,f=0,g=1,h=ed,i=!1;function j(b){return null==b||isNaN(b*=1)?e:h(0===c?.5:(b=(d(b)-a)*c,i?Math.max(0,Math.min(1,b)):b))}function k(a){return function(b){var c,d;return arguments.length?([c,d]=b,h=a(c,d),j):[h(0),h(1)]}}return j.domain=function(e){return arguments.length?([f,g]=e,a=d(f*=1),b=d(g*=1),c=a===b?0:1/(b-a),j):[f,g]},j.clamp=function(a){return arguments.length?(i=!!a,j):i},j.interpolator=function(a){return arguments.length?(h=a,j):h},j.range=k(d9),j.rangeRound=k(ea),j.unknown=function(a){return arguments.length?(e=a,j):e},function(e){return d=e,a=e(f),b=e(g),c=a===b?0:1/(b-a),j}}function gH(a,b){return b.domain(a.domain()).interpolator(a.interpolator()).clamp(a.clamp()).unknown(a.unknown())}function gI(){var a=eK(gG());return a.copy=function(){return gH(a,gI()).exponent(a.exponent())},da.apply(a,arguments)}function gJ(){return gI.apply(null,arguments).exponent(.5)}function gK(){var a,b,c,d,e,f,g,h=0,i=.5,j=1,k=1,l=ed,m=!1;function n(a){return isNaN(a*=1)?g:(a=.5+((a=+f(a))-b)*(k*a<k*b?d:e),l(m?Math.max(0,Math.min(1,a)):a))}function o(a){return function(b){var c,d,e;return arguments.length?([c,d,e]=b,l=function(a,b){void 0===b&&(b=a,a=d9);for(var c=0,d=b.length-1,e=b[0],f=Array(d<0?0:d);c<d;)f[c]=a(e,e=b[++c]);return function(a){var b=Math.max(0,Math.min(d-1,Math.floor(a*=d)));return f[b](a-b)}}(a,[c,d,e]),n):[l(0),l(.5),l(1)]}}return n.domain=function(g){return arguments.length?([h,i,j]=g,a=f(h*=1),b=f(i*=1),c=f(j*=1),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n):[h,i,j]},n.clamp=function(a){return arguments.length?(m=!!a,n):m},n.interpolator=function(a){return arguments.length?(l=a,n):l},n.range=o(d9),n.rangeRound=o(ea),n.unknown=function(a){return arguments.length?(g=a,n):g},function(g){return f=g,a=g(h),b=g(i),c=g(j),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n}}function gL(){var a=eK(gK());return a.copy=function(){return gH(a,gL()).exponent(a.exponent())},da.apply(a,arguments)}function gM(){return gL.apply(null,arguments).exponent(.5)}g$=(gZ=function(a){var b=a.dateTime,c=a.date,d=a.time,e=a.periods,f=a.days,g=a.shortDays,h=a.months,i=a.shortMonths,j=fA(e),k=fB(e),l=fA(f),m=fB(f),n=fA(g),o=fB(g),p=fA(h),q=fB(h),r=fA(i),s=fB(i),t={a:function(a){return g[a.getDay()]},A:function(a){return f[a.getDay()]},b:function(a){return i[a.getMonth()]},B:function(a){return h[a.getMonth()]},c:null,d:fW,e:fW,f:f_,g:ga,G:gc,H:fX,I:fY,j:fZ,L:f$,m:f0,M:f1,p:function(a){return e[+(a.getHours()>=12)]},q:function(a){return 1+~~(a.getMonth()/3)},Q:gz,s:gA,S:f2,u:f3,U:f4,V:f6,w:f7,W:f8,x:null,X:null,y:f9,Y:gb,Z:gd,"%":gy},u={a:function(a){return g[a.getUTCDay()]},A:function(a){return f[a.getUTCDay()]},b:function(a){return i[a.getUTCMonth()]},B:function(a){return h[a.getUTCMonth()]},c:null,d:ge,e:ge,f:gj,g:gu,G:gw,H:gf,I:gg,j:gh,L:gi,m:gk,M:gl,p:function(a){return e[+(a.getUTCHours()>=12)]},q:function(a){return 1+~~(a.getUTCMonth()/3)},Q:gz,s:gA,S:gm,u:gn,U:go,V:gq,w:gr,W:gs,x:null,X:null,y:gt,Y:gv,Z:gx,"%":gy},v={a:function(a,b,c){var d=n.exec(b.slice(c));return d?(a.w=o.get(d[0].toLowerCase()),c+d[0].length):-1},A:function(a,b,c){var d=l.exec(b.slice(c));return d?(a.w=m.get(d[0].toLowerCase()),c+d[0].length):-1},b:function(a,b,c){var d=r.exec(b.slice(c));return d?(a.m=s.get(d[0].toLowerCase()),c+d[0].length):-1},B:function(a,b,c){var d=p.exec(b.slice(c));return d?(a.m=q.get(d[0].toLowerCase()),c+d[0].length):-1},c:function(a,c,d){return y(a,b,c,d)},d:fM,e:fM,f:fS,g:fI,G:fH,H:fO,I:fO,j:fN,L:fR,m:fL,M:fP,p:function(a,b,c){var d=j.exec(b.slice(c));return d?(a.p=k.get(d[0].toLowerCase()),c+d[0].length):-1},q:fK,Q:fU,s:fV,S:fQ,u:fD,U:fE,V:fF,w:fC,W:fG,x:function(a,b,d){return y(a,c,b,d)},X:function(a,b,c){return y(a,d,b,c)},y:fI,Y:fH,Z:fJ,"%":fT};function w(a,b){return function(c){var d,e,f,g=[],h=-1,i=0,j=a.length;for(c instanceof Date||(c=new Date(+c));++h<j;)37===a.charCodeAt(h)&&(g.push(a.slice(i,h)),null!=(e=fu[d=a.charAt(++h)])?d=a.charAt(++h):e="e"===d?" ":"0",(f=b[d])&&(d=f(c,e)),g.push(d),i=h+1);return g.push(a.slice(i,h)),g.join("")}}function x(a,b){return function(c){var d,e,f=ft(1900,void 0,1);if(y(f,a,c+="",0)!=c.length)return null;if("Q"in f)return new Date(f.Q);if("s"in f)return new Date(1e3*f.s+("L"in f?f.L:0));if(!b||"Z"in f||(f.Z=0),"p"in f&&(f.H=f.H%12+12*f.p),void 0===f.m&&(f.m="q"in f?f.q:0),"V"in f){if(f.V<1||f.V>53)return null;"w"in f||(f.w=1),"Z"in f?(d=(e=(d=fs(ft(f.y,0,1))).getUTCDay())>4||0===e?fc.ceil(d):fc(d),d=e0.offset(d,(f.V-1)*7),f.y=d.getUTCFullYear(),f.m=d.getUTCMonth(),f.d=d.getUTCDate()+(f.w+6)%7):(d=(e=(d=fr(ft(f.y,0,1))).getDay())>4||0===e?e4.ceil(d):e4(d),d=e_.offset(d,(f.V-1)*7),f.y=d.getFullYear(),f.m=d.getMonth(),f.d=d.getDate()+(f.w+6)%7)}else("W"in f||"U"in f)&&("w"in f||(f.w="u"in f?f.u%7:+("W"in f)),e="Z"in f?fs(ft(f.y,0,1)).getUTCDay():fr(ft(f.y,0,1)).getDay(),f.m=0,f.d="W"in f?(f.w+6)%7+7*f.W-(e+5)%7:f.w+7*f.U-(e+6)%7);return"Z"in f?(f.H+=f.Z/100|0,f.M+=f.Z%100,fs(f)):fr(f)}}function y(a,b,c,d){for(var e,f,g=0,h=b.length,i=c.length;g<h;){if(d>=i)return -1;if(37===(e=b.charCodeAt(g++))){if(!(f=v[(e=b.charAt(g++))in fu?b.charAt(g++):e])||(d=f(a,c,d))<0)return -1}else if(e!=c.charCodeAt(d++))return -1}return d}return t.x=w(c,t),t.X=w(d,t),t.c=w(b,t),u.x=w(c,u),u.X=w(d,u),u.c=w(b,u),{format:function(a){var b=w(a+="",t);return b.toString=function(){return a},b},parse:function(a){var b=x(a+="",!1);return b.toString=function(){return a},b},utcFormat:function(a){var b=w(a+="",u);return b.toString=function(){return a},b},utcParse:function(a){var b=x(a+="",!0);return b.toString=function(){return a},b}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,gZ.parse,g_=gZ.utcFormat,gZ.utcParse;var gN=a=>a.chartData,gO=ca([gN],a=>{var b=null!=a.chartData?a.chartData.length-1:0;return{chartData:a.chartData,computedData:a.computedData,dataEndIndex:b,dataStartIndex:0}}),gP=(a,b,c,d)=>d?gO(a):gN(a);function gQ(a){return Number.isFinite(a)}function gR(a){return"number"==typeof a&&a>0&&Number.isFinite(a)}function gS(a){if(Array.isArray(a)&&2===a.length){var[b,c]=a;if(gQ(b)&&gQ(c))return!0}return!1}function gT(a,b,c){return c?a:[Math.min(a[0],b[0]),Math.max(a[1],b[1])]}var gU,gV,gW,gX,gY,gZ,g$,g_,g0,g1,g2=!0,g3="[DecimalError] ",g4=g3+"Invalid argument: ",g5=g3+"Exponent out of range: ",g6=Math.floor,g7=Math.pow,g8=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,g9=g6(1286742750677284.5),ha={};function hb(a,b){var c,d,e,f,g,h,i,j,k=a.constructor,l=k.precision;if(!a.s||!b.s)return b.s||(b=new k(a)),g2?hl(b,l):b;if(i=a.d,j=b.d,g=a.e,e=b.e,i=i.slice(),f=g-e){for(f<0?(d=i,f=-f,h=j.length):(d=j,e=g,h=i.length),f>(h=(g=Math.ceil(l/7))>h?g+1:h+1)&&(f=h,d.length=1),d.reverse();f--;)d.push(0);d.reverse()}for((h=i.length)-(f=j.length)<0&&(f=h,d=j,j=i,i=d),c=0;f;)c=(i[--f]=i[f]+j[f]+c)/1e7|0,i[f]%=1e7;for(c&&(i.unshift(c),++e),h=i.length;0==i[--h];)i.pop();return b.d=i,b.e=e,g2?hl(b,l):b}function hc(a,b,c){if(a!==~~a||a<b||a>c)throw Error(g4+a)}function hd(a){var b,c,d,e=a.length-1,f="",g=a[0];if(e>0){for(f+=g,b=1;b<e;b++)(c=7-(d=a[b]+"").length)&&(f+=hi(c)),f+=d;(c=7-(d=(g=a[b])+"").length)&&(f+=hi(c))}else if(0===g)return"0";for(;g%10==0;)g/=10;return f+g}ha.absoluteValue=ha.abs=function(){var a=new this.constructor(this);return a.s&&(a.s=1),a},ha.comparedTo=ha.cmp=function(a){var b,c,d,e;if(a=new this.constructor(a),this.s!==a.s)return this.s||-a.s;if(this.e!==a.e)return this.e>a.e^this.s<0?1:-1;for(b=0,c=(d=this.d.length)<(e=a.d.length)?d:e;b<c;++b)if(this.d[b]!==a.d[b])return this.d[b]>a.d[b]^this.s<0?1:-1;return d===e?0:d>e^this.s<0?1:-1},ha.decimalPlaces=ha.dp=function(){var a=this.d.length-1,b=(a-this.e)*7;if(a=this.d[a])for(;a%10==0;a/=10)b--;return b<0?0:b},ha.dividedBy=ha.div=function(a){return he(this,new this.constructor(a))},ha.dividedToIntegerBy=ha.idiv=function(a){var b=this.constructor;return hl(he(this,new b(a),0,1),b.precision)},ha.equals=ha.eq=function(a){return!this.cmp(a)},ha.exponent=function(){return hg(this)},ha.greaterThan=ha.gt=function(a){return this.cmp(a)>0},ha.greaterThanOrEqualTo=ha.gte=function(a){return this.cmp(a)>=0},ha.isInteger=ha.isint=function(){return this.e>this.d.length-2},ha.isNegative=ha.isneg=function(){return this.s<0},ha.isPositive=ha.ispos=function(){return this.s>0},ha.isZero=function(){return 0===this.s},ha.lessThan=ha.lt=function(a){return 0>this.cmp(a)},ha.lessThanOrEqualTo=ha.lte=function(a){return 1>this.cmp(a)},ha.logarithm=ha.log=function(a){var b,c=this.constructor,d=c.precision,e=d+5;if(void 0===a)a=new c(10);else if((a=new c(a)).s<1||a.eq(g1))throw Error(g3+"NaN");if(this.s<1)throw Error(g3+(this.s?"NaN":"-Infinity"));return this.eq(g1)?new c(0):(g2=!1,b=he(hj(this,e),hj(a,e),e),g2=!0,hl(b,d))},ha.minus=ha.sub=function(a){return a=new this.constructor(a),this.s==a.s?hm(this,a):hb(this,(a.s=-a.s,a))},ha.modulo=ha.mod=function(a){var b,c=this.constructor,d=c.precision;if(!(a=new c(a)).s)throw Error(g3+"NaN");return this.s?(g2=!1,b=he(this,a,0,1).times(a),g2=!0,this.minus(b)):hl(new c(this),d)},ha.naturalExponential=ha.exp=function(){return hf(this)},ha.naturalLogarithm=ha.ln=function(){return hj(this)},ha.negated=ha.neg=function(){var a=new this.constructor(this);return a.s=-a.s||0,a},ha.plus=ha.add=function(a){return a=new this.constructor(a),this.s==a.s?hb(this,a):hm(this,(a.s=-a.s,a))},ha.precision=ha.sd=function(a){var b,c,d;if(void 0!==a&&!!a!==a&&1!==a&&0!==a)throw Error(g4+a);if(b=hg(this)+1,c=7*(d=this.d.length-1)+1,d=this.d[d]){for(;d%10==0;d/=10)c--;for(d=this.d[0];d>=10;d/=10)c++}return a&&b>c?b:c},ha.squareRoot=ha.sqrt=function(){var a,b,c,d,e,f,g,h=this.constructor;if(this.s<1){if(!this.s)return new h(0);throw Error(g3+"NaN")}for(a=hg(this),g2=!1,0==(e=Math.sqrt(+this))||e==1/0?(((b=hd(this.d)).length+a)%2==0&&(b+="0"),e=Math.sqrt(b),a=g6((a+1)/2)-(a<0||a%2),d=new h(b=e==1/0?"5e"+a:(b=e.toExponential()).slice(0,b.indexOf("e")+1)+a)):d=new h(e.toString()),e=g=(c=h.precision)+3;;)if(d=(f=d).plus(he(this,f,g+2)).times(.5),hd(f.d).slice(0,g)===(b=hd(d.d)).slice(0,g)){if(b=b.slice(g-3,g+1),e==g&&"4999"==b){if(hl(f,c+1,0),f.times(f).eq(this)){d=f;break}}else if("9999"!=b)break;g+=4}return g2=!0,hl(d,c)},ha.times=ha.mul=function(a){var b,c,d,e,f,g,h,i,j,k=this.constructor,l=this.d,m=(a=new k(a)).d;if(!this.s||!a.s)return new k(0);for(a.s*=this.s,c=this.e+a.e,(i=l.length)<(j=m.length)&&(f=l,l=m,m=f,g=i,i=j,j=g),f=[],d=g=i+j;d--;)f.push(0);for(d=j;--d>=0;){for(b=0,e=i+d;e>d;)h=f[e]+m[d]*l[e-d-1]+b,f[e--]=h%1e7|0,b=h/1e7|0;f[e]=(f[e]+b)%1e7|0}for(;!f[--g];)f.pop();return b?++c:f.shift(),a.d=f,a.e=c,g2?hl(a,k.precision):a},ha.toDecimalPlaces=ha.todp=function(a,b){var c=this,d=c.constructor;return(c=new d(c),void 0===a)?c:(hc(a,0,1e9),void 0===b?b=d.rounding:hc(b,0,8),hl(c,a+hg(c)+1,b))},ha.toExponential=function(a,b){var c,d=this,e=d.constructor;return void 0===a?c=hn(d,!0):(hc(a,0,1e9),void 0===b?b=e.rounding:hc(b,0,8),c=hn(d=hl(new e(d),a+1,b),!0,a+1)),c},ha.toFixed=function(a,b){var c,d,e=this.constructor;return void 0===a?hn(this):(hc(a,0,1e9),void 0===b?b=e.rounding:hc(b,0,8),c=hn((d=hl(new e(this),a+hg(this)+1,b)).abs(),!1,a+hg(d)+1),this.isneg()&&!this.isZero()?"-"+c:c)},ha.toInteger=ha.toint=function(){var a=this.constructor;return hl(new a(this),hg(this)+1,a.rounding)},ha.toNumber=function(){return+this},ha.toPower=ha.pow=function(a){var b,c,d,e,f,g,h=this,i=h.constructor,j=+(a=new i(a));if(!a.s)return new i(g1);if(!(h=new i(h)).s){if(a.s<1)throw Error(g3+"Infinity");return h}if(h.eq(g1))return h;if(d=i.precision,a.eq(g1))return hl(h,d);if(g=(b=a.e)>=(c=a.d.length-1),f=h.s,g){if((c=j<0?-j:j)<=0x1fffffffffffff){for(e=new i(g1),b=Math.ceil(d/7+4),g2=!1;c%2&&ho((e=e.times(h)).d,b),0!==(c=g6(c/2));)ho((h=h.times(h)).d,b);return g2=!0,a.s<0?new i(g1).div(e):hl(e,d)}}else if(f<0)throw Error(g3+"NaN");return f=f<0&&1&a.d[Math.max(b,c)]?-1:1,h.s=1,g2=!1,e=a.times(hj(h,d+12)),g2=!0,(e=hf(e)).s=f,e},ha.toPrecision=function(a,b){var c,d,e=this,f=e.constructor;return void 0===a?(c=hg(e),d=hn(e,c<=f.toExpNeg||c>=f.toExpPos)):(hc(a,1,1e9),void 0===b?b=f.rounding:hc(b,0,8),c=hg(e=hl(new f(e),a,b)),d=hn(e,a<=c||c<=f.toExpNeg,a)),d},ha.toSignificantDigits=ha.tosd=function(a,b){var c=this.constructor;return void 0===a?(a=c.precision,b=c.rounding):(hc(a,1,1e9),void 0===b?b=c.rounding:hc(b,0,8)),hl(new c(this),a,b)},ha.toString=ha.valueOf=ha.val=ha.toJSON=ha[Symbol.for("nodejs.util.inspect.custom")]=function(){var a=hg(this),b=this.constructor;return hn(this,a<=b.toExpNeg||a>=b.toExpPos)};var he=function(){function a(a,b){var c,d=0,e=a.length;for(a=a.slice();e--;)c=a[e]*b+d,a[e]=c%1e7|0,d=c/1e7|0;return d&&a.unshift(d),a}function b(a,b,c,d){var e,f;if(c!=d)f=c>d?1:-1;else for(e=f=0;e<c;e++)if(a[e]!=b[e]){f=a[e]>b[e]?1:-1;break}return f}function c(a,b,c){for(var d=0;c--;)a[c]-=d,d=+(a[c]<b[c]),a[c]=1e7*d+a[c]-b[c];for(;!a[0]&&a.length>1;)a.shift()}return function(d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z=d.constructor,A=d.s==e.s?1:-1,B=d.d,C=e.d;if(!d.s)return new z(d);if(!e.s)throw Error(g3+"Division by zero");for(j=0,i=d.e-e.e,x=C.length,v=B.length,o=(n=new z(A)).d=[];C[j]==(B[j]||0);)++j;if(C[j]>(B[j]||0)&&--i,(s=null==f?f=z.precision:g?f+(hg(d)-hg(e))+1:f)<0)return new z(0);if(s=s/7+2|0,j=0,1==x)for(k=0,C=C[0],s++;(j<v||k)&&s--;j++)t=1e7*k+(B[j]||0),o[j]=t/C|0,k=t%C|0;else{for((k=1e7/(C[0]+1)|0)>1&&(C=a(C,k),B=a(B,k),x=C.length,v=B.length),u=x,q=(p=B.slice(0,x)).length;q<x;)p[q++]=0;(y=C.slice()).unshift(0),w=C[0],C[1]>=1e7/2&&++w;do k=0,(h=b(C,p,x,q))<0?(r=p[0],x!=q&&(r=1e7*r+(p[1]||0)),(k=r/w|0)>1?(k>=1e7&&(k=1e7-1),m=(l=a(C,k)).length,q=p.length,1==(h=b(l,p,m,q))&&(k--,c(l,x<m?y:C,m))):(0==k&&(h=k=1),l=C.slice()),(m=l.length)<q&&l.unshift(0),c(p,l,q),-1==h&&(q=p.length,(h=b(C,p,x,q))<1&&(k++,c(p,x<q?y:C,q))),q=p.length):0===h&&(k++,p=[0]),o[j++]=k,h&&p[0]?p[q++]=B[u]||0:(p=[B[u]],q=1);while((u++<v||void 0!==p[0])&&s--)}return o[0]||o.shift(),n.e=i,hl(n,g?f+hg(n)+1:f)}}();function hf(a,b){var c,d,e,f,g,h=0,i=0,j=a.constructor,k=j.precision;if(hg(a)>16)throw Error(g5+hg(a));if(!a.s)return new j(g1);for(null==b?(g2=!1,g=k):g=b,f=new j(.03125);a.abs().gte(.1);)a=a.times(f),i+=5;for(g+=Math.log(g7(2,i))/Math.LN10*2+5|0,c=d=e=new j(g1),j.precision=g;;){if(d=hl(d.times(a),g),c=c.times(++h),hd((f=e.plus(he(d,c,g))).d).slice(0,g)===hd(e.d).slice(0,g)){for(;i--;)e=hl(e.times(e),g);return j.precision=k,null==b?(g2=!0,hl(e,k)):e}e=f}}function hg(a){for(var b=7*a.e,c=a.d[0];c>=10;c/=10)b++;return b}function hh(a,b,c){if(b>a.LN10.sd())throw g2=!0,c&&(a.precision=c),Error(g3+"LN10 precision limit exceeded");return hl(new a(a.LN10),b)}function hi(a){for(var b="";a--;)b+="0";return b}function hj(a,b){var c,d,e,f,g,h,i,j,k,l=1,m=a,n=m.d,o=m.constructor,p=o.precision;if(m.s<1)throw Error(g3+(m.s?"NaN":"-Infinity"));if(m.eq(g1))return new o(0);if(null==b?(g2=!1,j=p):j=b,m.eq(10))return null==b&&(g2=!0),hh(o,j);if(o.precision=j+=10,d=(c=hd(n)).charAt(0),!(15e14>Math.abs(f=hg(m))))return i=hh(o,j+2,p).times(f+""),m=hj(new o(d+"."+c.slice(1)),j-10).plus(i),o.precision=p,null==b?(g2=!0,hl(m,p)):m;for(;d<7&&1!=d||1==d&&c.charAt(1)>3;)d=(c=hd((m=m.times(a)).d)).charAt(0),l++;for(f=hg(m),d>1?(m=new o("0."+c),f++):m=new o(d+"."+c.slice(1)),h=g=m=he(m.minus(g1),m.plus(g1),j),k=hl(m.times(m),j),e=3;;){if(g=hl(g.times(k),j),hd((i=h.plus(he(g,new o(e),j))).d).slice(0,j)===hd(h.d).slice(0,j))return h=h.times(2),0!==f&&(h=h.plus(hh(o,j+2,p).times(f+""))),h=he(h,new o(l),j),o.precision=p,null==b?(g2=!0,hl(h,p)):h;h=i,e+=2}}function hk(a,b){var c,d,e;for((c=b.indexOf("."))>-1&&(b=b.replace(".","")),(d=b.search(/e/i))>0?(c<0&&(c=d),c+=+b.slice(d+1),b=b.substring(0,d)):c<0&&(c=b.length),d=0;48===b.charCodeAt(d);)++d;for(e=b.length;48===b.charCodeAt(e-1);)--e;if(b=b.slice(d,e)){if(e-=d,a.e=g6((c=c-d-1)/7),a.d=[],d=(c+1)%7,c<0&&(d+=7),d<e){for(d&&a.d.push(+b.slice(0,d)),e-=7;d<e;)a.d.push(+b.slice(d,d+=7));d=7-(b=b.slice(d)).length}else d-=e;for(;d--;)b+="0";if(a.d.push(+b),g2&&(a.e>g9||a.e<-g9))throw Error(g5+c)}else a.s=0,a.e=0,a.d=[0];return a}function hl(a,b,c){var d,e,f,g,h,i,j,k,l=a.d;for(g=1,f=l[0];f>=10;f/=10)g++;if((d=b-g)<0)d+=7,e=b,j=l[k=0];else{if((k=Math.ceil((d+1)/7))>=(f=l.length))return a;for(g=1,j=f=l[k];f>=10;f/=10)g++;d%=7,e=d-7+g}if(void 0!==c&&(h=j/(f=g7(10,g-e-1))%10|0,i=b<0||void 0!==l[k+1]||j%f,i=c<4?(h||i)&&(0==c||c==(a.s<0?3:2)):h>5||5==h&&(4==c||i||6==c&&(d>0?e>0?j/g7(10,g-e):0:l[k-1])%10&1||c==(a.s<0?8:7))),b<1||!l[0])return i?(f=hg(a),l.length=1,b=b-f-1,l[0]=g7(10,(7-b%7)%7),a.e=g6(-b/7)||0):(l.length=1,l[0]=a.e=a.s=0),a;if(0==d?(l.length=k,f=1,k--):(l.length=k+1,f=g7(10,7-d),l[k]=e>0?(j/g7(10,g-e)%g7(10,e)|0)*f:0),i)for(;;)if(0==k){1e7==(l[0]+=f)&&(l[0]=1,++a.e);break}else{if(l[k]+=f,1e7!=l[k])break;l[k--]=0,f=1}for(d=l.length;0===l[--d];)l.pop();if(g2&&(a.e>g9||a.e<-g9))throw Error(g5+hg(a));return a}function hm(a,b){var c,d,e,f,g,h,i,j,k,l,m=a.constructor,n=m.precision;if(!a.s||!b.s)return b.s?b.s=-b.s:b=new m(a),g2?hl(b,n):b;if(i=a.d,l=b.d,d=b.e,j=a.e,i=i.slice(),g=j-d){for((k=g<0)?(c=i,g=-g,h=l.length):(c=l,d=j,h=i.length),g>(e=Math.max(Math.ceil(n/7),h)+2)&&(g=e,c.length=1),c.reverse(),e=g;e--;)c.push(0);c.reverse()}else{for((k=(e=i.length)<(h=l.length))&&(h=e),e=0;e<h;e++)if(i[e]!=l[e]){k=i[e]<l[e];break}g=0}for(k&&(c=i,i=l,l=c,b.s=-b.s),h=i.length,e=l.length-h;e>0;--e)i[h++]=0;for(e=l.length;e>g;){if(i[--e]<l[e]){for(f=e;f&&0===i[--f];)i[f]=1e7-1;--i[f],i[e]+=1e7}i[e]-=l[e]}for(;0===i[--h];)i.pop();for(;0===i[0];i.shift())--d;return i[0]?(b.d=i,b.e=d,g2?hl(b,n):b):new m(0)}function hn(a,b,c){var d,e=hg(a),f=hd(a.d),g=f.length;return b?(c&&(d=c-g)>0?f=f.charAt(0)+"."+f.slice(1)+hi(d):g>1&&(f=f.charAt(0)+"."+f.slice(1)),f=f+(e<0?"e":"e+")+e):e<0?(f="0."+hi(-e-1)+f,c&&(d=c-g)>0&&(f+=hi(d))):e>=g?(f+=hi(e+1-g),c&&(d=c-e-1)>0&&(f=f+"."+hi(d))):((d=e+1)<g&&(f=f.slice(0,d)+"."+f.slice(d)),c&&(d=c-g)>0&&(e+1===g&&(f+="."),f+=hi(d))),a.s<0?"-"+f:f}function ho(a,b){if(a.length>b)return a.length=b,!0}function hp(a){if(!a||"object"!=typeof a)throw Error(g3+"Object expected");var b,c,d,e=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(b=0;b<e.length;b+=3)if(void 0!==(d=a[c=e[b]]))if(g6(d)===d&&d>=e[b+1]&&d<=e[b+2])this[c]=d;else throw Error(g4+c+": "+d);if(void 0!==(d=a[c="LN10"]))if(d==Math.LN10)this[c]=new this(d);else throw Error(g4+c+": "+d);return this}var g0=function a(b){var c,d,e;function f(a){if(!(this instanceof f))return new f(a);if(this.constructor=f,a instanceof f){this.s=a.s,this.e=a.e,this.d=(a=a.d)?a.slice():a;return}if("number"==typeof a){if(0*a!=0)throw Error(g4+a);if(a>0)this.s=1;else if(a<0)a=-a,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(a===~~a&&a<1e7){this.e=0,this.d=[a];return}return hk(this,a.toString())}if("string"!=typeof a)throw Error(g4+a);if(45===a.charCodeAt(0)?(a=a.slice(1),this.s=-1):this.s=1,g8.test(a))hk(this,a);else throw Error(g4+a)}if(f.prototype=ha,f.ROUND_UP=0,f.ROUND_DOWN=1,f.ROUND_CEIL=2,f.ROUND_FLOOR=3,f.ROUND_HALF_UP=4,f.ROUND_HALF_DOWN=5,f.ROUND_HALF_EVEN=6,f.ROUND_HALF_CEIL=7,f.ROUND_HALF_FLOOR=8,f.clone=a,f.config=f.set=hp,void 0===b&&(b={}),b)for(c=0,e=["precision","rounding","toExpNeg","toExpPos","LN10"];c<e.length;)b.hasOwnProperty(d=e[c++])||(b[d]=this[d]);return f.config(b),f}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});g1=new g0(1);let hq=g0;var hr=a=>a,hs={},ht=a=>function b(){let c;return 0==arguments.length||1==arguments.length&&(c=arguments.length<=0?void 0:arguments[0],c===hs)?b:a(...arguments)},hu=(a,b)=>1===a?b:ht(function(){for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];var f=d.filter(a=>a!==hs).length;return f>=a?b(...d):hu(a-f,ht(function(){for(var a=arguments.length,c=Array(a),e=0;e<a;e++)c[e]=arguments[e];return b(...d.map(a=>a===hs?c.shift():a),...c)}))}),hv=a=>hu(a.length,a),hw=(a,b)=>{for(var c=[],d=a;d<b;++d)c[d-a]=d;return c},hx=hv((a,b)=>Array.isArray(b)?b.map(a):Object.keys(b).map(a=>b[a]).map(a)),hy=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];if(!b.length)return hr;var d=b.reverse(),e=d[0],f=d.slice(1);return function(){return f.reduce((a,b)=>b(a),e(...arguments))}},hz=a=>Array.isArray(a)?a.reverse():a.split("").reverse().join(""),hA=a=>{var b=null,c=null;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return b&&e.every((a,c)=>{var d;return a===(null==(d=b)?void 0:d[c])})?c:(b=e,c=a(...e))}};function hB(a){return 0===a?1:Math.floor(new hq(a).abs().log(10).toNumber())+1}function hC(a,b,c){for(var d=new hq(a),e=0,f=[];d.lt(b)&&e<1e5;)f.push(d.toNumber()),d=d.add(c),e++;return f}hv((a,b,c)=>{var d=+a;return d+c*(b-d)}),hv((a,b,c)=>{var d=b-a;return(c-a)/(d=d||1/0)}),hv((a,b,c)=>{var d=b-a;return Math.max(0,Math.min(1,(c-a)/(d=d||1/0)))});var hD=a=>{var[b,c]=a,[d,e]=[b,c];return b>c&&([d,e]=[c,b]),[d,e]},hE=(a,b,c)=>{if(a.lte(0))return new hq(0);var d=hB(a.toNumber()),e=new hq(10).pow(d),f=a.div(e),g=1!==d?.05:.1,h=new hq(Math.ceil(f.div(g).toNumber())).add(c).mul(g).mul(e);return new hq(b?h.toNumber():Math.ceil(h.toNumber()))},hF=function(a,b,c,d){var e,f=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((b-a)/(c-1)))return{step:new hq(0),tickMin:new hq(0),tickMax:new hq(0)};var g=hE(new hq(b).sub(a).div(c-1),d,f),h=Math.ceil((e=a<=0&&b>=0?new hq(0):(e=new hq(a).add(b).div(2)).sub(new hq(e).mod(g))).sub(a).div(g).toNumber()),i=Math.ceil(new hq(b).sub(e).div(g).toNumber()),j=h+i+1;return j>c?hF(a,b,c,d,f+1):(j<c&&(i=b>0?i+(c-j):i,h=b>0?h:h+(c-j)),{step:g,tickMin:e.sub(new hq(h).mul(g)),tickMax:e.add(new hq(i).mul(g))})},hG=hA(function(a){var[b,c]=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],f=Math.max(d,2),[g,h]=hD([b,c]);if(g===-1/0||h===1/0){var i=h===1/0?[g,...hw(0,d-1).map(()=>1/0)]:[...hw(0,d-1).map(()=>-1/0),h];return b>c?hz(i):i}if(g===h){var j=new hq(1),k=new hq(g);if(!k.isint()&&e){var l=Math.abs(g);l<1?(j=new hq(10).pow(hB(g)-1),k=new hq(Math.floor(k.div(j).toNumber())).mul(j)):l>1&&(k=new hq(Math.floor(g)))}else 0===g?k=new hq(Math.floor((d-1)/2)):e||(k=new hq(Math.floor(g)));var m=Math.floor((d-1)/2);return hy(hx(a=>k.add(new hq(a-m).mul(j)).toNumber()),hw)(0,d)}var{step:n,tickMin:o,tickMax:p}=hF(g,h,f,e,0),q=hC(o,p.add(new hq(.1).mul(n)),n);return b>c?hz(q):q}),hH=hA(function(a,b){var[c,d]=a,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[f,g]=hD([c,d]);if(f===-1/0||g===1/0)return[c,d];if(f===g)return[f];var h=Math.max(b,2),i=hE(new hq(g).sub(f).div(h-1),e,0),j=[...hC(new hq(f),new hq(g),i),g];return!1===e&&(j=j.map(a=>Math.round(a))),c>d?hz(j):j}),hI=a=>a.rootProps.stackOffset,hJ=a=>a.options.chartName,hK=a=>a.rootProps.syncId,hL=a=>a.rootProps.syncMethod,hM=a=>a.options.eventEmitter,hN={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},hO={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},hP=(a,b)=>{if(a&&b)return null!=a&&a.reversed?[b[1],b[0]]:b},hQ={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:hN.angleAxisId,includeHidden:!1,name:void 0,reversed:hN.reversed,scale:hN.scale,tick:hN.tick,tickCount:void 0,ticks:void 0,type:hN.type,unit:void 0},hR={allowDataOverflow:hO.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:hO.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hO.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hO.scale,tick:hO.tick,tickCount:hO.tickCount,ticks:void 0,type:hO.type,unit:void 0},hS={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:hN.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hN.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hN.scale,tick:hN.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},hT={allowDataOverflow:hO.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:hO.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hO.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hO.scale,tick:hO.tick,tickCount:hO.tickCount,ticks:void 0,type:"category",unit:void 0},hU=(a,b)=>null!=a.polarAxis.angleAxis[b]?a.polarAxis.angleAxis[b]:"radial"===a.layout.layoutType?hS:hQ,hV=(a,b)=>null!=a.polarAxis.radiusAxis[b]?a.polarAxis.radiusAxis[b]:"radial"===a.layout.layoutType?hT:hR,hW=a=>a.polarOptions,hX=ca([cL,cM,cV],cx),hY=ca([hW,hX],(a,b)=>{if(null!=a)return z(a.innerRadius,b,0)}),hZ=ca([hW,hX],(a,b)=>{if(null!=a)return z(a.outerRadius,b,.8*b)}),h$=ca([hW],a=>{if(null==a)return[0,0];var{startAngle:b,endAngle:c}=a;return[b,c]});ca([hU,h$],hP);var h_=ca([hX,hY,hZ],(a,b,c)=>{if(null!=a&&null!=b&&null!=c)return[b,c]});ca([hV,h_],hP);var h0=ca([c5,hW,hY,hZ,cL,cM],(a,b,c,d,e,f)=>{if(("centric"===a||"radial"===a)&&null!=b&&null!=c&&null!=d){var{cx:g,cy:h,startAngle:i,endAngle:j}=b;return{cx:z(g,e,e/2),cy:z(h,f,f/2),innerRadius:c,outerRadius:d,startAngle:i,endAngle:j,clockWise:!1}}}),h1=(a,b)=>b,h2=(a,b,c)=>c;function h3(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function h4(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?h3(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):h3(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var h5=[0,"auto"],h6={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},h7=(a,b)=>{var c=a.cartesianAxis.xAxis[b];return null==c?h6:c},h8={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:h5,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},h9=(a,b)=>{var c=a.cartesianAxis.yAxis[b];return null==c?h8:c},ia={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},ib=(a,b)=>{var c=a.cartesianAxis.zAxis[b];return null==c?ia:c},ic=(a,b,c)=>{switch(b){case"xAxis":return h7(a,c);case"yAxis":return h9(a,c);case"zAxis":return ib(a,c);case"angleAxis":return hU(a,c);case"radiusAxis":return hV(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},id=(a,b,c)=>{switch(b){case"xAxis":return h7(a,c);case"yAxis":return h9(a,c);case"angleAxis":return hU(a,c);case"radiusAxis":return hV(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},ie=a=>a.graphicalItems.countOfBars>0;function ig(a,b){return c=>{switch(a){case"xAxis":return"xAxisId"in c&&c.xAxisId===b;case"yAxis":return"yAxisId"in c&&c.yAxisId===b;case"zAxis":return"zAxisId"in c&&c.zAxisId===b;case"angleAxis":return"angleAxisId"in c&&c.angleAxisId===b;case"radiusAxis":return"radiusAxisId"in c&&c.radiusAxisId===b;default:return!1}}}var ih=a=>a.graphicalItems.cartesianItems,ii=ca([h1,h2],ig),ij=(a,b,c)=>a.filter(c).filter(a=>(null==b?void 0:b.includeHidden)===!0||!a.hide),ik=ca([ih,ic,ii],ij),il=a=>a.filter(a=>void 0===a.stackId),im=ca([ik],il),io=a=>a.map(a=>a.data).filter(Boolean).flat(1),ip=ca([ik],io),iq=(a,b)=>{var{chartData:c=[],dataStartIndex:d,dataEndIndex:e}=b;return a.length>0?a:c.slice(d,e+1)},ir=ca([ip,gP],iq),is=(a,b,c)=>(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cA(a,b.dataKey)})):c.length>0?c.map(a=>a.dataKey).flatMap(b=>a.map(a=>({value:cA(a,b)}))):a.map(a=>({value:a})),it=ca([ir,ic,ik],is);function iu(a,b){switch(a){case"xAxis":return"x"===b.direction;case"yAxis":return"y"===b.direction;default:return!1}}function iv(a){return a.filter(a=>w(a)||a instanceof Date).map(Number).filter(a=>!1===t(a))}var iw=(a,b,c)=>Object.fromEntries(Object.entries(b.reduce((a,b)=>(null==b.stackId||(null==a[b.stackId]&&(a[b.stackId]=[]),a[b.stackId].push(b)),a),{})).map(b=>{var[d,e]=b;return[d,{stackedData:((a,b,c)=>{var d=cE[c];return(function(){var a=cp([]),b=cq,c=cn,d=cr;function e(e){var f,g,h=Array.from(a.apply(this,arguments),cs),i=h.length,j=-1;for(let a of e)for(f=0,++j;f<i;++f)(h[f][j]=[0,+d(a,h[f].key,j,e)]).data=a;for(f=0,g=co(b(h));f<i;++f)h[g[f]].index=f;return c(h,g),h}return e.keys=function(b){return arguments.length?(a="function"==typeof b?b:cp(Array.from(b)),e):a},e.value=function(a){return arguments.length?(d="function"==typeof a?a:cp(+a),e):d},e.order=function(a){return arguments.length?(b=null==a?cq:"function"==typeof a?a:cp(Array.from(a)),e):b},e.offset=function(a){return arguments.length?(c=null==a?cn:a,e):c},e})().keys(b).value((a,b)=>+cA(a,b,0)).order(cq).offset(d)(a)})(a,e.map(a=>a.dataKey),c),graphicalItems:e}]})),ix=ca([ir,ik,hI],iw),iy=(a,b,c)=>{var{dataStartIndex:d,dataEndIndex:e}=b;if("zAxis"!==c){var f=((a,b,c)=>{if(null!=a)return(a=>[a[0]===1/0?0:a[0],a[1]===-1/0?0:a[1]])(Object.keys(a).reduce((d,e)=>{var{stackedData:f}=a[e],g=f.reduce((a,d)=>{var e=(a=>{var b=a.flat(2).filter(v);return[Math.min(...b),Math.max(...b)]})(d.slice(b,c+1));return[Math.min(a[0],e[0]),Math.max(a[1],e[1])]},[1/0,-1/0]);return[Math.min(g[0],d[0]),Math.max(g[1],d[1])]},[1/0,-1/0]))})(a,d,e);if(null==f||0!==f[0]||0!==f[1])return f}},iz=ca([ix,gN,h1],iy),iA=(a,b,c,d)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var e,f,g=null==(e=c.errorBars)?void 0:e.filter(a=>iu(d,a)),h=cA(a,null!=(f=b.dataKey)?f:c.dataKey);return{value:h,errorDomain:function(a,b,c){return!c||"number"!=typeof b||t(b)||!c.length?[]:iv(c.flatMap(c=>{var d,e,f=cA(a,c.dataKey);if(Array.isArray(f)?[d,e]=f:d=e=f,gQ(d)&&gQ(e))return[b-d,b+e]}))}(a,h,g)}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cA(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]})),iB=ca(ir,ic,im,h1,iA);function iC(a){var{value:b}=a;if(w(b)||b instanceof Date)return b}var iD=a=>{var b;if(null==a||!("domain"in a))return h5;if(null!=a.domain)return a.domain;if(null!=a.ticks){if("number"===a.type){var c=iv(a.ticks);return[Math.min(...c),Math.max(...c)]}if("category"===a.type)return a.ticks.map(String)}return null!=(b=null==a?void 0:a.domain)?b:h5},iE=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];var d=b.filter(Boolean);if(0!==d.length){var e=d.flat();return[Math.min(...e),Math.max(...e)]}},iF=a=>a.referenceElements.dots,iG=(a,b,c)=>a.filter(a=>"extendDomain"===a.ifOverflow).filter(a=>"xAxis"===b?a.xAxisId===c:a.yAxisId===c),iH=ca([iF,h1,h2],iG),iI=a=>a.referenceElements.areas,iJ=ca([iI,h1,h2],iG),iK=a=>a.referenceElements.lines,iL=ca([iK,h1,h2],iG),iM=(a,b)=>{var c=iv(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iN=ca(iH,h1,iM),iO=(a,b)=>{var c=iv(a.flatMap(a=>["xAxis"===b?a.x1:a.y1,"xAxis"===b?a.x2:a.y2]));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iP=ca([iJ,h1],iO),iQ=(a,b)=>{var c=iv(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iR=ca(iL,h1,iQ),iS=ca(iN,iR,iP,(a,b,c)=>iE(a,c,b)),iT=ca([ic],iD),iU=(a,b,c,d,e)=>{var f=function(a,b){if(b&&"function"!=typeof a&&Array.isArray(a)&&2===a.length){var c,d,[e,f]=a;if(gQ(e))c=e;else if("function"==typeof e)return;if(gQ(f))d=f;else if("function"==typeof f)return;var g=[c,d];if(gS(g))return g}}(b,a.allowDataOverflow);return null!=f?f:function(a,b,c){if(c||null!=b){if("function"==typeof a&&null!=b)try{var d=a(b,c);if(gS(d))return gT(d,b,c)}catch(a){}if(Array.isArray(a)&&2===a.length){var e,f,[g,h]=a;if("auto"===g)null!=b&&(e=Math.min(...b));else if(v(g))e=g;else if("function"==typeof g)try{null!=b&&(e=g(null==b?void 0:b[0]))}catch(a){}else if("string"==typeof g&&cG.test(g)){var i=cG.exec(g);if(null==i||null==b)e=void 0;else{var j=+i[1];e=b[0]-j}}else e=null==b?void 0:b[0];if("auto"===h)null!=b&&(f=Math.max(...b));else if(v(h))f=h;else if("function"==typeof h)try{null!=b&&(f=h(null==b?void 0:b[1]))}catch(a){}else if("string"==typeof h&&cH.test(h)){var k=cH.exec(h);if(null==k||null==b)f=void 0;else{var l=+k[1];f=b[1]+l}}else f=null==b?void 0:b[1];var m=[e,f];if(gS(m))return null==b?m:gT(m,b,c)}}}(b,iE(c,e,(a=>{var b=iv(a.flatMap(a=>[a.value,a.errorDomain]).flat(1));if(0!==b.length)return[Math.min(...b),Math.max(...b)]})(d)),a.allowDataOverflow)},iV=ca([ic,iT,iz,iB,iS],iU),iW=[0,1],iX=(a,b,c,d,e,f,g)=>{if(null!=a&&null!=c&&0!==c.length){var{dataKey:h,type:i}=a,j=cB(b,f);return j&&null==h?c8()(0,c.length):"category"===i?((a,b,c)=>{var d=a.map(iC).filter(a=>null!=a);return c&&(null==b.dataKey||b.allowDuplicatedCategory&&A(d))?c8()(0,a.length):b.allowDuplicatedCategory?d:Array.from(new Set(d))})(d,a,j):"expand"===e?iW:g}},iY=ca([ic,c5,ir,it,hI,h1,iV],iX),iZ=(a,b,c,e,f)=>{if(null!=a){var{scale:g,type:h}=a;if("auto"===g)return"radial"===b&&"radiusAxis"===f?"band":"radial"===b&&"angleAxis"===f?"linear":"category"===h&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!c)?"point":"category"===h?"band":"linear";if("string"==typeof g){var i="scale".concat(D(g));return i in d?i:"point"}}},i$=ca([ic,c5,ie,hJ,h1],iZ);function i_(a,b,c,e){if(null!=c&&null!=e){if("function"==typeof a.scale)return a.scale.copy().domain(c).range(e);var f=function(a){if(null!=a){if(a in d)return d[a]();var b="scale".concat(D(a));if(b in d)return d[b]()}}(b);if(null!=f){var g=f.domain(c).range(e);return(a=>{var b=a.domain();if(b&&!(b.length<=2)){var c=b.length,d=a.range(),e=Math.min(d[0],d[1])-1e-4,f=Math.max(d[0],d[1])+1e-4,g=a(b[0]),h=a(b[c-1]);(g<e||g>f||h<e||h>f)&&a.domain([b[0],b[c-1]])}})(g),g}}}var i0=(a,b,c)=>{var d=iD(b);if("auto"===c||"linear"===c){if(null!=b&&b.tickCount&&Array.isArray(d)&&("auto"===d[0]||"auto"===d[1])&&gS(a))return hG(a,b.tickCount,b.allowDecimals);if(null!=b&&b.tickCount&&"number"===b.type&&gS(a))return hH(a,b.tickCount,b.allowDecimals)}},i1=ca([iY,id,i$],i0),i2=(a,b,c,d)=>"angleAxis"!==d&&(null==a?void 0:a.type)==="number"&&gS(b)&&Array.isArray(c)&&c.length>0?[Math.min(b[0],c[0]),Math.max(b[1],c[c.length-1])]:b,i3=ca([ic,iY,i1,h1],i2),i4=ca(it,ic,(a,b)=>{if(b&&"number"===b.type){var c=1/0,d=Array.from(iv(a.map(a=>a.value))).sort((a,b)=>a-b);if(d.length<2)return 1/0;var e=d[d.length-1]-d[0];if(0===e)return 1/0;for(var f=0;f<d.length-1;f++)c=Math.min(c,d[f+1]-d[f]);return c/e}}),i5=ca(i4,c5,a=>a.rootProps.barCategoryGap,cV,(a,b,c,d)=>d,(a,b,c,d,e)=>{if(!gQ(a))return 0;var f="vertical"===b?d.height:d.width;if("gap"===e)return a*f/2;if("no-gap"===e){var g=z(c,a*f),h=a*f/2;return h-g-(h-g)/f*g}return 0}),i6=ca(h7,(a,b)=>{var c=h7(a,b);return null==c||"string"!=typeof c.padding?0:i5(a,"xAxis",b,c.padding)},(a,b)=>{if(null==a)return{left:0,right:0};var c,d,{padding:e}=a;return"string"==typeof e?{left:b,right:b}:{left:(null!=(c=e.left)?c:0)+b,right:(null!=(d=e.right)?d:0)+b}}),i7=ca(h9,(a,b)=>{var c=h9(a,b);return null==c||"string"!=typeof c.padding?0:i5(a,"yAxis",b,c.padding)},(a,b)=>{if(null==a)return{top:0,bottom:0};var c,d,{padding:e}=a;return"string"==typeof e?{top:b,bottom:b}:{top:(null!=(c=e.top)?c:0)+b,bottom:(null!=(d=e.bottom)?d:0)+b}}),i8=ca([cV,i6,c_,c$,(a,b,c)=>c],(a,b,c,d,e)=>{var{padding:f}=d;return e?[f.left,c.width-f.right]:[a.left+b.left,a.left+a.width-b.right]}),i9=ca([cV,c5,i7,c_,c$,(a,b,c)=>c],(a,b,c,d,e,f)=>{var{padding:g}=e;return f?[d.height-g.bottom,g.top]:"horizontal"===b?[a.top+a.height-c.bottom,a.top+c.top]:[a.top+c.top,a.top+a.height-c.bottom]}),ja=(a,b,c,d)=>{var e;switch(b){case"xAxis":return i8(a,c,d);case"yAxis":return i9(a,c,d);case"zAxis":return null==(e=ib(a,c))?void 0:e.range;case"angleAxis":return h$(a);case"radiusAxis":return h_(a,c);default:return}},jb=ca([ic,ja],hP),jc=ca([ic,i$,i3,jb],i_);function jd(a,b){return a.id<b.id?-1:+(a.id>b.id)}ca(ik,h1,(a,b)=>a.flatMap(a=>{var b;return null!=(b=a.errorBars)?b:[]}).filter(a=>iu(b,a)));var je=(a,b)=>b,jf=(a,b,c)=>c,jg=ca(cP,je,jf,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(jd)),jh=ca(cQ,je,jf,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(jd)),ji=(a,b)=>({width:a.width,height:b.height}),jj=ca(cV,h7,ji),jk=ca(cM,cV,jg,je,jf,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=ji(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"top":return a.top;case"bottom":return c-a.bottom;default:return 0}})(b,d,a));var i="top"===d&&!e||"bottom"===d&&e;g[c.id]=f-Number(i)*h.height,f+=(i?-1:1)*h.height}),g}),jl=ca(cL,cV,jh,je,jf,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=((a,b)=>({width:"number"==typeof b.width?b.width:60,height:a.height}))(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"left":return a.left;case"right":return c-a.right;default:return 0}})(b,d,a));var i="left"===d&&!e||"right"===d&&e;g[c.id]=f-Number(i)*h.width,f+=(i?-1:1)*h.width}),g}),jm=ca(cV,h9,(a,b)=>({width:"number"==typeof b.width?b.width:60,height:a.height})),jn=(a,b,c,d)=>{if(null!=c){var{allowDuplicatedCategory:e,type:f,dataKey:g}=c,h=cB(a,d),i=b.map(a=>a.value);if(g&&h&&"category"===f&&e&&A(i))return i}},jo=ca([c5,it,ic,h1],jn),jp=(a,b,c,d)=>{if(null!=c&&null!=c.dataKey){var{type:e,scale:f}=c;if(cB(a,d)&&("number"===e||"auto"!==f))return b.map(a=>a.value)}},jq=ca([c5,it,id,h1],jp),jr=ca([c5,(a,b,c)=>{switch(b){case"xAxis":return h7(a,c);case"yAxis":return h9(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},i$,jc,jo,jq,ja,i1,h1],(a,b,c,d,e,f,g,h,i)=>{if(null==b)return null;var j=cB(a,i);return{angle:b.angle,interval:b.interval,minTickGap:b.minTickGap,orientation:b.orientation,tick:b.tick,tickCount:b.tickCount,tickFormatter:b.tickFormatter,ticks:b.ticks,type:b.type,unit:b.unit,axisType:i,categoricalDomain:f,duplicateDomain:e,isCategorical:j,niceTicks:h,range:g,realScaleType:c,scale:d}}),js=ca([c5,id,i$,jc,i1,ja,jo,jq,h1],(a,b,c,d,e,f,g,h,i)=>{if(null!=b&&null!=d){var j=cB(a,i),{type:k,ticks:l,tickCount:m}=b,n="scaleBand"===c&&"function"==typeof d.bandwidth?d.bandwidth()/2:2,o="category"===k&&d.bandwidth?d.bandwidth()/n:0;o="angleAxis"===i&&null!=f&&f.length>=2?2*s(f[0]-f[1])*o:o;var p=l||e;return p?p.map((a,b)=>({index:b,coordinate:d(g?g.indexOf(a):a)+o,value:a,offset:o})).filter(a=>!t(a.coordinate)):j&&h?h.map((a,b)=>({coordinate:d(a)+o,value:a,index:b,offset:o})):d.ticks?d.ticks(m).map(a=>({coordinate:d(a)+o,value:a,offset:o})):d.domain().map((a,b)=>({coordinate:d(a)+o,value:g?g[a]:a,index:b,offset:o}))}}),jt=ca([c5,id,jc,ja,jo,jq,h1],(a,b,c,d,e,f,g)=>{if(null!=b&&null!=c&&null!=d&&d[0]!==d[1]){var h=cB(a,g),{tickCount:i}=b,j=0;return(j="angleAxis"===g&&(null==d?void 0:d.length)>=2?2*s(d[0]-d[1])*j:j,h&&f)?f.map((a,b)=>({coordinate:c(a)+j,value:a,index:b,offset:j})):c.ticks?c.ticks(i).map(a=>({coordinate:c(a)+j,value:a,offset:j})):c.domain().map((a,b)=>({coordinate:c(a)+j,value:e?e[a]:a,index:b,offset:j}))}}),ju=ca(ic,jc,(a,b)=>{if(null!=a&&null!=b)return h4(h4({},a),{},{scale:b})}),jv=ca([ic,i$,iY,jb],i_);ca((a,b,c)=>ib(a,c),jv,(a,b)=>{if(null!=a&&null!=b)return h4(h4({},a),{},{scale:b})});var jw=ca([c5,cP,cQ],(a,b,c)=>{switch(a){case"horizontal":return b.some(a=>a.reversed)?"right-to-left":"left-to-right";case"vertical":return c.some(a=>a.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),jx=a=>a.options.defaultTooltipEventType,jy=a=>a.options.validateTooltipEventTypes;function jz(a,b,c){if(null==a)return b;var d=a?"axis":"item";return null==c?b:c.includes(d)?d:b}function jA(a,b){return jz(b,jx(a),jy(a))}var jB=(a,b)=>{var c,d=Number(b);if(!t(d)&&null!=b)return d>=0?null==a||null==(c=a[d])?void 0:c.value:void 0};function jC(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function jD(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?jC(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):jC(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var jE=(a,b,c,d)=>{if(null==b)return bw;var e=function(a,b,c){return"axis"===b?"click"===c?a.axisInteraction.click:a.axisInteraction.hover:"click"===c?a.itemInteraction.click:a.itemInteraction.hover}(a,b,c);if(null==e)return bw;if(e.active)return e;if(a.keyboardInteraction.active)return a.keyboardInteraction;if(a.syncInteraction.active&&null!=a.syncInteraction.index)return a.syncInteraction;var f=!0===a.settings.active;if(null!=e.index){if(f)return jD(jD({},e),{},{active:!0})}else if(null!=d)return{active:!0,coordinate:void 0,dataKey:void 0,index:d};return jD(jD({},bw),{},{coordinate:e.coordinate})},jF=(a,b)=>{var c=null==a?void 0:a.index;if(null==c)return null;var d=Number(c);if(!gQ(d))return c;var e=Infinity;return b.length>0&&(e=b.length-1),String(Math.max(0,Math.min(d,e)))},jG=(a,b,c,d,e,f,g,h)=>{if(null!=f&&null!=h){var i=g[0],j=null==i?void 0:h(i.positions,f);if(null!=j)return j;var k=null==e?void 0:e[Number(f)];if(k)if("horizontal"===c)return{x:k.coordinate,y:(d.top+b)/2};else return{x:(d.left+a)/2,y:k.coordinate}}},jH=(a,b,c,d)=>{var e;return"axis"===b?a.tooltipItemPayloads:0===a.tooltipItemPayloads.length?[]:null==(e="hover"===c?a.itemInteraction.hover.dataKey:a.itemInteraction.click.dataKey)&&null!=d?[a.tooltipItemPayloads[0]]:a.tooltipItemPayloads.filter(a=>{var b;return(null==(b=a.settings)?void 0:b.dataKey)===e})},jI=a=>a.options.tooltipPayloadSearcher,jJ=a=>a.tooltip;function jK(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function jL(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?jK(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):jK(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var jM=(a,b,c,d,e,f,g)=>{if(null!=b&&null!=f){var{chartData:h,computedData:i,dataStartIndex:j,dataEndIndex:k}=c;return a.reduce((a,c)=>{var l,m,n,o,p,{dataDefinedOnItem:q,settings:r}=c,s=function(a,b,c){return Array.isArray(a)&&a&&b+c!==0?a.slice(b,c+1):a}((l=q,m=h,null!=l?l:m),j,k),t=null!=(n=null==r?void 0:r.dataKey)?n:null==d?void 0:d.dataKey,u=null==r?void 0:r.nameKey;return Array.isArray(o=null!=d&&d.dataKey&&Array.isArray(s)&&!Array.isArray(s[0])&&"axis"===g?C(s,d.dataKey,e):f(s,b,i,u))?o.forEach(b=>{var c=jL(jL({},r),{},{name:b.name,unit:b.unit,color:void 0,fill:void 0});a.push(cJ({tooltipEntrySettings:c,dataKey:b.dataKey,payload:b.payload,value:cA(b.payload,b.dataKey),name:b.name}))}):a.push(cJ({tooltipEntrySettings:r,dataKey:t,payload:o,value:cA(o,t),name:null!=(p=cA(o,u))?p:null==r?void 0:r.name})),a},[])}},jN=a=>{var b=c5(a);return"horizontal"===b?"xAxis":"vertical"===b?"yAxis":"centric"===b?"angleAxis":"radiusAxis"},jO=a=>a.tooltip.settings.axisId,jP=a=>{var b=jN(a),c=jO(a);return id(a,b,c)},jQ=ca([jP,c5,ie,hJ,jN],iZ),jR=ca([a=>a.graphicalItems.cartesianItems,a=>a.graphicalItems.polarItems],(a,b)=>[...a,...b]),jS=ca([jN,jO],ig),jT=ca([jR,jP,jS],ij),jU=ca([jT],io),jV=ca([jU,gN],iq),jW=ca([jV,jP,jT],is),jX=ca([jP],iD),jY=ca([jV,jT,hI],iw),jZ=ca([jY,gN,jN],iy),j$=ca([jT],il),j_=ca([jV,jP,j$,jN],iA),j0=ca([iF,jN,jO],iG),j1=ca([j0,jN],iM),j2=ca([iI,jN,jO],iG),j3=ca([j2,jN],iO),j4=ca([iK,jN,jO],iG),j5=ca([j4,jN],iQ),j6=ca([j1,j5,j3],iE),j7=ca([jP,jX,jZ,j_,j6],iU),j8=ca([jP,c5,jV,jW,hI,jN,j7],iX),j9=ca([j8,jP,jQ],i0),ka=ca([jP,j8,j9,jN],i2),kb=a=>{var b=jN(a),c=jO(a);return ja(a,b,c,!1)},kc=ca([jP,kb],hP),kd=ca([jP,jQ,ka,kc],i_),ke=ca([c5,jW,jP,jN],jn),kf=ca([c5,jW,jP,jN],jp),kg=ca([c5,jP,jQ,kd,kb,ke,kf,jN],(a,b,c,d,e,f,g,h)=>{if(b){var{type:i}=b,j=cB(a,h);if(d){var k="scaleBand"===c&&d.bandwidth?d.bandwidth()/2:2,l="category"===i&&d.bandwidth?d.bandwidth()/k:0;return(l="angleAxis"===h&&null!=e&&(null==e?void 0:e.length)>=2?2*s(e[0]-e[1])*l:l,j&&g)?g.map((a,b)=>({coordinate:d(a)+l,value:a,index:b,offset:l})):d.domain().map((a,b)=>({coordinate:d(a)+l,value:f?f[a]:a,index:b,offset:l}))}}}),kh=ca([jx,jy,a=>a.tooltip.settings],(a,b,c)=>jz(c.shared,a,b)),ki=a=>a.tooltip.settings.trigger,kj=a=>a.tooltip.settings.defaultIndex,kk=ca([jJ,kh,ki,kj],jE),kl=ca([kk,jV],jF),km=ca([kg,kl],jB),kn=ca([kk],a=>{if(a)return a.dataKey}),ko=ca([jJ,kh,ki,kj],jH),kp=ca([cL,cM,c5,cV,kg,kj,ko,jI],jG),kq=ca([kk,kp],(a,b)=>null!=a&&a.coordinate?a.coordinate:b),kr=ca([kk],a=>a.active),ks=ca([ko,kl,gN,jP,km,jI,kh],jM),kt=ca([ks],a=>{if(null!=a)return Array.from(new Set(a.map(a=>a.payload).filter(a=>null!=a)))}),ku=(a,b)=>b,kv=(a,b,c)=>c,kw=(a,b,c,d)=>d,kx=ca(kg,a=>cl()(a,a=>a.coordinate)),ky=ca([jJ,ku,kv,kw],jE),kz=ca([ky,jV],jF),kA=ca([jJ,ku,kv,kw],jH),kB=ca([cL,cM,c5,cV,kg,kw,kA,jI],jG),kC=ca([ky,kB],(a,b)=>{var c;return null!=(c=a.coordinate)?c:b}),kD=ca(kg,kz,jB),kE=ca([kA,kz,gN,jP,kD,jI,ku],jM),kF=ca([ky],a=>({isActive:a.active,activeIndex:a.index})),kG=ca([(a,b)=>b,c5,h0,jN,kc,kg,kx,cV],(a,b,c,d,e,f,g,h)=>{if(a&&b&&d&&e&&f){var i=function(a,b,c,d,e){return"horizontal"===c||"vertical"===c?a>=e.left&&a<=e.left+e.width&&b>=e.top&&b<=e.top+e.height?{x:a,y:b}:null:d?((a,b)=>{var c,{x:d,y:e}=a,{radius:f,angle:g}=((a,b)=>{var{x:c,y:d}=a,{cx:e,cy:f}=b,g=((a,b)=>{var{x:c,y:d}=a,{x:e,y:f}=b;return Math.sqrt((c-e)**2+(d-f)**2)})({x:c,y:d},{x:e,y:f});if(g<=0)return{radius:g,angle:0};var h=Math.acos((c-e)/g);return d>f&&(h=2*Math.PI-h),{radius:g,angle:180*h/Math.PI,angleInRadian:h}})({x:d,y:e},b),{innerRadius:h,outerRadius:i}=b;if(f<h||f>i||0===f)return null;var{startAngle:j,endAngle:k}=(a=>{var{startAngle:b,endAngle:c}=a,d=Math.min(Math.floor(b/360),Math.floor(c/360));return{startAngle:b-360*d,endAngle:c-360*d}})(b),l=g;if(j<=k){for(;l>k;)l-=360;for(;l<j;)l+=360;c=l>=j&&l<=k}else{for(;l>j;)l-=360;for(;l<k;)l+=360;c=l>=k&&l<=j}return c?cu(cu({},b),{},{radius:f,angle:((a,b)=>{var{startAngle:c,endAngle:d}=b;return a+360*Math.min(Math.floor(c/360),Math.floor(d/360))})(l,b)}):null})({x:a,y:b},d):null}(a.chartX,a.chartY,b,c,h);if(i){var j=((a,b,c,d,e)=>{var f,g=-1,h=null!=(f=null==b?void 0:b.length)?f:0;if(h<=1||null==a)return 0;if("angleAxis"===d&&null!=e&&1e-6>=Math.abs(Math.abs(e[1]-e[0])-360))for(var i=0;i<h;i++){var j=i>0?c[i-1].coordinate:c[h-1].coordinate,k=c[i].coordinate,l=i>=h-1?c[0].coordinate:c[i+1].coordinate,m=void 0;if(s(k-j)!==s(l-k)){var n=[];if(s(l-k)===s(e[1]-e[0])){m=l;var o=k+e[1]-e[0];n[0]=Math.min(o,(o+j)/2),n[1]=Math.max(o,(o+j)/2)}else{m=j;var p=l+e[1]-e[0];n[0]=Math.min(k,(p+k)/2),n[1]=Math.max(k,(p+k)/2)}var q=[Math.min(k,(m+k)/2),Math.max(k,(m+k)/2)];if(a>q[0]&&a<=q[1]||a>=n[0]&&a<=n[1]){({index:g}=c[i]);break}}else{var r=Math.min(j,l),t=Math.max(j,l);if(a>(r+k)/2&&a<=(t+k)/2){({index:g}=c[i]);break}}}else if(b){for(var u=0;u<h;u++)if(0===u&&a<=(b[u].coordinate+b[u+1].coordinate)/2||u>0&&u<h-1&&a>(b[u].coordinate+b[u-1].coordinate)/2&&a<=(b[u].coordinate+b[u+1].coordinate)/2||u===h-1&&a>(b[u].coordinate+b[u-1].coordinate)/2){({index:g}=b[u]);break}}return g})(((a,b)=>"horizontal"===b?a.x:"vertical"===b?a.y:"centric"===b?a.angle:a.radius)(i,b),g,f,d,e),k=((a,b,c,d)=>{var e=b.find(a=>a&&a.index===c);if(e){if("horizontal"===a)return{x:e.coordinate,y:d.y};if("vertical"===a)return{x:d.x,y:e.coordinate};if("centric"===a){var f=e.coordinate,{radius:g}=d;return cz(cz(cz({},d),cw(d.cx,d.cy,g,f)),{},{angle:f,radius:g})}var h=e.coordinate,{angle:i}=d;return cz(cz(cz({},d),cw(d.cx,d.cy,h,i)),{},{angle:i,radius:h})}return{x:0,y:0}})(b,f,j,i);return{activeIndex:String(j),activeCoordinate:k}}}}),kH=a=>{var b=a.currentTarget.getBoundingClientRect(),c=b.width/a.currentTarget.offsetWidth,d=b.height/a.currentTarget.offsetHeight;return{chartX:Math.round((a.clientX-b.left)/c),chartY:Math.round((a.clientY-b.top)/d)}},kI=aG("mouseClick"),kJ=bi();kJ.startListening({actionCreator:kI,effect:(a,b)=>{var c=a.payload,d=kG(b.getState(),kH(c));(null==d?void 0:d.activeIndex)!=null&&b.dispatch(bG({activeIndex:d.activeIndex,activeDataKey:void 0,activeCoordinate:d.activeCoordinate}))}});var kK=aG("mouseMove"),kL=bi();function kM(a,b){return b instanceof HTMLElement?"HTMLElement <".concat(b.tagName,' class="').concat(b.className,'">'):b===window?"global.window":b}function kN(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function kO(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?kN(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):kN(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}kL.startListening({actionCreator:kK,effect:(a,b)=>{var c=a.payload,d=b.getState(),e=jA(d,d.tooltip.settings.shared),f=kG(d,kH(c));"axis"===e&&((null==f?void 0:f.activeIndex)!=null?b.dispatch(bF({activeIndex:f.activeIndex,activeDataKey:void 0,activeCoordinate:f.activeCoordinate})):b.dispatch(bD()))}});var kP=aO({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(a,b){a.xAxis[b.payload.id]=b.payload},removeXAxis(a,b){delete a.xAxis[b.payload.id]},addYAxis(a,b){a.yAxis[b.payload.id]=b.payload},removeYAxis(a,b){delete a.yAxis[b.payload.id]},addZAxis(a,b){a.zAxis[b.payload.id]=b.payload},removeZAxis(a,b){delete a.zAxis[b.payload.id]},updateYAxisWidth(a,b){var{id:c,width:d}=b.payload;a.yAxis[c]&&(a.yAxis[c]=kO(kO({},a.yAxis[c]),{},{width:d}))}}}),{addXAxis:kQ,removeXAxis:kR,addYAxis:kS,removeYAxis:kT,addZAxis:kU,removeZAxis:kV,updateYAxisWidth:kW}=kP.actions,kX=kP.reducer,kY=aO({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(a){a.countOfBars+=1},removeBar(a){a.countOfBars-=1},addCartesianGraphicalItem(a,b){a.cartesianItems.push(b.payload)},replaceCartesianGraphicalItem(a,b){var{prev:c,next:d}=b.payload,e=aC(a).cartesianItems.indexOf(c);e>-1&&(a.cartesianItems[e]=d)},removeCartesianGraphicalItem(a,b){var c=aC(a).cartesianItems.indexOf(b.payload);c>-1&&a.cartesianItems.splice(c,1)},addPolarGraphicalItem(a,b){a.polarItems.push(b.payload)},removePolarGraphicalItem(a,b){var c=aC(a).polarItems.indexOf(b.payload);c>-1&&a.polarItems.splice(c,1)}}}),{addBar:kZ,removeBar:k$,addCartesianGraphicalItem:k_,replaceCartesianGraphicalItem:k0,removeCartesianGraphicalItem:k1,addPolarGraphicalItem:k2,removePolarGraphicalItem:k3}=kY.actions,k4=kY.reducer,k5=aO({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(a,b)=>{a.dots.push(b.payload)},removeDot:(a,b)=>{var c=aC(a).dots.findIndex(a=>a===b.payload);-1!==c&&a.dots.splice(c,1)},addArea:(a,b)=>{a.areas.push(b.payload)},removeArea:(a,b)=>{var c=aC(a).areas.findIndex(a=>a===b.payload);-1!==c&&a.areas.splice(c,1)},addLine:(a,b)=>{a.lines.push(b.payload)},removeLine:(a,b)=>{var c=aC(a).lines.findIndex(a=>a===b.payload);-1!==c&&a.lines.splice(c,1)}}}),{addDot:k6,removeDot:k7,addArea:k8,removeArea:k9,addLine:la,removeLine:lb}=k5.actions,lc=k5.reducer,ld={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},le=aO({name:"brush",initialState:ld,reducers:{setBrushSettings:(a,b)=>null==b.payload?ld:b.payload}}),{setBrushSettings:lf}=le.actions,lg=le.reducer,lh=aO({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(a,b){a.size.width=b.payload.width,a.size.height=b.payload.height},setLegendSettings(a,b){a.settings.align=b.payload.align,a.settings.layout=b.payload.layout,a.settings.verticalAlign=b.payload.verticalAlign,a.settings.itemSorter=b.payload.itemSorter},addLegendPayload(a,b){a.payload.push(b.payload)},removeLegendPayload(a,b){var c=aC(a).payload.indexOf(b.payload);c>-1&&a.payload.splice(c,1)}}}),{setLegendSize:li,setLegendSettings:lj,addLegendPayload:lk,removeLegendPayload:ll}=lh.actions,lm=lh.reducer,ln={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},lo=aO({name:"rootProps",initialState:ln,reducers:{updateOptions:(a,b)=>{var c;a.accessibilityLayer=b.payload.accessibilityLayer,a.barCategoryGap=b.payload.barCategoryGap,a.barGap=null!=(c=b.payload.barGap)?c:ln.barGap,a.barSize=b.payload.barSize,a.maxBarSize=b.payload.maxBarSize,a.stackOffset=b.payload.stackOffset,a.syncId=b.payload.syncId,a.syncMethod=b.payload.syncMethod,a.className=b.payload.className}}}),lp=lo.reducer,{updateOptions:lq}=lo.actions,lr=aO({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(a,b){a.radiusAxis[b.payload.id]=b.payload},removeRadiusAxis(a,b){delete a.radiusAxis[b.payload.id]},addAngleAxis(a,b){a.angleAxis[b.payload.id]=b.payload},removeAngleAxis(a,b){delete a.angleAxis[b.payload.id]}}}),{addRadiusAxis:ls,removeRadiusAxis:lt,addAngleAxis:lu,removeAngleAxis:lv}=lr.actions,lw=lr.reducer,lx=aO({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(a,b)=>b.payload}}),{updatePolarOptions:ly}=lx.actions,lz=lx.reducer,lA=aG("keyDown"),lB=aG("focus"),lC=bi();lC.startListening({actionCreator:lA,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip,e=a.payload;if("ArrowRight"===e||"ArrowLeft"===e||"Enter"===e){var f=Number(jF(d,jV(c))),g=kg(c);if("Enter"===e){var h=kB(c,"axis","hover",String(d.index));b.dispatch(bI({active:!d.active,activeIndex:d.index,activeDataKey:d.dataKey,activeCoordinate:h}));return}var i=f+("ArrowRight"===e?1:-1)*("left-to-right"===jw(c)?1:-1);if(null!=g&&!(i>=g.length)&&!(i<0)){var j=kB(c,"axis","hover",String(i));b.dispatch(bI({active:!0,activeIndex:i.toString(),activeDataKey:void 0,activeCoordinate:j}))}}}}}),lC.startListening({actionCreator:lB,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip;if(!d.active&&null==d.index){var e=kB(c,"axis","hover",String("0"));b.dispatch(bI({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:e}))}}}});var lD=aG("externalEvent"),lE=bi();lE.startListening({actionCreator:lD,effect:(a,b)=>{if(null!=a.payload.handler){var c=b.getState(),d={activeCoordinate:kq(c),activeDataKey:kn(c),activeIndex:kl(c),activeLabel:km(c),activeTooltipIndex:kl(c),isTooltipActive:kr(c)};a.payload.handler(d,a.payload.reactEvent)}}});var lF=ca([jJ],a=>a.tooltipItemPayloads),lG=ca([lF,jI,(a,b,c)=>b,(a,b,c)=>c],(a,b,c,d)=>{var e=a.find(a=>a.settings.dataKey===d);if(null!=e){var{positions:f}=e;if(null!=f)return b(f,c)}}),lH=aG("touchMove"),lI=bi();lI.startListening({actionCreator:lH,effect:(a,b)=>{var c=a.payload,d=b.getState(),e=jA(d,d.tooltip.settings.shared);if("axis"===e){var f=kG(d,kH({clientX:c.touches[0].clientX,clientY:c.touches[0].clientY,currentTarget:c.currentTarget}));(null==f?void 0:f.activeIndex)!=null&&b.dispatch(bF({activeIndex:f.activeIndex,activeDataKey:void 0,activeCoordinate:f.activeCoordinate}))}else if("item"===e){var g,h=c.touches[0],i=document.elementFromPoint(h.clientX,h.clientY);if(!i||!i.getAttribute)return;var j=i.getAttribute(cR),k=null!=(g=i.getAttribute(cS))?g:void 0,l=lG(b.getState(),j,k);b.dispatch(bB({activeDataKey:k,activeIndex:j,activeCoordinate:l}))}}});var lJ=N({brush:lg,cartesianAxis:kX,chartData:bO,graphicalItems:k4,layout:bU,legend:lm,options:bm,polarAxis:lw,polarOptions:lz,referenceElements:lc,rootProps:lp,tooltip:bJ}),lK=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(a){let b,c,d,e=function(a){let{thunk:b=!0,immutableCheck:c=!0,serializableCheck:d=!0,actionCreatorCheck:e=!0}=a??{},f=new aH;return b&&("boolean"==typeof b?f.push(R):f.push(Q(b.extraArgument))),f},{reducer:f,middleware:g,devTools:h=!0,duplicateMiddlewareCheck:i=!0,preloadedState:j,enhancers:k}=a||{};if("function"==typeof f)b=f;else if(M(f))b=N(f);else throw Error(bj(1));c="function"==typeof g?g(e):e();let l=O;h&&(l=aF({trace:!1,..."object"==typeof h&&h}));let m=(d=function(...a){return b=>(c,d)=>{let e=b(c,d),f=()=>{throw Error(I(15))},g={getState:e.getState,dispatch:(a,...b)=>f(a,...b)};return f=O(...a.map(a=>a(g)))(e.dispatch),{...e,dispatch:f}}}(...c),function(a){let{autoBatch:b=!0}=a??{},c=new aH(d);return b&&c.push(((a={type:"raf"})=>b=>(...c)=>{let d=b(...c),e=!0,f=!1,g=!1,h=new Set,i="tick"===a.type?queueMicrotask:"raf"===a.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:aK(10):"callback"===a.type?a.queueNotification:aK(a.timeout),j=()=>{g=!1,f&&(f=!1,h.forEach(a=>a()))};return Object.assign({},d,{subscribe(a){let b=d.subscribe(()=>e&&a());return h.add(a),()=>{b(),h.delete(a)}},dispatch(a){try{return(f=!(e=!a?.meta?.RTK_autoBatch))&&!g&&(g=!0,i(j)),d.dispatch(a)}finally{e=!0}}})})("object"==typeof b?b:void 0)),c});return function a(b,c,d){if("function"!=typeof b)throw Error(I(2));if("function"==typeof c&&"function"==typeof d||"function"==typeof d&&"function"==typeof arguments[3])throw Error(I(0));if("function"==typeof c&&void 0===d&&(d=c,c=void 0),void 0!==d){if("function"!=typeof d)throw Error(I(1));return d(a)(b,c)}let e=b,f=c,g=new Map,h=g,i=0,j=!1;function k(){h===g&&(h=new Map,g.forEach((a,b)=>{h.set(b,a)}))}function l(){if(j)throw Error(I(3));return f}function m(a){if("function"!=typeof a)throw Error(I(4));if(j)throw Error(I(5));let b=!0;k();let c=i++;return h.set(c,a),function(){if(b){if(j)throw Error(I(6));b=!1,k(),h.delete(c),g=null}}}function n(a){if(!M(a))throw Error(I(7));if(void 0===a.type)throw Error(I(8));if("string"!=typeof a.type)throw Error(I(17));if(j)throw Error(I(9));try{j=!0,f=e(f,a)}finally{j=!1}return(g=h).forEach(a=>{a()}),a}return n({type:L.INIT}),{dispatch:n,subscribe:m,getState:l,replaceReducer:function(a){if("function"!=typeof a)throw Error(I(10));e=a,n({type:L.REPLACE})},[J]:function(){return{subscribe(a){if("object"!=typeof a||null===a)throw Error(I(11));function b(){a.next&&a.next(l())}return b(),{unsubscribe:m(b)}},[J](){return this}}}}}(b,j,l(..."function"==typeof k?k(m):m()))}({reducer:lJ,preloadedState:a,middleware:a=>a({serializableCheck:!1}).concat([kJ.middleware,kL.middleware,lC.middleware,lE.middleware,lI.middleware]),devTools:{serialize:{replacer:kM},name:"recharts-".concat(b)}})};function lL(a){var{preloadedState:b,children:c,reduxStoreName:d}=a,e=cZ(),f=(0,n.useRef)(null);return e?c:(null==f.current&&(f.current=lK(b,d)),n.createElement(bv,{context:cd,store:f.current},c))}var lM=a=>{var{chartData:b}=a,c=cf(),d=cZ();return(0,n.useEffect)(()=>d?()=>{}:(c(bL(b)),()=>{c(bL(void 0))}),[b,c,d]),null};function lN(a){var{layout:b,width:c,height:d,margin:e}=a;return cf(),cZ(),null}function lO(a){return cf(),null}function lP(a){return cf(),null}var lQ=c(29632),lR=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],lS=["points","pathLength"],lT={svg:["viewBox","children"],polygon:lS,polyline:lS},lU=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],lV=(a,b)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var c=a;if((0,n.isValidElement)(a)&&(c=a.props),"object"!=typeof c&&"function"!=typeof c)return null;var d={};return Object.keys(c).forEach(a=>{lU.includes(a)&&(d[a]=b||(b=>c[a](c,b)))}),d},lW=(a,b,c)=>{if(null===a||"object"!=typeof a&&"function"!=typeof a)return null;var d=null;return Object.keys(a).forEach(e=>{var f=a[e];lU.includes(e)&&"function"==typeof f&&(d||(d={}),d[e]=a=>(f(b,c,a),null))}),d},lX=a=>"string"==typeof a?a:a?a.displayName||a.name||"Component":"",lY=null,lZ=null,l$=a=>{if(a===lY&&Array.isArray(lZ))return lZ;var b=[];return n.Children.forEach(a,a=>{null==a||((0,lQ.isFragment)(a)?b=b.concat(l$(a.props.children)):b.push(a))}),lZ=b,lY=a,b};function l_(a,b){var c=[],d=[];return d=Array.isArray(b)?b.map(a=>lX(a)):[lX(b)],l$(a).forEach(a=>{var b=r()(a,"type.displayName")||r()(a,"type.name");-1!==d.indexOf(b)&&c.push(a)}),c}var l0=a=>!a||"object"!=typeof a||!("clipDot"in a)||!!a.clipDot,l1=(a,b,c)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var d=a;if((0,n.isValidElement)(a)&&(d=a.props),"object"!=typeof d&&"function"!=typeof d)return null;var e={};return Object.keys(d).forEach(a=>{var f;((a,b,c,d)=>{var e,f=null!=(e=d&&(null==lT?void 0:lT[d]))?e:[];return b.startsWith("data-")||"function"!=typeof a&&(d&&f.includes(b)||lR.includes(b))||c&&lU.includes(b)})(null==(f=d)?void 0:f[a],a,b,c)&&(e[a]=d[a])}),e},l2=()=>cj(a=>a.rootProps.accessibilityLayer),l3=["children","width","height","viewBox","className","style","title","desc"];function l4(){return(l4=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var l5=(0,n.forwardRef)((a,b)=>{var{children:c,width:d,height:e,viewBox:f,className:g,style:h,title:i,desc:j}=a,k=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,l3),l=f||{width:d,height:e,x:0,y:0},o=(0,m.$)("recharts-surface",g);return n.createElement("svg",l4({},l1(k,!0,"svg"),{className:o,width:d,height:e,style:h,viewBox:"".concat(l.x," ").concat(l.y," ").concat(l.width," ").concat(l.height),ref:b}),n.createElement("title",null,i),n.createElement("desc",null,j),c)}),l6=["children"];function l7(){return(l7=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var l8={width:"100%",height:"100%"},l9=(0,n.forwardRef)((a,b)=>{var c,d,e=c3(),f=c4(),g=l2();if(!gR(e)||!gR(f))return null;var{children:h,otherAttributes:i,title:j,desc:k}=a;return c="number"==typeof i.tabIndex?i.tabIndex:g?0:void 0,d="string"==typeof i.role?i.role:g?"application":void 0,n.createElement(l5,l7({},i,{title:j,desc:k,role:d,tabIndex:c,width:e,height:f,style:l8,ref:b}),h)}),ma=a=>{var{children:b}=a,c=cj(c_);if(!c)return null;var{width:d,height:e,y:f,x:g}=c;return n.createElement(l5,{width:d,height:e,x:g,y:f},b)},mb=(0,n.forwardRef)((a,b)=>{var{children:c}=a,d=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,l6);return cZ()?n.createElement(ma,null,c):n.createElement(l9,l7({ref:b},d),c)});function mc(a){return a.tooltip.syncInteraction}new(c(11117));var md=(0,n.createContext)(null),me=(0,n.createContext)(null);function mf(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var mg=(0,n.forwardRef)((a,b)=>{var{children:c,className:d,height:e,onClick:f,onContextMenu:g,onDoubleClick:h,onMouseDown:i,onMouseEnter:j,onMouseLeave:k,onMouseMove:l,onMouseUp:o,onTouchEnd:p,onTouchMove:q,onTouchStart:r,style:s,width:t}=a,u=cf(),[v,w]=(0,n.useState)(null),[x,y]=(0,n.useState)(null);cf(),cj(hK),cj(hM),cf(),cj(hL),cj(kg),c6(),c0(),cj(a=>a.rootProps.className),cj(hK),cj(hM),cf();var z=function(){cf();var[a,b]=(0,n.useState)(null);return cj(cN),b}(),A=(0,n.useCallback)(a=>{z(a),"function"==typeof b&&b(a),w(a),y(a)},[z,b,w,y]),B=(0,n.useCallback)(a=>{u(kI(a)),u(lD({handler:f,reactEvent:a}))},[u,f]),C=(0,n.useCallback)(a=>{u(kK(a)),u(lD({handler:j,reactEvent:a}))},[u,j]),D=(0,n.useCallback)(a=>{u(bD()),u(lD({handler:k,reactEvent:a}))},[u,k]),E=(0,n.useCallback)(a=>{u(kK(a)),u(lD({handler:l,reactEvent:a}))},[u,l]),F=(0,n.useCallback)(()=>{u(lB())},[u]),G=(0,n.useCallback)(a=>{u(lA(a.key))},[u]),H=(0,n.useCallback)(a=>{u(lD({handler:g,reactEvent:a}))},[u,g]),I=(0,n.useCallback)(a=>{u(lD({handler:h,reactEvent:a}))},[u,h]),J=(0,n.useCallback)(a=>{u(lD({handler:i,reactEvent:a}))},[u,i]),K=(0,n.useCallback)(a=>{u(lD({handler:o,reactEvent:a}))},[u,o]),L=(0,n.useCallback)(a=>{u(lD({handler:r,reactEvent:a}))},[u,r]),M=(0,n.useCallback)(a=>{u(lH(a)),u(lD({handler:q,reactEvent:a}))},[u,q]),N=(0,n.useCallback)(a=>{u(lD({handler:p,reactEvent:a}))},[u,p]);return n.createElement(md.Provider,{value:v},n.createElement(me.Provider,{value:x},n.createElement("div",{className:(0,m.$)("recharts-wrapper",d),style:function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mf(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mf(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({position:"relative",cursor:"default",width:t,height:e},s),onClick:B,onContextMenu:H,onDoubleClick:I,onFocus:F,onKeyDown:G,onMouseDown:J,onMouseEnter:C,onMouseLeave:D,onMouseMove:E,onMouseUp:K,onTouchEnd:N,onTouchMove:M,onTouchStart:L,ref:A},c)))}),mh=ca([cV],a=>{if(a)return{top:a.top,bottom:a.bottom,left:a.left,right:a.right}}),mi=ca([mh,cL,cM],(a,b,c)=>{if(a&&null!=b&&null!=c)return{x:a.left,y:a.top,width:Math.max(0,b-a.left-a.right),height:Math.max(0,c-a.top-a.bottom)}}),mj=()=>cj(mi),mk=(0,n.createContext)(void 0),ml=a=>{var{children:b}=a,[c]=(0,n.useState)("".concat(y("recharts"),"-clip")),d=mj();if(null==d)return null;var{x:e,y:f,width:g,height:h}=d;return n.createElement(mk.Provider,{value:c},n.createElement("defs",null,n.createElement("clipPath",{id:c},n.createElement("rect",{x:e,y:f,height:h,width:g}))),b)},mm=["children","className","width","height","style","compact","title","desc"],mn=(0,n.forwardRef)((a,b)=>{var{children:c,className:d,width:e,height:f,style:g,compact:h,title:i,desc:j}=a,k=l1(function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,mm),!1);return h?n.createElement(mb,{otherAttributes:k,title:i,desc:j},c):n.createElement(mg,{className:d,style:g,width:e,height:f,onClick:a.onClick,onMouseLeave:a.onMouseLeave,onMouseEnter:a.onMouseEnter,onMouseMove:a.onMouseMove,onMouseDown:a.onMouseDown,onMouseUp:a.onMouseUp,onContextMenu:a.onContextMenu,onDoubleClick:a.onDoubleClick,onTouchStart:a.onTouchStart,onTouchMove:a.onTouchMove,onTouchEnd:a.onTouchEnd},n.createElement(mb,{otherAttributes:k,title:i,desc:j,ref:b},n.createElement(ml,null,c)))});function mo(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function mp(a,b){var c=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mo(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mo(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},a);return Object.keys(b).reduce((a,c)=>(void 0===a[c]&&void 0!==b[c]&&(a[c]=b[c]),a),c)}var mq=["width","height","layout"];function mr(){return(mr=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var ms={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},mt=(0,n.forwardRef)(function(a,b){var c,d=mp(a.categoricalChartProps,ms),{width:e,height:f,layout:g}=d,h=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(d,mq);if(!gR(e)||!gR(f))return null;var{chartName:i,defaultTooltipEventType:j,validateTooltipEventTypes:k,tooltipPayloadSearcher:l}=a;return n.createElement(lL,{preloadedState:{options:{chartName:i,defaultTooltipEventType:j,validateTooltipEventTypes:k,tooltipPayloadSearcher:l,eventEmitter:void 0}},reduxStoreName:null!=(c=d.id)?c:i},n.createElement(lM,{chartData:d.data}),n.createElement(lN,{width:e,height:f,layout:g,margin:d.margin}),n.createElement(lO,{accessibilityLayer:d.accessibilityLayer,barCategoryGap:d.barCategoryGap,maxBarSize:d.maxBarSize,stackOffset:d.stackOffset,barGap:d.barGap,barSize:d.barSize,syncId:d.syncId,syncMethod:d.syncMethod,className:d.className}),n.createElement(lP,{cx:d.cx,cy:d.cy,startAngle:d.startAngle,endAngle:d.endAngle,innerRadius:d.innerRadius,outerRadius:d.outerRadius}),n.createElement(mn,mr({width:e,height:f},h,{ref:b})))}),mu=["item"],mv={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},mw=(0,n.forwardRef)((a,b)=>{var c=mp(a,mv);return n.createElement(mt,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:mu,tooltipPayloadSearcher:bk,categoricalChartProps:c,ref:b})}),mx=a=>a.graphicalItems.polarItems,my=ca([h1,h2],ig),mz=ca([mx,ic,my],ij),mA=ca([mz],io),mB=ca([mA,gO],iq),mC=ca([mB,ic,mz],is),mD=ca([mB,ic,mz],(a,b,c)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var d;return{value:cA(a,null!=(d=b.dataKey)?d:c.dataKey),errorDomain:[]}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cA(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]}))),mE=()=>void 0,mF=ca([ic,iT,mE,mD,mE],iU),mG=ca([ic,c5,mB,mC,hI,h1,mF],iX),mH=ca([mG,ic,i$],i0);function mI(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function mJ(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mI(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mI(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}ca([ic,mG,mH,h1],i2);var mK=(a,b)=>b,mL=[],mM=(a,b,c)=>(null==c?void 0:c.length)===0?mL:c,mN=ca([gO,mK,mM],(a,b,c)=>{var d,{chartData:e}=a;if((d=(null==b?void 0:b.data)!=null&&b.data.length>0?b.data:e)&&d.length||null==c||(d=c.map(a=>mJ(mJ({},b.presentationProps),a.props))),null!=d)return d}),mO=ca([mN,mK,mM],(a,b,c)=>{if(null!=a)return a.map((a,d)=>{var e,f,g=cA(a,b.nameKey,b.name);return f=null!=c&&null!=(e=c[d])&&null!=(e=e.props)&&e.fill?c[d].props.fill:"object"==typeof a&&null!=a&&"fill"in a?a.fill:b.fill,{value:cK(g,b.dataKey),color:f,payload:a,type:b.legendType}})}),mP=ca([mx,mK],(a,b)=>{if(a.some(a=>"pie"===a.type&&b.dataKey===a.dataKey&&b.data===a.data))return b}),mQ=ca([mN,mP,mM,cV],(a,b,c,d)=>{if(null!=b&&null!=a)return function(a){var b,c,d,{pieSettings:e,displayedData:f,cells:g,offset:h}=a,{cornerRadius:i,startAngle:j,endAngle:k,dataKey:l,nameKey:m,tooltipType:n}=e,o=Math.abs(e.minAngle),p=s(k-j)*Math.min(Math.abs(k-j),360),q=Math.abs(p),r=f.length<=1?0:null!=(b=e.paddingAngle)?b:0,t=f.filter(a=>0!==cA(a,l,0)).length,u=q-t*o-(q>=360?t:t-1)*r,w=f.reduce((a,b)=>{var c=cA(b,l,0);return a+(v(c)?c:0)},0);return w>0&&(c=f.map((a,b)=>{var c,f=cA(a,l,0),k=cA(a,m,b),q=((a,b,c)=>{let d,e,f;var{top:g,left:h,width:i,height:j}=b,k=cx(i,j),l=h+z(a.cx,i,i/2),m=g+z(a.cy,j,j/2),n=z(a.innerRadius,k,0);return{cx:l,cy:m,innerRadius:n,outerRadius:(d=c,e=a.outerRadius,f=k,"function"==typeof e?e(d):z(e,f,.8*f)),maxRadius:a.maxRadius||Math.sqrt(i*i+j*j)/2}})(e,h,a),t=(v(f)?f:0)/w,x=o2(o2({},a),g&&g[b]&&g[b].props),y=(c=b?d.endAngle+s(p)*r*(0!==f):j)+s(p)*((0!==f?o:0)+t*u),A=(c+y)/2,B=(q.innerRadius+q.outerRadius)/2,C=[{name:k,value:f,payload:x,dataKey:l,type:n}],D=cw(q.cx,q.cy,B,A);return d=o2(o2(o2(o2({},e.presentationProps),{},{percent:t,cornerRadius:i,name:k,tooltipPayload:C,midAngle:A,middleRadius:B,tooltipPosition:D},x),q),{},{value:cA(a,l),startAngle:c,endAngle:y,payload:x,paddingAngle:s(p)*r})})),c}({offset:d,pieSettings:b,displayedData:a,cells:c})});function mR(a){return cf(),(0,n.useRef)(null),null}function mS(a){return cf(),null}var mT=["children","className"];function mU(){return(mU=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var mV=n.forwardRef((a,b)=>{var{children:c,className:d}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,mT),f=(0,m.$)("recharts-layer",d);return n.createElement("g",mU({className:f},l1(e,!0),{ref:b}),c)});function mW(){}function mX(a,b,c){a._context.bezierCurveTo((2*a._x0+a._x1)/3,(2*a._y0+a._y1)/3,(a._x0+2*a._x1)/3,(a._y0+2*a._y1)/3,(a._x0+4*a._x1+b)/6,(a._y0+4*a._y1+c)/6)}function mY(a){this._context=a}function mZ(a){this._context=a}function m$(a){this._context=a}mY.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:mX(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:mX(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},mZ.prototype={areaStart:mW,areaEnd:mW,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._x2=a,this._y2=b;break;case 1:this._point=2,this._x3=a,this._y3=b;break;case 2:this._point=3,this._x4=a,this._y4=b,this._context.moveTo((this._x0+4*this._x1+a)/6,(this._y0+4*this._y1+b)/6);break;default:mX(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},m$.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var c=(this._x0+4*this._x1+a)/6,d=(this._y0+4*this._y1+b)/6;this._line?this._context.lineTo(c,d):this._context.moveTo(c,d);break;case 3:this._point=4;default:mX(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}};class m_{constructor(a,b){this._context=a,this._x=b}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+a)/2,this._y0,this._x0,b,a,b):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+b)/2,a,this._y0,a,b)}this._x0=a,this._y0=b}}function m0(a){this._context=a}function m1(a){this._context=a}function m2(a){return new m1(a)}m0.prototype={areaStart:mW,areaEnd:mW,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(a,b){a*=1,b*=1,this._point?this._context.lineTo(a,b):(this._point=1,this._context.moveTo(a,b))}};function m3(a,b,c){var d=a._x1-a._x0,e=b-a._x1,f=(a._y1-a._y0)/(d||e<0&&-0),g=(c-a._y1)/(e||d<0&&-0);return((f<0?-1:1)+(g<0?-1:1))*Math.min(Math.abs(f),Math.abs(g),.5*Math.abs((f*e+g*d)/(d+e)))||0}function m4(a,b){var c=a._x1-a._x0;return c?(3*(a._y1-a._y0)/c-b)/2:b}function m5(a,b,c){var d=a._x0,e=a._y0,f=a._x1,g=a._y1,h=(f-d)/3;a._context.bezierCurveTo(d+h,e+h*b,f-h,g-h*c,f,g)}function m6(a){this._context=a}function m7(a){this._context=new m8(a)}function m8(a){this._context=a}function m9(a){this._context=a}function na(a){var b,c,d=a.length-1,e=Array(d),f=Array(d),g=Array(d);for(e[0]=0,f[0]=2,g[0]=a[0]+2*a[1],b=1;b<d-1;++b)e[b]=1,f[b]=4,g[b]=4*a[b]+2*a[b+1];for(e[d-1]=2,f[d-1]=7,g[d-1]=8*a[d-1]+a[d],b=1;b<d;++b)c=e[b]/f[b-1],f[b]-=c,g[b]-=c*g[b-1];for(e[d-1]=g[d-1]/f[d-1],b=d-2;b>=0;--b)e[b]=(g[b]-e[b+1])/f[b];for(b=0,f[d-1]=(a[d]+e[d-1])/2;b<d-1;++b)f[b]=2*a[b+1]-e[b+1];return[e,f]}function nb(a,b){this._context=a,this._t=b}m1.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._context.lineTo(a,b)}}},m6.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:m5(this,this._t0,m4(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){var c=NaN;if(b*=1,(a*=1)!==this._x1||b!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,m5(this,m4(this,c=m3(this,a,b)),c);break;default:m5(this,this._t0,c=m3(this,a,b))}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b,this._t0=c}}},(m7.prototype=Object.create(m6.prototype)).point=function(a,b){m6.prototype.point.call(this,b,a)},m8.prototype={moveTo:function(a,b){this._context.moveTo(b,a)},closePath:function(){this._context.closePath()},lineTo:function(a,b){this._context.lineTo(b,a)},bezierCurveTo:function(a,b,c,d,e,f){this._context.bezierCurveTo(b,a,d,c,f,e)}},m9.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var a=this._x,b=this._y,c=a.length;if(c)if(this._line?this._context.lineTo(a[0],b[0]):this._context.moveTo(a[0],b[0]),2===c)this._context.lineTo(a[1],b[1]);else for(var d=na(a),e=na(b),f=0,g=1;g<c;++f,++g)this._context.bezierCurveTo(d[0][f],e[0][f],d[1][f],e[1][f],a[g],b[g]);(this._line||0!==this._line&&1===c)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(a,b){this._x.push(+a),this._y.push(+b)}},nb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,b),this._context.lineTo(a,b);else{var c=this._x*(1-this._t)+a*this._t;this._context.lineTo(c,this._y),this._context.lineTo(c,b)}}this._x=a,this._y=b}};let nc=Math.PI,nd=2*nc,ne=nd-1e-6;function nf(a){this._+=a[0];for(let b=1,c=a.length;b<c;++b)this._+=arguments[b]+a[b]}class ng{constructor(a){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==a?nf:function(a){let b=Math.floor(a);if(!(b>=0))throw Error(`invalid digits: ${a}`);if(b>15)return nf;let c=10**b;return function(a){this._+=a[0];for(let b=1,d=a.length;b<d;++b)this._+=Math.round(arguments[b]*c)/c+a[b]}}(a)}moveTo(a,b){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(a,b){this._append`L${this._x1=+a},${this._y1=+b}`}quadraticCurveTo(a,b,c,d){this._append`Q${+a},${+b},${this._x1=+c},${this._y1=+d}`}bezierCurveTo(a,b,c,d,e,f){this._append`C${+a},${+b},${+c},${+d},${this._x1=+e},${this._y1=+f}`}arcTo(a,b,c,d,e){if(a*=1,b*=1,c*=1,d*=1,(e*=1)<0)throw Error(`negative radius: ${e}`);let f=this._x1,g=this._y1,h=c-a,i=d-b,j=f-a,k=g-b,l=j*j+k*k;if(null===this._x1)this._append`M${this._x1=a},${this._y1=b}`;else if(l>1e-6)if(Math.abs(k*h-i*j)>1e-6&&e){let m=c-f,n=d-g,o=h*h+i*i,p=Math.sqrt(o),q=Math.sqrt(l),r=e*Math.tan((nc-Math.acos((o+l-(m*m+n*n))/(2*p*q)))/2),s=r/q,t=r/p;Math.abs(s-1)>1e-6&&this._append`L${a+s*j},${b+s*k}`,this._append`A${e},${e},0,0,${+(k*m>j*n)},${this._x1=a+t*h},${this._y1=b+t*i}`}else this._append`L${this._x1=a},${this._y1=b}`}arc(a,b,c,d,e,f){if(a*=1,b*=1,c*=1,f=!!f,c<0)throw Error(`negative radius: ${c}`);let g=c*Math.cos(d),h=c*Math.sin(d),i=a+g,j=b+h,k=1^f,l=f?d-e:e-d;null===this._x1?this._append`M${i},${j}`:(Math.abs(this._x1-i)>1e-6||Math.abs(this._y1-j)>1e-6)&&this._append`L${i},${j}`,c&&(l<0&&(l=l%nd+nd),l>ne?this._append`A${c},${c},0,1,${k},${a-g},${b-h}A${c},${c},0,1,${k},${this._x1=i},${this._y1=j}`:l>1e-6&&this._append`A${c},${c},0,${+(l>=nc)},${k},${this._x1=a+c*Math.cos(e)},${this._y1=b+c*Math.sin(e)}`)}rect(a,b,c,d){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}h${c*=1}v${+d}h${-c}Z`}toString(){return this._}}function nh(a){let b=3;return a.digits=function(c){if(!arguments.length)return b;if(null==c)b=null;else{let a=Math.floor(c);if(!(a>=0))throw RangeError(`invalid digits: ${c}`);b=a}return a},()=>new ng(b)}function ni(a){return a[0]}function nj(a){return a[1]}function nk(a,b){var c=cp(!0),d=null,e=m2,f=null,g=nh(h);function h(h){var i,j,k,l=(h=co(h)).length,m=!1;for(null==d&&(f=e(k=g())),i=0;i<=l;++i)!(i<l&&c(j=h[i],i,h))===m&&((m=!m)?f.lineStart():f.lineEnd()),m&&f.point(+a(j,i,h),+b(j,i,h));if(k)return f=null,k+""||null}return a="function"==typeof a?a:void 0===a?ni:cp(a),b="function"==typeof b?b:void 0===b?nj:cp(b),h.x=function(b){return arguments.length?(a="function"==typeof b?b:cp(+b),h):a},h.y=function(a){return arguments.length?(b="function"==typeof a?a:cp(+a),h):b},h.defined=function(a){return arguments.length?(c="function"==typeof a?a:cp(!!a),h):c},h.curve=function(a){return arguments.length?(e=a,null!=d&&(f=e(d)),h):e},h.context=function(a){return arguments.length?(null==a?d=f=null:f=e(d=a),h):d},h}function nl(a,b,c){var d=null,e=cp(!0),f=null,g=m2,h=null,i=nh(j);function j(j){var k,l,m,n,o,p=(j=co(j)).length,q=!1,r=Array(p),s=Array(p);for(null==f&&(h=g(o=i())),k=0;k<=p;++k){if(!(k<p&&e(n=j[k],k,j))===q)if(q=!q)l=k,h.areaStart(),h.lineStart();else{for(h.lineEnd(),h.lineStart(),m=k-1;m>=l;--m)h.point(r[m],s[m]);h.lineEnd(),h.areaEnd()}q&&(r[k]=+a(n,k,j),s[k]=+b(n,k,j),h.point(d?+d(n,k,j):r[k],c?+c(n,k,j):s[k]))}if(o)return h=null,o+""||null}function k(){return nk().defined(e).curve(g).context(f)}return a="function"==typeof a?a:void 0===a?ni:cp(+a),b="function"==typeof b?b:void 0===b?cp(0):cp(+b),c="function"==typeof c?c:void 0===c?nj:cp(+c),j.x=function(b){return arguments.length?(a="function"==typeof b?b:cp(+b),d=null,j):a},j.x0=function(b){return arguments.length?(a="function"==typeof b?b:cp(+b),j):a},j.x1=function(a){return arguments.length?(d=null==a?null:"function"==typeof a?a:cp(+a),j):d},j.y=function(a){return arguments.length?(b="function"==typeof a?a:cp(+a),c=null,j):b},j.y0=function(a){return arguments.length?(b="function"==typeof a?a:cp(+a),j):b},j.y1=function(a){return arguments.length?(c=null==a?null:"function"==typeof a?a:cp(+a),j):c},j.lineX0=j.lineY0=function(){return k().x(a).y(b)},j.lineY1=function(){return k().x(a).y(c)},j.lineX1=function(){return k().x(d).y(b)},j.defined=function(a){return arguments.length?(e="function"==typeof a?a:cp(!!a),j):e},j.curve=function(a){return arguments.length?(g=a,null!=f&&(h=g(f)),j):g},j.context=function(a){return arguments.length?(null==a?f=h=null:h=g(f=a),j):f},j}function nm(){return(nm=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nn(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function no(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nn(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nn(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}ng.prototype;var np={curveBasisClosed:function(a){return new mZ(a)},curveBasisOpen:function(a){return new m$(a)},curveBasis:function(a){return new mY(a)},curveBumpX:function(a){return new m_(a,!0)},curveBumpY:function(a){return new m_(a,!1)},curveLinearClosed:function(a){return new m0(a)},curveLinear:m2,curveMonotoneX:function(a){return new m6(a)},curveMonotoneY:function(a){return new m7(a)},curveNatural:function(a){return new m9(a)},curveStep:function(a){return new nb(a,.5)},curveStepAfter:function(a){return new nb(a,1)},curveStepBefore:function(a){return new nb(a,0)}},nq=a=>gQ(a.x)&&gQ(a.y),nr=a=>a.x,ns=a=>a.y,nt=a=>{var{className:b,points:c,path:d,pathRef:e}=a;if((!c||!c.length)&&!d)return null;var f=c&&c.length?(a=>{var b,{type:c="linear",points:d=[],baseLine:e,layout:f,connectNulls:g=!1}=a,h=((a,b)=>{if("function"==typeof a)return a;var c="curve".concat(D(a));return("curveMonotone"===c||"curveBump"===c)&&b?np["".concat(c).concat("vertical"===b?"Y":"X")]:np[c]||m2})(c,f),i=g?d.filter(nq):d;if(Array.isArray(e)){var j=g?e.filter(a=>nq(a)):e,k=i.map((a,b)=>no(no({},a),{},{base:j[b]}));return(b="vertical"===f?nl().y(ns).x1(nr).x0(a=>a.base.x):nl().x(nr).y1(ns).y0(a=>a.base.y)).defined(nq).curve(h),b(k)}return(b="vertical"===f&&v(e)?nl().y(ns).x1(nr).x0(e):v(e)?nl().x(nr).y1(ns).y0(e):nk().x(nr).y(ns)).defined(nq).curve(h),b(i)})(a):d;return n.createElement("path",nm({},l1(a,!1),lV(a),{className:(0,m.$)("recharts-curve",b),d:null===f?void 0:f,ref:e}))},nu={isSsr:!0};function nv(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function nw(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nv(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nv(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var nx={widthCache:{},cacheCount:0},ny={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nz="recharts_measurement_span",nA=function(a){var b,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==a||nu.isSsr)return{width:0,height:0};var d=(Object.keys(b=nw({},c)).forEach(a=>{b[a]||delete b[a]}),b),e=JSON.stringify({text:a,copyStyle:d});if(nx.widthCache[e])return nx.widthCache[e];try{var f=document.getElementById(nz);f||((f=document.createElement("span")).setAttribute("id",nz),f.setAttribute("aria-hidden","true"),document.body.appendChild(f));var g=nw(nw({},ny),d);Object.assign(f.style,g),f.textContent="".concat(a);var h=f.getBoundingClientRect(),i={width:h.width,height:h.height};return nx.widthCache[e]=i,++nx.cacheCount>2e3&&(nx.cacheCount=0,nx.widthCache={}),i}catch(a){return{width:0,height:0}}},nB=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nC=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nD=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nE=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nF={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nG=Object.keys(nF);class nH{static parse(a){var b,[,c,d]=null!=(b=nE.exec(a))?b:[];return new nH(parseFloat(c),null!=d?d:"")}constructor(a,b){this.num=a,this.unit=b,this.num=a,this.unit=b,t(a)&&(this.unit=""),""===b||nD.test(b)||(this.num=NaN,this.unit=""),nG.includes(b)&&(this.num=a*nF[b],this.unit="px")}add(a){return this.unit!==a.unit?new nH(NaN,""):new nH(this.num+a.num,this.unit)}subtract(a){return this.unit!==a.unit?new nH(NaN,""):new nH(this.num-a.num,this.unit)}multiply(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new nH(NaN,""):new nH(this.num*a.num,this.unit||a.unit)}divide(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new nH(NaN,""):new nH(this.num/a.num,this.unit||a.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return t(this.num)}}function nI(a){if(a.includes("NaN"))return"NaN";for(var b=a;b.includes("*")||b.includes("/");){var c,[,d,e,f]=null!=(c=nB.exec(b))?c:[],g=nH.parse(null!=d?d:""),h=nH.parse(null!=f?f:""),i="*"===e?g.multiply(h):g.divide(h);if(i.isNaN())return"NaN";b=b.replace(nB,i.toString())}for(;b.includes("+")||/.-\d+(?:\.\d+)?/.test(b);){var j,[,k,l,m]=null!=(j=nC.exec(b))?j:[],n=nH.parse(null!=k?k:""),o=nH.parse(null!=m?m:""),p="+"===l?n.add(o):n.subtract(o);if(p.isNaN())return"NaN";b=b.replace(nC,p.toString())}return b}var nJ=/\(([^()]*)\)/;function nK(a){var b=function(a){try{var b;return b=a.replace(/\s+/g,""),b=function(a){for(var b,c=a;null!=(b=nJ.exec(c));){var[,d]=b;c=c.replace(nJ,nI(d))}return c}(b),b=nI(b)}catch(a){return"NaN"}}(a.slice(5,-1));return"NaN"===b?"":b}var nL=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nM=["dx","dy","angle","className","breakAll"];function nN(){return(nN=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nO(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var nP=/[ \f\n\r\t\v\u2028\u2029]+/,nQ=a=>{var{children:b,breakAll:c,style:d}=a;try{let a;var e=[];a=b,null==a||(e=c?b.toString().split(""):b.toString().split(nP));var f=e.map(a=>({word:a,width:nA(a,d).width})),g=c?0:nA("\xa0",d).width;return{wordsWithComputedWidth:f,spaceWidth:g}}catch(a){return null}},nR=a=>[{words:null==a?[]:a.toString().split(nP)}],nS="#808080",nT=(0,n.forwardRef)((a,b)=>{var c,{x:d=0,y:e=0,lineHeight:f="1em",capHeight:g="0.71em",scaleToFit:h=!1,textAnchor:i="start",verticalAnchor:j="end",fill:k=nS}=a,l=nO(a,nL),o=(0,n.useMemo)(()=>(a=>{var{width:b,scaleToFit:c,children:d,style:e,breakAll:f,maxLines:g}=a;if((b||c)&&!nu.isSsr){var h=nQ({breakAll:f,children:d,style:e});if(!h)return nR(d);var{wordsWithComputedWidth:i,spaceWidth:j}=h;return((a,b,c,d,e)=>{var f,{maxLines:g,children:h,style:i,breakAll:j}=a,k=v(g),l=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return a.reduce((a,b)=>{var{word:f,width:g}=b,h=a[a.length-1];return h&&(null==d||e||h.width+g+c<Number(d))?(h.words.push(f),h.width+=g+c):a.push({words:[f],width:g}),a},[])},m=l(b),n=a=>a.reduce((a,b)=>a.width>b.width?a:b);if(!k||e||!(m.length>g||n(m).width>Number(d)))return m;for(var o=a=>{var b=l(nQ({breakAll:j,style:i,children:h.slice(0,a)+"…"}).wordsWithComputedWidth);return[b.length>g||n(b).width>Number(d),b]},p=0,q=h.length-1,r=0;p<=q&&r<=h.length-1;){var s=Math.floor((p+q)/2),[t,u]=o(s-1),[w]=o(s);if(t||w||(p=s+1),t&&w&&(q=s-1),!t&&w){f=u;break}r++}return f||m})({breakAll:f,children:d,maxLines:g,style:e},i,j,b,c)}return nR(d)})({breakAll:l.breakAll,children:l.children,maxLines:l.maxLines,scaleToFit:h,style:l.style,width:l.width}),[l.breakAll,l.children,l.maxLines,h,l.style,l.width]),{dx:p,dy:q,angle:r,className:s,breakAll:t}=l,u=nO(l,nM);if(!w(d)||!w(e))return null;var x=d+(v(p)?p:0),y=e+(v(q)?q:0);switch(j){case"start":c=nK("calc(".concat(g,")"));break;case"middle":c=nK("calc(".concat((o.length-1)/2," * -").concat(f," + (").concat(g," / 2))"));break;default:c=nK("calc(".concat(o.length-1," * -").concat(f,")"))}var z=[];if(h){var A=o[0].width,{width:B}=l;z.push("scale(".concat(v(B)?B/A:1,")"))}return r&&z.push("rotate(".concat(r,", ").concat(x,", ").concat(y,")")),z.length&&(u.transform=z.join(" ")),n.createElement("text",nN({},l1(u,!0),{ref:b,x:x,y:y,className:(0,m.$)("recharts-text",s),textAnchor:i,fill:k.includes("url")?nS:k}),o.map((a,b)=>{var d=a.words.join(t?"":" ");return n.createElement("tspan",{x:x,dy:0===b?c:f,key:"".concat(d,"-").concat(b)},d)}))});nT.displayName="Text";var nU=a=>null;nU.displayName="Cell";var nV=c(92867),nW=c.n(nV),nX=c(12728),nY=c.n(nX),nZ=(a,b)=>[0,3*a,3*b-6*a,3*a-3*b+1],n$=(a,b)=>a.map((a,c)=>a*b**c).reduce((a,b)=>a+b),n_=(a,b)=>c=>n$(nZ(a,b),c),n0=function(){let a,b;for(var c,d,e,f,g=arguments.length,h=Array(g),i=0;i<g;i++)h[i]=arguments[i];if(1===h.length)switch(h[0]){case"linear":[c,e,d,f]=[0,0,1,1];break;case"ease":[c,e,d,f]=[.25,.1,.25,1];break;case"ease-in":[c,e,d,f]=[.42,0,1,1];break;case"ease-out":[c,e,d,f]=[.42,0,.58,1];break;case"ease-in-out":[c,e,d,f]=[0,0,.58,1];break;default:var j=h[0].split("(");"cubic-bezier"===j[0]&&4===j[1].split(")")[0].split(",").length&&([c,e,d,f]=j[1].split(")")[0].split(",").map(a=>parseFloat(a)))}else 4===h.length&&([c,e,d,f]=h);var k=n_(c,d),l=n_(e,f),m=(a=c,b=d,c=>n$([...nZ(a,b).map((a,b)=>a*b).slice(1),0],c)),n=a=>a>1?1:a<0?0:a,o=a=>{for(var b=a>1?1:a,c=b,d=0;d<8;++d){var e=k(c)-b,f=m(c);if(1e-4>Math.abs(e-b)||f<1e-4)break;c=n(c-e/f)}return l(c)};return o.isStepper=!1,o},n1=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:b=100,damping:c=8,dt:d=17}=a,e=(a,e,f)=>{var g=f+(-(a-e)*b-f*c)*d/1e3,h=f*d/1e3+a;return 1e-4>Math.abs(h-e)&&1e-4>Math.abs(g)?[e,0]:[h,g]};return e.isStepper=!0,e.dt=d,e};function n2(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function n3(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?n2(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):n2(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var n4=(a,b)=>Object.keys(b).reduce((c,d)=>n3(n3({},c),{},{[d]:a(d,b[d])}),{});function n5(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function n6(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?n5(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):n5(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var n7=(a,b,c)=>a+(b-a)*c,n8=a=>{var{from:b,to:c}=a;return b!==c},n9=(a,b,c)=>{var d=n4((b,c)=>{if(n8(c)){var[d,e]=a(c.from,c.to,c.velocity);return n6(n6({},c),{},{from:d,velocity:e})}return c},b);return c<1?n4((a,b)=>n8(b)?n6(n6({},b),{},{velocity:n7(b.velocity,d[a].velocity,c),from:n7(b.from,d[a].from,c)}):b,b):n9(a,d,c-1)};class oa{setTimeout(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,c=performance.now(),d=null,e=f=>{f-c>=b?a(f):"function"==typeof requestAnimationFrame&&(d=requestAnimationFrame(e))};return d=requestAnimationFrame(e),()=>{cancelAnimationFrame(d)}}}var ob=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function oc(){return(oc=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function od(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oe(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?od(Object(c),!0).forEach(function(b){of(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):od(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function of(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class og extends n.PureComponent{constructor(a,b){super(a,b),of(this,"mounted",!1),of(this,"manager",null),of(this,"stopJSAnimation",null),of(this,"unSubscribe",null);var{isActive:c,attributeName:d,from:e,to:f,children:g,duration:h,animationManager:i}=this.props;if(this.manager=i,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!c||h<=0){this.state={style:{}},"function"==typeof g&&(this.state={style:f});return}if(e){if("function"==typeof g){this.state={style:e};return}this.state={style:d?{[d]:e}:e}}else this.state={style:{}}}componentDidMount(){var{isActive:a,canBegin:b}=this.props;this.mounted=!0,a&&b&&this.runAnimation(this.props)}componentDidUpdate(a){var{isActive:b,canBegin:c,attributeName:d,shouldReAnimate:e,to:f,from:g}=this.props,{style:h}=this.state;if(c){if(!b){this.state&&h&&(d&&h[d]!==f||!d&&h!==f)&&this.setState({style:d?{[d]:f}:f});return}if(!nY()(a.to,f)||!a.canBegin||!a.isActive){var i=!a.canBegin||!a.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var j=i||e?g:a.to;this.state&&h&&(d&&h[d]!==j||!d&&h!==j)&&this.setState({style:d?{[d]:j}:j}),this.runAnimation(oe(oe({},this.props),{},{from:j,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:a}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),a&&a()}handleStyleChange(a){this.changeStyle(a)}changeStyle(a){this.mounted&&this.setState({style:a})}runJSAnimation(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,{from:A,to:B,duration:C,easing:D,begin:E,onAnimationEnd:F,onAnimationStart:G}=a,H=(w=(a=>{if("string"==typeof a)switch(a){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return n0(a);case"spring":return n1();default:if("cubic-bezier"===a.split("(")[0])return n0(a)}return"function"==typeof a?a:null})(D),x=this.changeStyle,y=this.manager.getTimeoutController(),z=[Object.keys(A),Object.keys(B)].reduce((a,b)=>a.filter(a=>b.includes(a))),!0===w.isStepper?(b=A,c=B,d=w,e=z,f=x,g=y,i=e.reduce((a,d)=>n6(n6({},a),{},{[d]:{from:b[d],velocity:0,to:c[d]}}),{}),j=null,k=a=>{h||(h=a);var e=(a-h)/d.dt;i=n9(d,i,e),f(n6(n6(n6({},b),c),n4((a,b)=>b.from,i))),h=a,Object.values(i).filter(n8).length&&(j=g.setTimeout(k))},()=>(j=g.setTimeout(k),()=>{j()})):(l=A,m=B,n=w,o=C,p=z,q=x,r=y,t=null,u=p.reduce((a,b)=>n6(n6({},a),{},{[b]:[l[b],m[b]]}),{}),v=a=>{s||(s=a);var b=(a-s)/o,c=n4((a,c)=>n7(...c,n(b)),u);if(q(n6(n6(n6({},l),m),c)),b<1)t=r.setTimeout(v);else{var d=n4((a,b)=>n7(...b,n(1)),u);q(n6(n6(n6({},l),m),d))}},()=>(t=r.setTimeout(v),()=>{t()}))),I=()=>{this.stopJSAnimation=H()};this.manager.start([G,E,I,C,F])}runAnimation(a){let b;var{begin:c,duration:d,attributeName:e,to:f,easing:g,onAnimationStart:h,onAnimationEnd:i,children:j}=a;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof g||"function"==typeof j||"spring"===g)return void this.runJSAnimation(a);var k=e?{[e]:f}:f,l=(b=Object.keys(k),b.map(a=>"".concat(a.replace(/([A-Z])/g,a=>"-".concat(a.toLowerCase()))," ").concat(d,"ms ").concat(g)).join(","));this.manager.start([h,c,oe(oe({},k),{},{transition:l}),d,i])}render(){var a=this.props,{children:b,begin:c,duration:d,attributeName:e,easing:f,isActive:g,from:h,to:i,canBegin:j,onAnimationEnd:k,shouldReAnimate:l,onAnimationReStart:m,animationManager:o}=a,p=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,ob),q=n.Children.count(b),r=this.state.style;if("function"==typeof b)return b(r);if(!g||0===q||d<=0)return b;var s=a=>{var{style:b={},className:c}=a.props;return(0,n.cloneElement)(a,oe(oe({},p),{},{style:oe(oe({},b),r),className:c}))};return 1===q?s(n.Children.only(b)):n.createElement("div",null,n.Children.map(b,a=>s(a)))}}of(og,"displayName","Animate"),of(og,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var oh=(0,n.createContext)(null);function oi(a){var b,c,d,e,f,g,h,i=(0,n.useContext)(oh);return n.createElement(og,oc({},a,{animationManager:null!=(g=null!=(h=a.animationManager)?h:i)?g:(b=new oa,c=()=>null,d=!1,e=null,f=a=>{if(!d){if(Array.isArray(a)){if(!a.length)return;var[g,...h]=a;if("number"==typeof g){e=b.setTimeout(f.bind(null,h),g);return}f(g),e=b.setTimeout(f.bind(null,h));return}"object"==typeof a&&c(a),"function"==typeof a&&a()}},{stop:()=>{d=!0},start:a=>{d=!1,e&&(e(),e=null),f(a)},subscribe:a=>(c=a,()=>{c=()=>null}),getTimeoutController:()=>b})}))}function oj(){return(oj=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var ok=(a,b,c,d,e)=>{var f,g=Math.min(Math.abs(c)/2,Math.abs(d)/2),h=d>=0?1:-1,i=c>=0?1:-1,j=+(d>=0&&c>=0||d<0&&c<0);if(g>0&&e instanceof Array){for(var k=[0,0,0,0],l=0;l<4;l++)k[l]=e[l]>g?g:e[l];f="M".concat(a,",").concat(b+h*k[0]),k[0]>0&&(f+="A ".concat(k[0],",").concat(k[0],",0,0,").concat(j,",").concat(a+i*k[0],",").concat(b)),f+="L ".concat(a+c-i*k[1],",").concat(b),k[1]>0&&(f+="A ".concat(k[1],",").concat(k[1],",0,0,").concat(j,",\n        ").concat(a+c,",").concat(b+h*k[1])),f+="L ".concat(a+c,",").concat(b+d-h*k[2]),k[2]>0&&(f+="A ".concat(k[2],",").concat(k[2],",0,0,").concat(j,",\n        ").concat(a+c-i*k[2],",").concat(b+d)),f+="L ".concat(a+i*k[3],",").concat(b+d),k[3]>0&&(f+="A ".concat(k[3],",").concat(k[3],",0,0,").concat(j,",\n        ").concat(a,",").concat(b+d-h*k[3])),f+="Z"}else if(g>0&&e===+e&&e>0){var m=Math.min(g,e);f="M ".concat(a,",").concat(b+h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+i*m,",").concat(b,"\n            L ").concat(a+c-i*m,",").concat(b,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c,",").concat(b+h*m,"\n            L ").concat(a+c,",").concat(b+d-h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c-i*m,",").concat(b+d,"\n            L ").concat(a+i*m,",").concat(b+d,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a,",").concat(b+d-h*m," Z")}else f="M ".concat(a,",").concat(b," h ").concat(c," v ").concat(d," h ").concat(-c," Z");return f},ol={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},om=a=>{var b=mp(a,ol),c=(0,n.useRef)(null),[d,e]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&e(a)}catch(a){}},[]);var{x:f,y:g,width:h,height:i,radius:j,className:k}=b,{animationEasing:l,animationDuration:o,animationBegin:p,isAnimationActive:q,isUpdateAnimationActive:r}=b;if(f!==+f||g!==+g||h!==+h||i!==+i||0===h||0===i)return null;var s=(0,m.$)("recharts-rectangle",k);return r?n.createElement(oi,{canBegin:d>0,from:{width:h,height:i,x:f,y:g},to:{width:h,height:i,x:f,y:g},duration:o,animationEasing:l,isActive:r},a=>{var{width:e,height:f,x:g,y:h}=a;return n.createElement(oi,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:o,isActive:q,easing:l},n.createElement("path",oj({},l1(b,!0),{className:s,d:ok(g,h,e,f,j),ref:c})))}):n.createElement("path",oj({},l1(b,!0),{className:s,d:ok(f,g,h,i,j)}))};function on(){return(on=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var oo=(a,b,c,d,e)=>{var f=c-d;return"M ".concat(a,",").concat(b)+"L ".concat(a+c,",").concat(b)+"L ".concat(a+c-f/2,",").concat(b+e)+"L ".concat(a+c-f/2-d,",").concat(b+e)+"L ".concat(a,",").concat(b," Z")},op={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},oq=a=>{var b=mp(a,op),c=(0,n.useRef)(),[d,e]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&e(a)}catch(a){}},[]);var{x:f,y:g,upperWidth:h,lowerWidth:i,height:j,className:k}=b,{animationEasing:l,animationDuration:o,animationBegin:p,isUpdateAnimationActive:q}=b;if(f!==+f||g!==+g||h!==+h||i!==+i||j!==+j||0===h&&0===i||0===j)return null;var r=(0,m.$)("recharts-trapezoid",k);return q?n.createElement(oi,{canBegin:d>0,from:{upperWidth:0,lowerWidth:0,height:j,x:f,y:g},to:{upperWidth:h,lowerWidth:i,height:j,x:f,y:g},duration:o,animationEasing:l,isActive:q},a=>{var{upperWidth:e,lowerWidth:f,height:g,x:h,y:i}=a;return n.createElement(oi,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:o,easing:l},n.createElement("path",on({},l1(b,!0),{className:r,d:oo(h,i,e,f,g),ref:c})))}):n.createElement("g",null,n.createElement("path",on({},l1(b,!0),{className:r,d:oo(f,g,h,i,j)})))};function or(){return(or=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var os=a=>{var{cx:b,cy:c,radius:d,angle:e,sign:f,isExternal:g,cornerRadius:h,cornerIsExternal:i}=a,j=h*(g?1:-1)+d,k=Math.asin(h/j)/cv,l=i?e:e+f*k,m=cw(b,c,j,l);return{center:m,circleTangency:cw(b,c,d,l),lineTangency:cw(b,c,j*Math.cos(k*cv),i?e-f*k:e),theta:k}},ot=a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:f,endAngle:g}=a,h=((a,b)=>s(b-a)*Math.min(Math.abs(b-a),359.999))(f,g),i=f+h,j=cw(b,c,e,f),k=cw(b,c,e,i),l="M ".concat(j.x,",").concat(j.y,"\n    A ").concat(e,",").concat(e,",0,\n    ").concat(+(Math.abs(h)>180),",").concat(+(f>i),",\n    ").concat(k.x,",").concat(k.y,"\n  ");if(d>0){var m=cw(b,c,d,f),n=cw(b,c,d,i);l+="L ".concat(n.x,",").concat(n.y,"\n            A ").concat(d,",").concat(d,",0,\n            ").concat(+(Math.abs(h)>180),",").concat(+(f<=i),",\n            ").concat(m.x,",").concat(m.y," Z")}else l+="L ".concat(b,",").concat(c," Z");return l},ou={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},ov=a=>{var b,c=mp(a,ou),{cx:d,cy:e,innerRadius:f,outerRadius:g,cornerRadius:h,forceCornerRadius:i,cornerIsExternal:j,startAngle:k,endAngle:l,className:o}=c;if(g<f||k===l)return null;var p=(0,m.$)("recharts-sector",o),q=g-f,r=z(h,q,0,!0);return b=r>0&&360>Math.abs(k-l)?(a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,cornerRadius:f,forceCornerRadius:g,cornerIsExternal:h,startAngle:i,endAngle:j}=a,k=s(j-i),{circleTangency:l,lineTangency:m,theta:n}=os({cx:b,cy:c,radius:e,angle:i,sign:k,cornerRadius:f,cornerIsExternal:h}),{circleTangency:o,lineTangency:p,theta:q}=os({cx:b,cy:c,radius:e,angle:j,sign:-k,cornerRadius:f,cornerIsExternal:h}),r=h?Math.abs(i-j):Math.abs(i-j)-n-q;if(r<0)return g?"M ".concat(m.x,",").concat(m.y,"\n        a").concat(f,",").concat(f,",0,0,1,").concat(2*f,",0\n        a").concat(f,",").concat(f,",0,0,1,").concat(-(2*f),",0\n      "):ot({cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:i,endAngle:j});var t="M ".concat(m.x,",").concat(m.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(l.x,",").concat(l.y,"\n    A").concat(e,",").concat(e,",0,").concat(+(r>180),",").concat(+(k<0),",").concat(o.x,",").concat(o.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(p.x,",").concat(p.y,"\n  ");if(d>0){var{circleTangency:u,lineTangency:v,theta:w}=os({cx:b,cy:c,radius:d,angle:i,sign:k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),{circleTangency:x,lineTangency:y,theta:z}=os({cx:b,cy:c,radius:d,angle:j,sign:-k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),A=h?Math.abs(i-j):Math.abs(i-j)-w-z;if(A<0&&0===f)return"".concat(t,"L").concat(b,",").concat(c,"Z");t+="L".concat(y.x,",").concat(y.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(x.x,",").concat(x.y,"\n      A").concat(d,",").concat(d,",0,").concat(+(A>180),",").concat(+(k>0),",").concat(u.x,",").concat(u.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(v.x,",").concat(v.y,"Z")}else t+="L".concat(b,",").concat(c,"Z");return t})({cx:d,cy:e,innerRadius:f,outerRadius:g,cornerRadius:Math.min(r,q/2),forceCornerRadius:i,cornerIsExternal:j,startAngle:k,endAngle:l}):ot({cx:d,cy:e,innerRadius:f,outerRadius:g,startAngle:k,endAngle:l}),n.createElement("path",or({},l1(c,!0),{className:p,d:b}))};let ow=Math.cos,ox=Math.sin,oy=Math.sqrt,oz=Math.PI,oA=2*oz,oB={draw(a,b){let c=oy(b/oz);a.moveTo(c,0),a.arc(0,0,c,0,oA)}},oC=oy(1/3),oD=2*oC,oE=ox(oz/10)/ox(7*oz/10),oF=ox(oA/10)*oE,oG=-ow(oA/10)*oE,oH=oy(3),oI=oy(3)/2,oJ=1/oy(12),oK=(oJ/2+1)*3;oy(3),oy(3);var oL=["type","size","sizeType"];function oM(){return(oM=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function oN(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oO(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oN(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oN(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var oP={symbolCircle:oB,symbolCross:{draw(a,b){let c=oy(b/5)/2;a.moveTo(-3*c,-c),a.lineTo(-c,-c),a.lineTo(-c,-3*c),a.lineTo(c,-3*c),a.lineTo(c,-c),a.lineTo(3*c,-c),a.lineTo(3*c,c),a.lineTo(c,c),a.lineTo(c,3*c),a.lineTo(-c,3*c),a.lineTo(-c,c),a.lineTo(-3*c,c),a.closePath()}},symbolDiamond:{draw(a,b){let c=oy(b/oD),d=c*oC;a.moveTo(0,-c),a.lineTo(d,0),a.lineTo(0,c),a.lineTo(-d,0),a.closePath()}},symbolSquare:{draw(a,b){let c=oy(b),d=-c/2;a.rect(d,d,c,c)}},symbolStar:{draw(a,b){let c=oy(.8908130915292852*b),d=oF*c,e=oG*c;a.moveTo(0,-c),a.lineTo(d,e);for(let b=1;b<5;++b){let f=oA*b/5,g=ow(f),h=ox(f);a.lineTo(h*c,-g*c),a.lineTo(g*d-h*e,h*d+g*e)}a.closePath()}},symbolTriangle:{draw(a,b){let c=-oy(b/(3*oH));a.moveTo(0,2*c),a.lineTo(-oH*c,-c),a.lineTo(oH*c,-c),a.closePath()}},symbolWye:{draw(a,b){let c=oy(b/oK),d=c/2,e=c*oJ,f=c*oJ+c,g=-d;a.moveTo(d,e),a.lineTo(d,f),a.lineTo(g,f),a.lineTo(-.5*d-oI*e,oI*d+-.5*e),a.lineTo(-.5*d-oI*f,oI*d+-.5*f),a.lineTo(-.5*g-oI*f,oI*g+-.5*f),a.lineTo(-.5*d+oI*e,-.5*e-oI*d),a.lineTo(-.5*d+oI*f,-.5*f-oI*d),a.lineTo(-.5*g+oI*f,-.5*f-oI*g),a.closePath()}}},oQ=Math.PI/180,oR=a=>{var{type:b="circle",size:c=64,sizeType:d="area"}=a,e=oO(oO({},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,oL)),{},{type:b,size:c,sizeType:d}),{className:f,cx:g,cy:h}=e,i=l1(e,!0);return g===+g&&h===+h&&c===+c?n.createElement("path",oM({},i,{className:(0,m.$)("recharts-symbols",f),transform:"translate(".concat(g,", ").concat(h,")"),d:(()=>{var a=oP["symbol".concat(D(b))]||oB;return(function(a,b){let c=null,d=nh(e);function e(){let e;if(c||(c=e=d()),a.apply(this,arguments).draw(c,+b.apply(this,arguments)),e)return c=null,e+""||null}return a="function"==typeof a?a:cp(a||oB),b="function"==typeof b?b:cp(void 0===b?64:+b),e.type=function(b){return arguments.length?(a="function"==typeof b?b:cp(b),e):a},e.size=function(a){return arguments.length?(b="function"==typeof a?a:cp(+a),e):b},e.context=function(a){return arguments.length?(c=null==a?null:a,e):c},e})().type(a).size(((a,b,c)=>{if("area"===b)return a;switch(c){case"cross":return 5*a*a/9;case"diamond":return .5*a*a/Math.sqrt(3);case"square":return a*a;case"star":var d=18*oQ;return 1.25*a*a*(Math.tan(d)-Math.tan(2*d)*Math.tan(d)**2);case"triangle":return Math.sqrt(3)*a*a/4;case"wye":return(21-10*Math.sqrt(3))*a*a/8;default:return Math.PI*a*a/4}})(c,d,b))()})()})):null};oR.registerSymbol=(a,b)=>{oP["symbol".concat(D(a))]=b};var oS=["option","shapeType","propTransformer","activeClassName","isActive"];function oT(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oU(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oT(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oT(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function oV(a,b){return oU(oU({},b),a)}function oW(a){var{shapeType:b,elementProps:c}=a;switch(b){case"rectangle":return n.createElement(om,c);case"trapezoid":return n.createElement(oq,c);case"sector":return n.createElement(ov,c);case"symbols":if("symbols"===b)return n.createElement(oR,c);break;default:return null}}function oX(a){var b,{option:c,shapeType:d,propTransformer:e=oV,activeClassName:f="recharts-active-shape",isActive:g}=a,h=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,oS);if((0,n.isValidElement)(c))b=(0,n.cloneElement)(c,oU(oU({},h),(0,n.isValidElement)(c)?c.props:c));else if("function"==typeof c)b=c(h);else if(nW()(c)&&"boolean"!=typeof c){var i=e(c,h);b=n.createElement(oW,{shapeType:d,elementProps:i})}else b=n.createElement(oW,{shapeType:d,elementProps:h});return g?n.createElement(mV,{className:f},b):b}function oY(a){var{fn:b,args:c}=a;return cf(),cZ(),null}function oZ(a){var{legendPayload:b}=a;return cf(),cZ(),null}function o$(a){var{legendPayload:b}=a;return cf(),cj(c5),null}function o_(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",c=(0,n.useRef)(y(b)),d=(0,n.useRef)(a);return d.current!==a&&(c.current=y(b),d.current=a),c.current}var o0=["onMouseEnter","onClick","onMouseLeave"];function o1(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function o2(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?o1(Object(c),!0).forEach(function(b){o3(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):o1(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function o3(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function o4(){return(o4=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function o5(a){var b=(0,n.useMemo)(()=>l1(a,!1),[a]),c=(0,n.useMemo)(()=>l_(a.children,nU),[a.children]),d=(0,n.useMemo)(()=>({name:a.name,nameKey:a.nameKey,tooltipType:a.tooltipType,data:a.data,dataKey:a.dataKey,cx:a.cx,cy:a.cy,startAngle:a.startAngle,endAngle:a.endAngle,minAngle:a.minAngle,paddingAngle:a.paddingAngle,innerRadius:a.innerRadius,outerRadius:a.outerRadius,cornerRadius:a.cornerRadius,legendType:a.legendType,fill:a.fill,presentationProps:b}),[a.cornerRadius,a.cx,a.cy,a.data,a.dataKey,a.endAngle,a.innerRadius,a.minAngle,a.name,a.nameKey,a.outerRadius,a.paddingAngle,a.startAngle,a.tooltipType,a.legendType,a.fill,b]),e=cj(a=>mO(a,d,c));return n.createElement(o$,{legendPayload:e})}function o6(a){var{dataKey:b,nameKey:c,sectors:d,stroke:e,strokeWidth:f,fill:g,name:h,hide:i,tooltipType:j}=a;return{dataDefinedOnItem:null==d?void 0:d.map(a=>a.tooltipPayload),positions:null==d?void 0:d.map(a=>a.tooltipPosition),settings:{stroke:e,strokeWidth:f,fill:g,dataKey:b,nameKey:c,name:cK(h,b),hide:i,type:j,color:g,unit:""}}}function o7(a){var{sectors:b,props:c,showLabels:d}=a,{label:e,labelLine:f,dataKey:g}=c;if(!d||!e||!b)return null;var h=l1(c,!1),i=l1(e,!1),j=l1(f,!1),k="object"==typeof e&&"offsetRadius"in e&&e.offsetRadius||20,l=b.map((a,b)=>{var c,d,l=(a.startAngle+a.endAngle)/2,o=cw(a.cx,a.cy,a.outerRadius+k,l),p=o2(o2(o2(o2({},h),a),{},{stroke:"none"},i),{},{index:b,textAnchor:(c=o.x)>(d=a.cx)?"start":c<d?"end":"middle"},o),q=o2(o2(o2(o2({},h),a),{},{fill:"none",stroke:a.fill},j),{},{index:b,points:[cw(a.cx,a.cy,a.outerRadius,l),o],key:"line"});return n.createElement(mV,{key:"label-".concat(a.startAngle,"-").concat(a.endAngle,"-").concat(a.midAngle,"-").concat(b)},f&&((a,b)=>{if(n.isValidElement(a))return n.cloneElement(a,b);if("function"==typeof a)return a(b);var c=(0,m.$)("recharts-pie-label-line","boolean"!=typeof a?a.className:"");return n.createElement(nt,o4({},b,{type:"linear",className:c}))})(f,q),((a,b,c)=>{if(n.isValidElement(a))return n.cloneElement(a,b);var d=c;if("function"==typeof a&&(d=a(b),n.isValidElement(d)))return d;var e=(0,m.$)("recharts-pie-label-text","boolean"!=typeof a&&"function"!=typeof a?a.className:"");return n.createElement(nT,o4({},b,{alignmentBaseline:"middle",className:e}),d)})(e,p,cA(a,g)))});return n.createElement(mV,{className:"recharts-pie-labels"},l)}function o8(a){var b,c,d,e,f,{sectors:g,activeShape:h,inactiveShape:i,allOtherPieProps:j,showLabels:k}=a,l=cj(kl),{onMouseEnter:m,onClick:o,onMouseLeave:p}=j,q=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(j,o0),r=(b=j.dataKey,c=cf(),(a,d)=>e=>{null==m||m(a,d,e),c(bB({activeIndex:String(d),activeDataKey:b,activeCoordinate:a.tooltipPosition}))}),s=(d=cf(),(a,b)=>c=>{null==p||p(a,b,c),d(bC())}),t=(e=j.dataKey,f=cf(),(a,b)=>c=>{null==o||o(a,b,c),f(bE({activeIndex:String(b),activeDataKey:e,activeCoordinate:a.tooltipPosition}))});return null==g?null:n.createElement(n.Fragment,null,g.map((a,b)=>{if((null==a?void 0:a.startAngle)===0&&(null==a?void 0:a.endAngle)===0&&1!==g.length)return null;var c=h&&String(b)===l,d=c?h:l?i:null,e=o2(o2({},a),{},{stroke:a.stroke,tabIndex:-1,[cR]:b,[cS]:j.dataKey});return n.createElement(mV,o4({tabIndex:-1,className:"recharts-pie-sector"},lW(q,a,b),{onMouseEnter:r(a,b),onMouseLeave:s(a,b),onClick:t(a,b),key:"sector-".concat(null==a?void 0:a.startAngle,"-").concat(null==a?void 0:a.endAngle,"-").concat(a.midAngle,"-").concat(b)}),n.createElement(oX,o4({option:d,isActive:c,shapeType:"sector"},e)))}),n.createElement(o7,{sectors:g,props:j,showLabels:k}))}function o9(a){var{props:b,previousSectorsRef:c}=a,{sectors:d,isAnimationActive:e,animationBegin:f,animationDuration:g,animationEasing:h,activeShape:i,inactiveShape:j,onAnimationStart:k,onAnimationEnd:l}=b,m=o_(b,"recharts-pie-"),o=c.current,[p,q]=(0,n.useState)(!0),s=(0,n.useCallback)(()=>{"function"==typeof l&&l(),q(!1)},[l]),t=(0,n.useCallback)(()=>{"function"==typeof k&&k(),q(!0)},[k]);return n.createElement(oi,{begin:f,duration:g,isActive:e,easing:h,from:{t:0},to:{t:1},onAnimationStart:t,onAnimationEnd:s,key:m},a=>{var{t:e}=a,f=[],g=(d&&d[0]).startAngle;return d.forEach((a,b)=>{var c=o&&o[b],d=b>0?r()(a,"paddingAngle",0):0;if(c){var h=B(c.endAngle-c.startAngle,a.endAngle-a.startAngle),i=o2(o2({},a),{},{startAngle:g+d,endAngle:g+h(e)+d});f.push(i),g=i.endAngle}else{var{endAngle:j,startAngle:k}=a,l=B(0,j-k)(e),m=o2(o2({},a),{},{startAngle:g+d,endAngle:g+l+d});f.push(m),g=m.endAngle}}),c.current=f,n.createElement(mV,null,n.createElement(o8,{sectors:f,activeShape:i,inactiveShape:j,allOtherPieProps:b,showLabels:!p}))})}function pa(a){var{sectors:b,isAnimationActive:c,activeShape:d,inactiveShape:e}=a,f=(0,n.useRef)(null),g=f.current;return c&&b&&b.length&&(!g||g!==b)?n.createElement(o9,{props:a,previousSectorsRef:f}):n.createElement(o8,{sectors:b,activeShape:d,inactiveShape:e,allOtherPieProps:a,showLabels:!0})}function pb(a){var{hide:b,className:c,rootTabIndex:d}=a,e=(0,m.$)("recharts-pie",c);return b?null:n.createElement(mV,{tabIndex:d,className:e},n.createElement(pa,a))}var pc={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!nu.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function pd(a){var b=mp(a,pc),c=(0,n.useMemo)(()=>l_(a.children,nU),[a.children]),d=l1(b,!1),e=(0,n.useMemo)(()=>({name:b.name,nameKey:b.nameKey,tooltipType:b.tooltipType,data:b.data,dataKey:b.dataKey,cx:b.cx,cy:b.cy,startAngle:b.startAngle,endAngle:b.endAngle,minAngle:b.minAngle,paddingAngle:b.paddingAngle,innerRadius:b.innerRadius,outerRadius:b.outerRadius,cornerRadius:b.cornerRadius,legendType:b.legendType,fill:b.fill,presentationProps:d}),[b.cornerRadius,b.cx,b.cy,b.data,b.dataKey,b.endAngle,b.innerRadius,b.minAngle,b.name,b.nameKey,b.outerRadius,b.paddingAngle,b.startAngle,b.tooltipType,b.legendType,b.fill,d]),f=cj(a=>mQ(a,e,c));return n.createElement(n.Fragment,null,n.createElement(oY,{fn:o6,args:o2(o2({},b),{},{sectors:f})}),n.createElement(pb,o4({},b,{sectors:f})))}class pe extends n.PureComponent{constructor(){super(...arguments),o3(this,"id",y("recharts-pie-"))}render(){return n.createElement(n.Fragment,null,n.createElement(mS,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),n.createElement(o5,this.props),n.createElement(pd,this.props),this.props.children)}}o3(pe,"displayName","Pie"),o3(pe,"defaultProps",pc);var pf=c(51215);function pg(){return(pg=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function ph(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pi(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ph(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ph(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pj(a){return Array.isArray(a)&&w(a[0])&&w(a[1])?a.join(" ~ "):a}var pk=a=>{var{separator:b=" : ",contentStyle:c={},itemStyle:d={},labelStyle:e={},payload:f,formatter:g,itemSorter:h,wrapperClassName:i,labelClassName:j,label:k,labelFormatter:l,accessibilityLayer:o=!1}=a,p=pi({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},c),q=pi({margin:0},e),r=null!=k,s=r?k:"",t=(0,m.$)("recharts-default-tooltip",i),u=(0,m.$)("recharts-tooltip-label",j);return r&&l&&null!=f&&(s=l(k,f)),n.createElement("div",pg({className:t,style:p},o?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:u,style:q},n.isValidElement(s)?s:"".concat(s)),(()=>{if(f&&f.length){var a=(h?cl()(f,h):f).map((a,c)=>{if("none"===a.type)return null;var e=a.formatter||g||pj,{value:h,name:i}=a,j=h,k=i;if(e){var l=e(h,i,a,c,f);if(Array.isArray(l))[j,k]=l;else{if(null==l)return null;j=l}}var m=pi({display:"block",paddingTop:4,paddingBottom:4,color:a.color||"#000"},d);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(c),style:m},w(k)?n.createElement("span",{className:"recharts-tooltip-item-name"},k):null,w(k)?n.createElement("span",{className:"recharts-tooltip-item-separator"},b):null,n.createElement("span",{className:"recharts-tooltip-item-value"},j),n.createElement("span",{className:"recharts-tooltip-item-unit"},a.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},a)}return null})())},pl="recharts-tooltip-wrapper",pm={visibility:"hidden"};function pn(a){var{allowEscapeViewBox:b,coordinate:c,key:d,offsetTopLeft:e,position:f,reverseDirection:g,tooltipDimension:h,viewBox:i,viewBoxDimension:j}=a;if(f&&v(f[d]))return f[d];var k=c[d]-h-(e>0?e:0),l=c[d]+e;if(b[d])return g[d]?k:l;var m=i[d];return null==m?0:g[d]?k<m?Math.max(l,m):Math.max(k,m):null==j?0:l+h>m+j?Math.max(k,m):Math.max(l,m)}function po(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pp(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?po(Object(c),!0).forEach(function(b){pq(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):po(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pq(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class pr extends n.PureComponent{constructor(){super(...arguments),pq(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),pq(this,"handleKeyDown",a=>{if("Escape"===a.key){var b,c,d,e;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(b=null==(c=this.props.coordinate)?void 0:c.x)?b:0,y:null!=(d=null==(e=this.props.coordinate)?void 0:e.y)?d:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var a,b;this.state.dismissed&&((null==(a=this.props.coordinate)?void 0:a.x)!==this.state.dismissedAtCoordinate.x||(null==(b=this.props.coordinate)?void 0:b.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:a,allowEscapeViewBox:b,animationDuration:c,animationEasing:d,children:e,coordinate:f,hasPayload:g,isAnimationActive:h,offset:i,position:j,reverseDirection:k,useTranslate3d:l,viewBox:o,wrapperStyle:p,lastBoundingBox:q,innerRef:r,hasPortalFromProps:s}=this.props,{cssClasses:t,cssProperties:u}=function(a){var b,c,d,{allowEscapeViewBox:e,coordinate:f,offsetTopLeft:g,position:h,reverseDirection:i,tooltipBox:j,useTranslate3d:k,viewBox:l}=a;return{cssProperties:b=j.height>0&&j.width>0&&f?function(a){var{translateX:b,translateY:c,useTranslate3d:d}=a;return{transform:d?"translate3d(".concat(b,"px, ").concat(c,"px, 0)"):"translate(".concat(b,"px, ").concat(c,"px)")}}({translateX:c=pn({allowEscapeViewBox:e,coordinate:f,key:"x",offsetTopLeft:g,position:h,reverseDirection:i,tooltipDimension:j.width,viewBox:l,viewBoxDimension:l.width}),translateY:d=pn({allowEscapeViewBox:e,coordinate:f,key:"y",offsetTopLeft:g,position:h,reverseDirection:i,tooltipDimension:j.height,viewBox:l,viewBoxDimension:l.height}),useTranslate3d:k}):pm,cssClasses:function(a){var{coordinate:b,translateX:c,translateY:d}=a;return(0,m.$)(pl,{["".concat(pl,"-right")]:v(c)&&b&&v(b.x)&&c>=b.x,["".concat(pl,"-left")]:v(c)&&b&&v(b.x)&&c<b.x,["".concat(pl,"-bottom")]:v(d)&&b&&v(b.y)&&d>=b.y,["".concat(pl,"-top")]:v(d)&&b&&v(b.y)&&d<b.y})}({translateX:c,translateY:d,coordinate:f})}}({allowEscapeViewBox:b,coordinate:f,offsetTopLeft:i,position:j,reverseDirection:k,tooltipBox:{height:q.height,width:q.width},useTranslate3d:l,viewBox:o}),w=s?{}:pp(pp({transition:h&&a?"transform ".concat(c,"ms ").concat(d):void 0},u),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&g?"visible":"hidden",position:"absolute",top:0,left:0}),x=pp(pp({},w),{},{visibility:!this.state.dismissed&&a&&g?"visible":"hidden"},p);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:t,style:x,ref:r},e)}}var ps=c(23854),pt=c.n(ps),pu=["x","y","top","left","width","height","className"];function pv(){return(pv=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function pw(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var px=a=>{var{x:b=0,y:c=0,top:d=0,left:e=0,width:f=0,height:g=0,className:h}=a,i=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pw(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pw(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({x:b,y:c,top:d,left:e,width:f,height:g},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,pu));return v(b)&&v(c)&&v(f)&&v(g)&&v(d)&&v(e)?n.createElement("path",pv({},l1(i,!0),{className:(0,m.$)("recharts-cross",h),d:"M".concat(b,",").concat(d,"v").concat(g,"M").concat(e,",").concat(c,"h").concat(f)})):null};function py(a){var{cx:b,cy:c,radius:d,startAngle:e,endAngle:f}=a;return{points:[cw(b,c,d,e),cw(b,c,d,f)],cx:b,cy:c,radius:d,startAngle:e,endAngle:f}}function pz(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pA(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pz(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pz(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pB(){return(pB=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function pC(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pD(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pC(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pC(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pE(a){var b,c,d,{coordinate:e,payload:f,index:g,offset:h,tooltipAxisBandSize:i,layout:j,cursor:k,tooltipEventType:l,chartName:o}=a;if(!k||!e||"ScatterChart"!==o&&"axis"!==l)return null;if("ScatterChart"===o)c=e,d=px;else if("BarChart"===o)b=i/2,c={stroke:"none",fill:"#ccc",x:"horizontal"===j?e.x-b:h.left+.5,y:"horizontal"===j?h.top+.5:e.y-b,width:"horizontal"===j?i:h.width-1,height:"horizontal"===j?h.height-1:i},d=om;else if("radial"===j){var{cx:p,cy:q,radius:r,startAngle:s,endAngle:t}=py(e);c={cx:p,cy:q,startAngle:s,endAngle:t,innerRadius:r,outerRadius:r},d=ov}else c={points:function(a,b,c){var d,e,f,g;if("horizontal"===a)f=d=b.x,e=c.top,g=c.top+c.height;else if("vertical"===a)g=e=b.y,d=c.left,f=c.left+c.width;else if(null!=b.cx&&null!=b.cy)if("centric"!==a)return py(b);else{var{cx:h,cy:i,innerRadius:j,outerRadius:k,angle:l}=b,m=cw(h,i,j,l),n=cw(h,i,k,l);d=m.x,e=m.y,f=n.x,g=n.y}return[{x:d,y:e},{x:f,y:g}]}(j,e,h)},d=nt;var u="object"==typeof k&&"className"in k?k.className:void 0,v=pD(pD(pD(pD({stroke:"#ccc",pointerEvents:"none"},h),c),l1(k,!1)),{},{payload:f,payloadIndex:g,className:(0,m.$)("recharts-tooltip-cursor",u)});return(0,n.isValidElement)(k)?(0,n.cloneElement)(k,v):(0,n.createElement)(d,v)}function pF(a){var b,c,d,e=(b=cj(jP),c=cj(kg),d=cj(kd),cI(pA(pA({},b),{},{scale:d}),c)),f=c2(),g=c6(),h=cj(hJ);return n.createElement(pE,pB({},a,{coordinate:a.coordinate,index:a.index,payload:a.payload,offset:f,layout:g,tooltipAxisBandSize:e,chartName:h}))}function pG(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pH(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pG(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pG(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pI(a){return a.dataKey}var pJ=[],pK={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!nu.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function pL(a){var b,c,d,e=mp(a,pK),{active:f,allowEscapeViewBox:g,animationDuration:h,animationEasing:i,content:j,filterNull:k,isAnimationActive:l,offset:m,payloadUniqBy:o,position:p,reverseDirection:q,useTranslate3d:r,wrapperStyle:s,cursor:t,shared:u,trigger:v,defaultIndex:w,portal:x,axisId:y}=e;cf();var z="number"==typeof w?String(w):w,A=c0(),B=l2(),C=cj(a=>jA(a,u)),{activeIndex:D,isActive:E}=cj(a=>kF(a,C,v,z)),F=cj(a=>kE(a,C,v,z)),G=cj(a=>kD(a,C,v,z)),H=cj(a=>kC(a,C,v,z)),I=(0,n.useContext)(md),J=null!=f?f:E,[K,L]=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[b,c]=(0,n.useState)({height:0,left:0,top:0,width:0}),d=(0,n.useCallback)(a=>{if(null!=a){var d=a.getBoundingClientRect(),e={height:d.height,left:d.left,top:d.top,width:d.width};(Math.abs(e.height-b.height)>1||Math.abs(e.left-b.left)>1||Math.abs(e.top-b.top)>1||Math.abs(e.width-b.width)>1)&&c({height:e.height,left:e.left,top:e.top,width:e.width})}},[b.width,b.height,b.top,b.left,...a]);return[b,d]}([F,J]),M="axis"===C?G:void 0;cj(a=>((a,b,c)=>{if(null!=b){var d=jJ(a);return"axis"===b?"hover"===c?d.axisInteraction.hover.dataKey:d.axisInteraction.click.dataKey:"hover"===c?d.itemInteraction.hover.dataKey:d.itemInteraction.click.dataKey}})(a,C,v)),cj(hM),cj(hK),cj(hL),null==(b=cj(mc))||b.active;var N=null!=x?x:I;if(null==N)return null;var O=null!=F?F:pJ;J||(O=pJ),k&&O.length&&(c=F.filter(a=>null!=a.value&&(!0!==a.hide||e.includeHidden)),O=!0===o?pt()(c,pI):"function"==typeof o?pt()(c,o):c);var P=O.length>0,Q=n.createElement(pr,{allowEscapeViewBox:g,animationDuration:h,animationEasing:i,isAnimationActive:l,active:J,coordinate:H,hasPayload:P,offset:m,position:p,reverseDirection:q,useTranslate3d:r,viewBox:A,wrapperStyle:s,lastBoundingBox:K,innerRef:L,hasPortalFromProps:!!x},(d=pH(pH({},e),{},{payload:O,label:M,active:J,coordinate:H,accessibilityLayer:B}),n.isValidElement(j)?n.cloneElement(j,d):"function"==typeof j?n.createElement(j,d):n.createElement(pk,d)));return n.createElement(n.Fragment,null,(0,pf.createPortal)(Q,N),J&&n.createElement(pF,{cursor:t,tooltipEventType:C,coordinate:H,payload:F,index:D}))}var pM=["width","height"];function pN(){return(pN=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var pO={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},pP=(0,n.forwardRef)(function(a,b){var c,d=mp(a.categoricalChartProps,pO),{width:e,height:f}=d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(d,pM);if(!gR(e)||!gR(f))return null;var{chartName:h,defaultTooltipEventType:i,validateTooltipEventTypes:j,tooltipPayloadSearcher:k,categoricalChartProps:l}=a;return n.createElement(lL,{preloadedState:{options:{chartName:h,defaultTooltipEventType:i,validateTooltipEventTypes:j,tooltipPayloadSearcher:k,eventEmitter:void 0}},reduxStoreName:null!=(c=l.id)?c:h},n.createElement(lM,{chartData:l.data}),n.createElement(lN,{width:e,height:f,layout:d.layout,margin:d.margin}),n.createElement(lO,{accessibilityLayer:d.accessibilityLayer,barCategoryGap:d.barCategoryGap,maxBarSize:d.maxBarSize,stackOffset:d.stackOffset,barGap:d.barGap,barSize:d.barSize,syncId:d.syncId,syncMethod:d.syncMethod,className:d.className}),n.createElement(mn,pN({},g,{width:e,height:f,ref:b})))}),pQ=["axis"],pR=(0,n.forwardRef)((a,b)=>n.createElement(pP,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:pQ,tooltipPayloadSearcher:bk,categoricalChartProps:a,ref:b}));class pS{static create(a){return new pS(a)}constructor(a){this.scale=a}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(a){var{bandAware:b,position:c}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==a){if(c)switch(c){case"start":default:return this.scale(a);case"middle":var d=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+d;case"end":var e=this.bandwidth?this.bandwidth():0;return this.scale(a)+e}if(b){var f=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+f}return this.scale(a)}}isInRange(a){var b=this.range(),c=b[0],d=b[b.length-1];return c<=d?a>=c&&a<=d:a>=d&&a<=c}}!function(a,b,c){var d;(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):a[b]=1e-4}(pS,"EPS",1e-4);var pT=function(a){var{width:b,height:c}=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=(d%180+180)%180*Math.PI/180,f=Math.atan(c/b);return Math.abs(e>f&&e<Math.PI-f?c/Math.sin(e):b/Math.cos(e))};function pU(a,b,c){if(b<1)return[];if(1===b&&void 0===c)return a;for(var d=[],e=0;e<a.length;e+=b)if(void 0!==c&&!0!==c(a[e]))return;else d.push(a[e]);return d}function pV(a,b,c,d,e){if(a*b<a*d||a*b>a*e)return!1;var f=c();return a*(b-a*f/2-d)>=0&&a*(b+a*f/2-e)<=0}function pW(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pX(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pW(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pW(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pY(a,b,c){var d,{tick:e,ticks:f,viewBox:g,minTickGap:h,orientation:i,interval:j,tickFormatter:k,unit:l,angle:m}=a;if(!f||!f.length||!e)return[];if(v(j)||nu.isSsr)return null!=(d=pU(f,(v(j)?j:0)+1))?d:[];var n="top"===i||"bottom"===i?"width":"height",o=l&&"width"===n?nA(l,{fontSize:b,letterSpacing:c}):{width:0,height:0},p=(a,d)=>{var e,f="function"==typeof k?k(a.value,d):a.value;return"width"===n?(e=nA(f,{fontSize:b,letterSpacing:c}),pT({width:e.width+o.width,height:e.height+o.height},m)):nA(f,{fontSize:b,letterSpacing:c})[n]},q=f.length>=2?s(f[1].coordinate-f[0].coordinate):1,r=function(a,b,c){var d="width"===c,{x:e,y:f,width:g,height:h}=a;return 1===b?{start:d?e:f,end:d?e+g:f+h}:{start:d?e+g:f+h,end:d?e:f}}(g,q,n);return"equidistantPreserveStart"===j?function(a,b,c,d,e){for(var f,g=(d||[]).slice(),{start:h,end:i}=b,j=0,k=1,l=h;k<=g.length;)if(f=function(){var b,f=null==d?void 0:d[j];if(void 0===f)return{v:pU(d,k)};var g=j,m=()=>(void 0===b&&(b=c(f,g)),b),n=f.coordinate,o=0===j||pV(a,n,m,l,i);o||(j=0,l=h,k+=1),o&&(l=n+a*(m()/2+e),j+=k)}())return f.v;return[]}(q,r,p,f,h):("preserveStart"===j||"preserveStartEnd"===j?function(a,b,c,d,e,f){var g=(d||[]).slice(),h=g.length,{start:i,end:j}=b;if(f){var k=d[h-1],l=c(k,h-1),m=a*(k.coordinate+a*l/2-j);g[h-1]=k=pX(pX({},k),{},{tickCoord:m>0?k.coordinate-m*a:k.coordinate}),pV(a,k.tickCoord,()=>l,i,j)&&(j=k.tickCoord-a*(l/2+e),g[h-1]=pX(pX({},k),{},{isShow:!0}))}for(var n=f?h-1:h,o=function(b){var d,f=g[b],h=()=>(void 0===d&&(d=c(f,b)),d);if(0===b){var k=a*(f.coordinate-a*h()/2-i);g[b]=f=pX(pX({},f),{},{tickCoord:k<0?f.coordinate-k*a:f.coordinate})}else g[b]=f=pX(pX({},f),{},{tickCoord:f.coordinate});pV(a,f.tickCoord,h,i,j)&&(i=f.tickCoord+a*(h()/2+e),g[b]=pX(pX({},f),{},{isShow:!0}))},p=0;p<n;p++)o(p);return g}(q,r,p,f,h,"preserveStartEnd"===j):function(a,b,c,d,e){for(var f=(d||[]).slice(),g=f.length,{start:h}=b,{end:i}=b,j=function(b){var d,j=f[b],k=()=>(void 0===d&&(d=c(j,b)),d);if(b===g-1){var l=a*(j.coordinate+a*k()/2-i);f[b]=j=pX(pX({},j),{},{tickCoord:l>0?j.coordinate-l*a:j.coordinate})}else f[b]=j=pX(pX({},j),{},{tickCoord:j.coordinate});pV(a,j.tickCoord,k,h,i)&&(i=j.tickCoord-a*(k()/2+e),f[b]=pX(pX({},j),{},{isShow:!0}))},k=g-1;k>=0;k--)j(k);return f}(q,r,p,f,h)).filter(a=>a.isShow)}function pZ(a,b){for(var c in a)if(({}).hasOwnProperty.call(a,c)&&(!({}).hasOwnProperty.call(b,c)||a[c]!==b[c]))return!1;for(var d in b)if(({}).hasOwnProperty.call(b,d)&&!({}).hasOwnProperty.call(a,d))return!1;return!0}var p$=["offset"],p_=["labelRef"];function p0(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function p1(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function p2(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?p1(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):p1(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function p3(){return(p3=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var p4=a=>null!=a&&"function"==typeof a;function p5(a){var b,{offset:c=5}=a,d=p2({offset:c},p0(a,p$)),{viewBox:e,position:f,value:g,children:h,content:i,className:j="",textBreakAll:k,labelRef:l}=d,o=c0(),p=e||o;if(!p||null==g&&null==h&&!(0,n.isValidElement)(i)&&"function"!=typeof i)return null;if((0,n.isValidElement)(i)){var{labelRef:q}=d,r=p0(d,p_);return(0,n.cloneElement)(i,r)}if("function"==typeof i){if(b=(0,n.createElement)(i,d),(0,n.isValidElement)(b))return b}else b=(a=>{var{value:b,formatter:c}=a,d=null==a.children?b:a.children;return"function"==typeof c?c(d):d})(d);var t="cx"in p&&v(p.cx),w=l1(d,!0);if(t&&("insideStart"===f||"insideEnd"===f||"end"===f))return((a,b,c)=>{let d,e;var f,g,{position:h,viewBox:i,offset:j,className:k}=a,{cx:l,cy:o,innerRadius:p,outerRadius:q,startAngle:r,endAngle:t,clockWise:u}=i,v=(p+q)/2,w=(d=r,s((e=t)-d)*Math.min(Math.abs(e-d),360)),x=w>=0?1:-1;"insideStart"===h?(f=r+x*j,g=u):"insideEnd"===h?(f=t-x*j,g=!u):"end"===h&&(f=t+x*j,g=u),g=w<=0?g:!g;var z=cw(l,o,v,f),A=cw(l,o,v,f+(g?1:-1)*359),B="M".concat(z.x,",").concat(z.y,"\n    A").concat(v,",").concat(v,",0,1,").concat(+!g,",\n    ").concat(A.x,",").concat(A.y),C=null==a.id?y("recharts-radial-line-"):a.id;return n.createElement("text",p3({},c,{dominantBaseline:"central",className:(0,m.$)("recharts-radial-bar-label",k)}),n.createElement("defs",null,n.createElement("path",{id:C,d:B})),n.createElement("textPath",{xlinkHref:"#".concat(C)},b))})(d,b,w);var x=t?(a=>{var{viewBox:b,offset:c,position:d}=a,{cx:e,cy:f,innerRadius:g,outerRadius:h,startAngle:i,endAngle:j}=b,k=(i+j)/2;if("outside"===d){var{x:l,y:m}=cw(e,f,h+c,k);return{x:l,y:m,textAnchor:l>=e?"start":"end",verticalAnchor:"middle"}}if("center"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"end"};var{x:n,y:o}=cw(e,f,(g+h)/2,k);return{x:n,y:o,textAnchor:"middle",verticalAnchor:"middle"}})(d):((a,b)=>{var{parentViewBox:c,offset:d,position:e}=a,{x:f,y:g,width:h,height:i}=b,j=i>=0?1:-1,k=j*d,l=j>0?"end":"start",m=j>0?"start":"end",n=h>=0?1:-1,o=n*d,p=n>0?"end":"start",q=n>0?"start":"end";if("top"===e)return p2(p2({},{x:f+h/2,y:g-j*d,textAnchor:"middle",verticalAnchor:l}),c?{height:Math.max(g-c.y,0),width:h}:{});if("bottom"===e)return p2(p2({},{x:f+h/2,y:g+i+k,textAnchor:"middle",verticalAnchor:m}),c?{height:Math.max(c.y+c.height-(g+i),0),width:h}:{});if("left"===e){var r={x:f-o,y:g+i/2,textAnchor:p,verticalAnchor:"middle"};return p2(p2({},r),c?{width:Math.max(r.x-c.x,0),height:i}:{})}if("right"===e){var s={x:f+h+o,y:g+i/2,textAnchor:q,verticalAnchor:"middle"};return p2(p2({},s),c?{width:Math.max(c.x+c.width-s.x,0),height:i}:{})}var t=c?{width:h,height:i}:{};return"insideLeft"===e?p2({x:f+o,y:g+i/2,textAnchor:q,verticalAnchor:"middle"},t):"insideRight"===e?p2({x:f+h-o,y:g+i/2,textAnchor:p,verticalAnchor:"middle"},t):"insideTop"===e?p2({x:f+h/2,y:g+k,textAnchor:"middle",verticalAnchor:m},t):"insideBottom"===e?p2({x:f+h/2,y:g+i-k,textAnchor:"middle",verticalAnchor:l},t):"insideTopLeft"===e?p2({x:f+o,y:g+k,textAnchor:q,verticalAnchor:m},t):"insideTopRight"===e?p2({x:f+h-o,y:g+k,textAnchor:p,verticalAnchor:m},t):"insideBottomLeft"===e?p2({x:f+o,y:g+i-k,textAnchor:q,verticalAnchor:l},t):"insideBottomRight"===e?p2({x:f+h-o,y:g+i-k,textAnchor:p,verticalAnchor:l},t):e&&"object"==typeof e&&(v(e.x)||u(e.x))&&(v(e.y)||u(e.y))?p2({x:f+z(e.x,h),y:g+z(e.y,i),textAnchor:"end",verticalAnchor:"end"},t):p2({x:f+h/2,y:g+i/2,textAnchor:"middle",verticalAnchor:"middle"},t)})(d,p);return n.createElement(nT,p3({ref:l,className:(0,m.$)("recharts-label",j)},w,x,{breakAll:k}),b)}p5.displayName="Label";var p6=a=>{var{cx:b,cy:c,angle:d,startAngle:e,endAngle:f,r:g,radius:h,innerRadius:i,outerRadius:j,x:k,y:l,top:m,left:n,width:o,height:p,clockWise:q,labelViewBox:r}=a;if(r)return r;if(v(o)&&v(p)){if(v(k)&&v(l))return{x:k,y:l,width:o,height:p};if(v(m)&&v(n))return{x:m,y:n,width:o,height:p}}return v(k)&&v(l)?{x:k,y:l,width:0,height:0}:v(b)&&v(c)?{cx:b,cy:c,startAngle:e||d||0,endAngle:f||d||0,innerRadius:i||0,outerRadius:j||h||g||0,clockWise:q}:a.viewBox?a.viewBox:void 0};p5.parseViewBox=p6,p5.renderCallByParent=function(a,b){var c=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&c&&!a.label)return null;var{children:d,labelRef:e}=a,f=p6(a),g=l_(d,p5).map((a,c)=>(0,n.cloneElement)(a,{viewBox:b||f,key:"label-".concat(c)}));return c?[((a,b,c)=>{if(!a)return null;var d={viewBox:b,labelRef:c};return!0===a?n.createElement(p5,p3({key:"label-implicit"},d)):w(a)?n.createElement(p5,p3({key:"label-implicit",value:a},d)):(0,n.isValidElement)(a)?a.type===p5?(0,n.cloneElement)(a,p2({key:"label-implicit"},d)):n.createElement(p5,p3({key:"label-implicit",content:a},d)):p4(a)?n.createElement(p5,p3({key:"label-implicit",content:a},d)):a&&"object"==typeof a?n.createElement(p5,p3({},a,{key:"label-implicit"},d)):null})(a.label,b||f,e),...g]:g};var p7=["viewBox"],p8=["viewBox"];function p9(){return(p9=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qa(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function qb(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?qa(Object(c),!0).forEach(function(b){qd(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):qa(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function qc(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function qd(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class qe extends n.Component{constructor(a){super(a),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(a,b){var{viewBox:c}=a,d=qc(a,p7),e=this.props,{viewBox:f}=e,g=qc(e,p8);return!pZ(c,f)||!pZ(d,g)||!pZ(b,this.state)}getTickLineCoord(a){var b,c,d,e,f,g,{x:h,y:i,width:j,height:k,orientation:l,tickSize:m,mirror:n,tickMargin:o}=this.props,p=n?-1:1,q=a.tickSize||m,r=v(a.tickCoord)?a.tickCoord:a.coordinate;switch(l){case"top":b=c=a.coordinate,g=(d=(e=i+!n*k)-p*q)-p*o,f=r;break;case"left":d=e=a.coordinate,f=(b=(c=h+!n*j)-p*q)-p*o,g=r;break;case"right":d=e=a.coordinate,f=(b=(c=h+n*j)+p*q)+p*o,g=r;break;default:b=c=a.coordinate,g=(d=(e=i+n*k)+p*q)+p*o,f=r}return{line:{x1:b,y1:d,x2:c,y2:e},tick:{x:f,y:g}}}getTickTextAnchor(){var a,{orientation:b,mirror:c}=this.props;switch(b){case"left":a=c?"start":"end";break;case"right":a=c?"end":"start";break;default:a="middle"}return a}getTickVerticalAnchor(){var{orientation:a,mirror:b}=this.props;switch(a){case"left":case"right":return"middle";case"top":return b?"start":"end";default:return b?"end":"start"}}renderAxisLine(){var{x:a,y:b,width:c,height:d,orientation:e,mirror:f,axisLine:g}=this.props,h=qb(qb(qb({},l1(this.props,!1)),l1(g,!1)),{},{fill:"none"});if("top"===e||"bottom"===e){var i=+("top"===e&&!f||"bottom"===e&&f);h=qb(qb({},h),{},{x1:a,y1:b+i*d,x2:a+c,y2:b+i*d})}else{var j=+("left"===e&&!f||"right"===e&&f);h=qb(qb({},h),{},{x1:a+j*c,y1:b,x2:a+j*c,y2:b+d})}return n.createElement("line",p9({},h,{className:(0,m.$)("recharts-cartesian-axis-line",r()(g,"className"))}))}static renderTickItem(a,b,c){var d,e=(0,m.$)(b.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(a))d=n.cloneElement(a,qb(qb({},b),{},{className:e}));else if("function"==typeof a)d=a(qb(qb({},b),{},{className:e}));else{var f="recharts-cartesian-axis-tick-value";"boolean"!=typeof a&&(f=(0,m.$)(f,a.className)),d=n.createElement(nT,p9({},b,{className:f}),c)}return d}renderTicks(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:d,stroke:e,tick:f,tickFormatter:g,unit:h}=this.props,i=pY(qb(qb({},this.props),{},{ticks:c}),a,b),j=this.getTickTextAnchor(),k=this.getTickVerticalAnchor(),l=l1(this.props,!1),o=l1(f,!1),p=qb(qb({},l),{},{fill:"none"},l1(d,!1)),q=i.map((a,b)=>{var{line:c,tick:q}=this.getTickLineCoord(a),s=qb(qb(qb(qb({textAnchor:j,verticalAnchor:k},l),{},{stroke:"none",fill:e},o),q),{},{index:b,payload:a,visibleTicksCount:i.length,tickFormatter:g});return n.createElement(mV,p9({className:"recharts-cartesian-axis-tick",key:"tick-".concat(a.value,"-").concat(a.coordinate,"-").concat(a.tickCoord)},lW(this.props,a,b)),d&&n.createElement("line",p9({},p,c,{className:(0,m.$)("recharts-cartesian-axis-tick-line",r()(d,"className"))})),f&&qe.renderTickItem(f,s,"".concat("function"==typeof g?g(a.value,b):a.value).concat(h||"")))});return q.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},q):null}render(){var{axisLine:a,width:b,height:c,className:d,hide:e}=this.props;if(e)return null;var{ticks:f}=this.props;return null!=b&&b<=0||null!=c&&c<=0?null:n.createElement(mV,{className:(0,m.$)("recharts-cartesian-axis",d),ref:a=>{if(a){var b=a.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(b);var c=b[0];if(c){var d=window.getComputedStyle(c).fontSize,e=window.getComputedStyle(c).letterSpacing;(d!==this.state.fontSize||e!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(c).fontSize,letterSpacing:window.getComputedStyle(c).letterSpacing})}}}},a&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,f),p5.renderCallByParent(this.props))}}qd(qe,"displayName","CartesianAxis"),qd(qe,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var qf=["x1","y1","x2","y2","key"],qg=["offset"],qh=["xAxisId","yAxisId"],qi=["xAxisId","yAxisId"];function qj(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function qk(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?qj(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):qj(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function ql(){return(ql=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qm(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var qn=a=>{var{fill:b}=a;if(!b||"none"===b)return null;var{fillOpacity:c,x:d,y:e,width:f,height:g,ry:h}=a;return n.createElement("rect",{x:d,y:e,ry:h,width:f,height:g,stroke:"none",fill:b,fillOpacity:c,className:"recharts-cartesian-grid-bg"})};function qo(a,b){var c;if(n.isValidElement(a))c=n.cloneElement(a,b);else if("function"==typeof a)c=a(b);else{var{x1:d,y1:e,x2:f,y2:g,key:h}=b,i=l1(qm(b,qf),!1),{offset:j}=i,k=qm(i,qg);c=n.createElement("line",ql({},k,{x1:d,y1:e,x2:f,y2:g,fill:"none",key:h}))}return c}function qp(a){var{x:b,width:c,horizontal:d=!0,horizontalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:f,yAxisId:g}=a,h=qm(a,qh),i=e.map((a,e)=>qo(d,qk(qk({},h),{},{x1:b,y1:a,x2:b+c,y2:a,key:"line-".concat(e),index:e})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},i)}function qq(a){var{y:b,height:c,vertical:d=!0,verticalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:f,yAxisId:g}=a,h=qm(a,qi),i=e.map((a,e)=>qo(d,qk(qk({},h),{},{x1:a,y1:b,x2:a,y2:b+c,key:"line-".concat(e),index:e})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},i)}function qr(a){var{horizontalFill:b,fillOpacity:c,x:d,y:e,width:f,height:g,horizontalPoints:h,horizontal:i=!0}=a;if(!i||!b||!b.length)return null;var j=h.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==j[0]&&j.unshift(0);var k=j.map((a,h)=>{var i=j[h+1]?j[h+1]-a:e+g-a;if(i<=0)return null;var k=h%b.length;return n.createElement("rect",{key:"react-".concat(h),y:a,x:d,height:i,width:f,stroke:"none",fill:b[k],fillOpacity:c,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},k)}function qs(a){var{vertical:b=!0,verticalFill:c,fillOpacity:d,x:e,y:f,width:g,height:h,verticalPoints:i}=a;if(!b||!c||!c.length)return null;var j=i.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==j[0]&&j.unshift(0);var k=j.map((a,b)=>{var i=j[b+1]?j[b+1]-a:e+g-a;if(i<=0)return null;var k=b%c.length;return n.createElement("rect",{key:"react-".concat(b),x:a,y:f,width:i,height:h,stroke:"none",fill:c[k],fillOpacity:d,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},k)}var qt=(a,b)=>{var{xAxis:c,width:d,height:e,offset:f}=a;return cC(pY(qk(qk(qk({},qe.defaultProps),c),{},{ticks:cD(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.left,f.left+f.width,b)},qu=(a,b)=>{var{yAxis:c,width:d,height:e,offset:f}=a;return cC(pY(qk(qk(qk({},qe.defaultProps),c),{},{ticks:cD(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.top,f.top+f.height,b)},qv={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function qw(a){var b=c3(),c=c4(),d=c2(),e=qk(qk({},mp(a,qv)),{},{x:v(a.x)?a.x:d.left,y:v(a.y)?a.y:d.top,width:v(a.width)?a.width:d.width,height:v(a.height)?a.height:d.height}),{xAxisId:f,yAxisId:g,x:h,y:i,width:j,height:k,syncWithTicks:l,horizontalValues:m,verticalValues:o}=e,p=cZ(),q=cj(a=>jr(a,"xAxis",f,p)),r=cj(a=>jr(a,"yAxis",g,p));if(!v(j)||j<=0||!v(k)||k<=0||!v(h)||h!==+h||!v(i)||i!==+i)return null;var s=e.verticalCoordinatesGenerator||qt,t=e.horizontalCoordinatesGenerator||qu,{horizontalPoints:u,verticalPoints:w}=e;if((!u||!u.length)&&"function"==typeof t){var x=m&&m.length,y=t({yAxis:r?qk(qk({},r),{},{ticks:x?m:r.ticks}):void 0,width:b,height:c,offset:d},!!x||l);E(Array.isArray(y),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof y,"]")),Array.isArray(y)&&(u=y)}if((!w||!w.length)&&"function"==typeof s){var z=o&&o.length,A=s({xAxis:q?qk(qk({},q),{},{ticks:z?o:q.ticks}):void 0,width:b,height:c,offset:d},!!z||l);E(Array.isArray(A),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof A,"]")),Array.isArray(A)&&(w=A)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(qn,{fill:e.fill,fillOpacity:e.fillOpacity,x:e.x,y:e.y,width:e.width,height:e.height,ry:e.ry}),n.createElement(qr,ql({},e,{horizontalPoints:u})),n.createElement(qs,ql({},e,{verticalPoints:w})),n.createElement(qp,ql({},e,{offset:d,horizontalPoints:u,xAxis:q,yAxis:r})),n.createElement(qq,ql({},e,{offset:d,verticalPoints:w,xAxis:q,yAxis:r})))}qw.displayName="CartesianGrid";var qx=["children"],qy=["dangerouslySetInnerHTML","ticks"];function qz(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function qA(){return(qA=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qB(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function qC(a){cf();var b=(0,n.useMemo)(()=>{var{children:b}=a;return qB(a,qx)},[a]),c=cj(a=>h7(a,b.id));return b===c?a.children:null}var qD=a=>{var{xAxisId:b,className:c}=a,d=cj(cX),e=cZ(),f="xAxis",g=cj(a=>jc(a,f,b,e)),h=cj(a=>js(a,f,b,e)),i=cj(a=>jj(a,b)),j=cj(a=>((a,b)=>{var c=cV(a),d=h7(a,b);if(null!=d){var e=jk(a,d.orientation,d.mirror)[b];return null==e?{x:c.left,y:0}:{x:c.left,y:e}}})(a,b));if(null==i||null==j)return null;var{dangerouslySetInnerHTML:k,ticks:l}=a,o=qB(a,qy);return n.createElement(qe,qA({},o,{scale:g,x:j.x,y:j.y,width:i.width,height:i.height,className:(0,m.$)("recharts-".concat(f," ").concat(f),c),viewBox:d,ticks:h}))},qE=a=>{var b,c,d,e,f;return n.createElement(qC,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.xAxisId,scale:a.scale,type:a.type,padding:a.padding,allowDataOverflow:a.allowDataOverflow,domain:a.domain,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,height:a.height,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(f=a.tick)||f,tickFormatter:a.tickFormatter},n.createElement(qD,a))};class qF extends n.Component{render(){return n.createElement(qE,this.props)}}qz(qF,"displayName","XAxis"),qz(qF,"defaultProps",{allowDataOverflow:h6.allowDataOverflow,allowDecimals:h6.allowDecimals,allowDuplicatedCategory:h6.allowDuplicatedCategory,height:h6.height,hide:!1,mirror:h6.mirror,orientation:h6.orientation,padding:h6.padding,reversed:h6.reversed,scale:h6.scale,tickCount:h6.tickCount,type:h6.type,xAxisId:0});var qG=["dangerouslySetInnerHTML","ticks"];function qH(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function qI(){return(qI=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qJ(a){return cf(),null}var qK=a=>{var b,{yAxisId:c,className:d,width:e,label:f}=a,g=(0,n.useRef)(null),h=(0,n.useRef)(null),i=cj(cX),j=cZ(),k=cf(),l="yAxis",o=cj(a=>jc(a,l,c,j)),p=cj(a=>jm(a,c)),q=cj(a=>((a,b)=>{var c=cV(a),d=h9(a,b);if(null!=d){var e=jl(a,d.orientation,d.mirror)[b];return null==e?{x:0,y:c.top}:{x:e,y:c.top}}})(a,c)),r=cj(a=>js(a,l,c,j));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==e||!p||p4(f)||(0,n.isValidElement)(f))){var a,b=g.current,d=null==b||null==(a=b.tickRefs)?void 0:a.current,{tickSize:i,tickMargin:j}=b.props,l=(a=>{var{ticks:b,label:c,labelGapWithTick:d=5,tickSize:e=0,tickMargin:f=0}=a,g=0;if(b){b.forEach(a=>{if(a){var b=a.getBoundingClientRect();b.width>g&&(g=b.width)}});var h=c?c.getBoundingClientRect().width:0;return Math.round(g+(e+f)+h+(c?d:0))}return 0})({ticks:d,label:h.current,labelGapWithTick:5,tickSize:i,tickMargin:j});Math.round(p.width)!==Math.round(l)&&k(kW({id:c,width:l}))}},[g,null==g||null==(b=g.current)||null==(b=b.tickRefs)?void 0:b.current,null==p?void 0:p.width,p,k,f,c,e]),null==p||null==q)return null;var{dangerouslySetInnerHTML:s,ticks:t}=a,u=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,qG);return n.createElement(qe,qI({},u,{ref:g,labelRef:h,scale:o,x:q.x,y:q.y,width:p.width,height:p.height,className:(0,m.$)("recharts-".concat(l," ").concat(l),d),viewBox:i,ticks:r}))},qL=a=>{var b,c,d,e,f;return n.createElement(n.Fragment,null,n.createElement(qJ,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.yAxisId,scale:a.scale,type:a.type,domain:a.domain,allowDataOverflow:a.allowDataOverflow,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,padding:a.padding,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,width:a.width,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(f=a.tick)||f,tickFormatter:a.tickFormatter}),n.createElement(qK,a))},qM={allowDataOverflow:h8.allowDataOverflow,allowDecimals:h8.allowDecimals,allowDuplicatedCategory:h8.allowDuplicatedCategory,hide:!1,mirror:h8.mirror,orientation:h8.orientation,padding:h8.padding,reversed:h8.reversed,scale:h8.scale,tickCount:h8.tickCount,type:h8.type,width:h8.width,yAxisId:0};class qN extends n.Component{render(){return n.createElement(qL,this.props)}}function qO(){return(qO=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}qH(qN,"displayName","YAxis"),qH(qN,"defaultProps",qM);var qP=a=>{var{cx:b,cy:c,r:d,className:e}=a,f=(0,m.$)("recharts-dot",e);return b===+b&&c===+c&&d===+d?n.createElement("circle",qO({},l1(a,!1),lV(a),{className:f,cx:b,cy:c,r:d})):null},qQ=c(9474),qR=c.n(qQ),qS=["valueAccessor"],qT=["data","dataKey","clockWise","id","textBreakAll"];function qU(){return(qU=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qV(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function qW(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?qV(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):qV(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function qX(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var qY=a=>Array.isArray(a.value)?qR()(a.value):a.value;function qZ(a){var{valueAccessor:b=qY}=a,c=qX(a,qS),{data:d,dataKey:e,clockWise:f,id:g,textBreakAll:h}=c,i=qX(c,qT);return d&&d.length?n.createElement(mV,{className:"recharts-label-list"},d.map((a,c)=>{var d=null==e?b(a,c):cA(a&&a.payload,e),j=null==g?{}:{id:"".concat(g,"-").concat(c)};return n.createElement(p5,qU({},l1(a,!0),i,j,{parentViewBox:a.parentViewBox,value:d,textBreakAll:h,viewBox:p5.parseViewBox(null==f?a:qW(qW({},a),{},{clockWise:f})),key:"label-".concat(c),index:c}))})):null}qZ.displayName="LabelList",qZ.renderCallByParent=function(a,b){var c,d=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&d&&!a.label)return null;var{children:e}=a,f=l_(e,qZ).map((a,c)=>(0,n.cloneElement)(a,{data:b,key:"labelList-".concat(c)}));return d?[(c=a.label,c?!0===c?n.createElement(qZ,{key:"labelList-implicit",data:b}):n.isValidElement(c)||p4(c)?n.createElement(qZ,{key:"labelList-implicit",data:b,content:c}):"object"==typeof c?n.createElement(qZ,qU({data:b},c,{key:"labelList-implicit"})):null:null),...f]:f};var q$=["children"],q_=()=>{},q0=(0,n.createContext)({addErrorBar:q_,removeErrorBar:q_}),q1=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function q2(a){var{children:b}=a,c=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,q$);return n.createElement(q1.Provider,{value:c},b)}var q3=a=>{var{children:b,xAxisId:c,yAxisId:d,zAxisId:e,dataKey:f,data:g,stackId:h,hide:i,type:j,barSize:k}=a,[l,m]=n.useState([]),o=(0,n.useCallback)(a=>{m(b=>[...b,a])},[m]),p=(0,n.useCallback)(a=>{m(b=>b.filter(b=>b!==a))},[m]),q=cZ();return n.createElement(q0.Provider,{value:{addErrorBar:o,removeErrorBar:p}},n.createElement(mR,{type:j,data:g,xAxisId:c,yAxisId:d,zAxisId:e,dataKey:f,errorBars:l,stackId:h,hide:i,barSize:k,isPanorama:q}),b)};function q4(a){var{addErrorBar:b,removeErrorBar:c}=(0,n.useContext)(q0);return null}var q5=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function q6(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function q7(){return(q7=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function q8(a){var b,c,{direction:d,width:e,dataKey:f,isAnimationActive:g,animationBegin:h,animationDuration:i,animationEasing:j}=a,k=l1(function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,q5),!1),{data:l,dataPointFormatter:m,xAxisId:o,yAxisId:p,errorBarOffset:q}=(0,n.useContext)(q1),r=(b=cZ(),cj(a=>ju(a,"xAxis",o,b))),s=(c=cZ(),cj(a=>ju(a,"yAxis",p,c)));if((null==r?void 0:r.scale)==null||(null==s?void 0:s.scale)==null||null==l||"x"===d&&"number"!==r.type)return null;var t=l.map(a=>{var b,c,{x:l,y:o,value:p,errorVal:t}=m(a,f,d);if(!t)return null;var u=[];if(Array.isArray(t)?[b,c]=t:b=c=t,"x"===d){var{scale:v}=r,w=o+q,x=w+e,y=w-e,z=v(p-b),A=v(p+c);u.push({x1:A,y1:x,x2:A,y2:y}),u.push({x1:z,y1:w,x2:A,y2:w}),u.push({x1:z,y1:x,x2:z,y2:y})}else if("y"===d){var{scale:B}=s,C=l+q,D=C-e,E=C+e,F=B(p-b),G=B(p+c);u.push({x1:D,y1:G,x2:E,y2:G}),u.push({x1:C,y1:F,x2:C,y2:G}),u.push({x1:D,y1:F,x2:E,y2:F})}var H="".concat(l+q,"px ").concat(o+q,"px");return n.createElement(mV,q7({className:"recharts-errorBar",key:"bar-".concat(u.map(a=>"".concat(a.x1,"-").concat(a.x2,"-").concat(a.y1,"-").concat(a.y2)))},k),u.map(a=>{var b=g?{transformOrigin:"".concat(a.x1-5,"px")}:void 0;return n.createElement(oi,{from:{transform:"scaleY(0)",transformOrigin:H},to:{transform:"scaleY(1)",transformOrigin:H},begin:h,easing:j,isActive:g,duration:i,key:"line-".concat(a.x1,"-").concat(a.x2,"-").concat(a.y1,"-").concat(a.y2),style:{transformOrigin:H}},n.createElement("line",q7({},a,{style:b})))}))});return n.createElement(mV,{className:"recharts-errorBars"},t)}var q9=(0,n.createContext)(void 0);function ra(a){var{direction:b,children:c}=a;return n.createElement(q9.Provider,{value:b},c)}var rb={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function rc(a){var b,c,d=(b=a.direction,c=(0,n.useContext)(q9),null!=b?b:null!=c?c:"x"),{width:e,isAnimationActive:f,animationBegin:g,animationDuration:h,animationEasing:i}=mp(a,rb);return n.createElement(n.Fragment,null,n.createElement(q4,{dataKey:a.dataKey,direction:d}),n.createElement(q8,q7({},a,{direction:d,width:e,isAnimationActive:f,animationBegin:g,animationDuration:h,animationEasing:i})))}class rd extends n.Component{render(){return n.createElement(rc,this.props)}}function re(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function rf(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?re(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):re(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function rg(a){var{points:b,mainColor:c,activeDot:d,itemDataKey:e}=a,f=cj(kl),g=cj(kt);if(null==b||null==g)return null;var h=b.find(a=>g.includes(a.payload));return null==h?null:(a=>{var b,{point:c,childIndex:d,mainColor:e,activeDot:f,dataKey:g}=a;if(!1===f||null==c.x||null==c.y)return null;var h=rf(rf({index:d,dataKey:g,cx:c.x,cy:c.y,r:4,fill:null!=e?e:"none",strokeWidth:2,stroke:"#fff",payload:c.payload,value:c.value},l1(f,!1)),lV(f));return b=(0,n.isValidElement)(f)?(0,n.cloneElement)(f,h):"function"==typeof f?f(h):n.createElement(qP,h),n.createElement(mV,{className:"recharts-active-dot"},b)})({point:h,childIndex:Number(f),mainColor:c,dataKey:e,activeDot:d})}function rh(a,b){var c,d,e=cj(b=>h7(b,a)),f=cj(a=>h9(a,b)),g=null!=(c=null==e?void 0:e.allowDataOverflow)?c:h6.allowDataOverflow,h=null!=(d=null==f?void 0:f.allowDataOverflow)?d:h8.allowDataOverflow;return{needClip:g||h,needClipX:g,needClipY:h}}function ri(a){var{xAxisId:b,yAxisId:c,clipPathId:d}=a,e=mj(),{needClipX:f,needClipY:g,needClip:h}=rh(b,c);if(!h)return null;var{x:i,y:j,width:k,height:l}=e;return n.createElement("clipPath",{id:"clipPath-".concat(d)},n.createElement("rect",{x:f?i:i-k/2,y:g?j:j-l/2,width:f?k:2*k,height:g?l:2*l}))}q6(rd,"defaultProps",rb),q6(rd,"displayName","ErrorBar");var rj=(a,b,c,d)=>ju(a,"xAxis",b,d),rk=(a,b,c,d)=>jt(a,"xAxis",b,d),rl=(a,b,c,d)=>ju(a,"yAxis",c,d),rm=(a,b,c,d)=>jt(a,"yAxis",c,d),rn=ca([c5,rj,rl,rk,rm],(a,b,c,d,e)=>cB(a,"xAxis")?cI(b,d,!1):cI(c,e,!1)),ro=ca([ih,(a,b,c,d,e)=>e],(a,b)=>{if(a.some(a=>"line"===a.type&&b.dataKey===a.dataKey&&b.data===a.data))return b}),rp=ca([c5,rj,rl,rk,rm,ro,rn,gP],(a,b,c,d,e,f,g,h)=>{var i,{chartData:j,dataStartIndex:k,dataEndIndex:l}=h;if(null!=f&&null!=b&&null!=c&&null!=d&&null!=e&&0!==d.length&&0!==e.length&&null!=g){var{dataKey:m,data:n}=f;if(null!=(i=null!=n&&n.length>0?n:null==j?void 0:j.slice(k,l+1)))return function(a){var{layout:b,xAxis:c,yAxis:d,xAxisTicks:e,yAxisTicks:f,dataKey:g,bandSize:h,displayedData:i}=a;return i.map((a,i)=>{var j=cA(a,g);if("horizontal"===b)return{x:cF({axis:c,ticks:e,bandSize:h,entry:a,index:i}),y:null==j?null:d.scale(j),value:j,payload:a};return{x:null==j?null:c.scale(j),y:cF({axis:d,ticks:f,bandSize:h,entry:a,index:i}),value:j,payload:a}})}({layout:a,xAxis:b,yAxis:c,xAxisTicks:d,yAxisTicks:e,dataKey:m,bandSize:g,displayedData:i})}}),rq=["type","layout","connectNulls","needClip"],rr=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function rs(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function rt(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ru(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?rt(Object(c),!0).forEach(function(b){rv(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):rt(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function rv(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function rw(){return(rw=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function rx(a){var{dataKey:b,data:c,stroke:d,strokeWidth:e,fill:f,name:g,hide:h,unit:i}=a;return{dataDefinedOnItem:c,positions:void 0,settings:{stroke:d,strokeWidth:e,fill:f,dataKey:b,nameKey:void 0,name:cK(g,b),hide:h,type:a.tooltipType,color:a.stroke,unit:i}}}var ry=(a,b)=>"".concat(b,"px ").concat(a-b,"px");function rz(a){var{clipPathId:b,points:c,props:d}=a,{dot:e,dataKey:f,needClip:g}=d;if(null==c||!e&&1!==c.length)return null;var h=l0(e),i=l1(d,!1),j=l1(e,!0),k=c.map((a,b)=>{var d,g=ru(ru(ru({key:"dot-".concat(b),r:3},i),j),{},{index:b,cx:a.x,cy:a.y,dataKey:f,value:a.value,payload:a.payload,points:c});if(n.isValidElement(e))d=n.cloneElement(e,g);else if("function"==typeof e)d=e(g);else{var h=(0,m.$)("recharts-line-dot","boolean"!=typeof e?e.className:"");d=n.createElement(qP,rw({},g,{className:h}))}return d}),l={clipPath:g?"url(#clipPath-".concat(h?"":"dots-").concat(b,")"):null};return n.createElement(mV,rw({className:"recharts-line-dots",key:"dots"},l),k)}function rA(a){var{clipPathId:b,pathRef:c,points:d,strokeDasharray:e,props:f,showLabels:g}=a,{type:h,layout:i,connectNulls:j,needClip:k}=f,l=ru(ru({},l1(rs(f,rq),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:k?"url(#clipPath-".concat(b,")"):null,points:d,type:h,layout:i,connectNulls:j,strokeDasharray:null!=e?e:f.strokeDasharray});return n.createElement(n.Fragment,null,(null==d?void 0:d.length)>1&&n.createElement(nt,rw({},l,{pathRef:c})),n.createElement(rz,{points:d,clipPathId:b,props:f}),g&&qZ.renderCallByParent(f,d))}function rB(a){var{clipPathId:b,props:c,pathRef:d,previousPointsRef:e,longestAnimatedLengthRef:f}=a,{points:g,strokeDasharray:h,isAnimationActive:i,animationBegin:j,animationDuration:k,animationEasing:l,animateNewValues:m,width:o,height:p,onAnimationEnd:q,onAnimationStart:r}=c,s=e.current,t=o_(c,"recharts-line-"),[u,v]=(0,n.useState)(!1),w=(0,n.useCallback)(()=>{"function"==typeof q&&q(),v(!1)},[q]),x=(0,n.useCallback)(()=>{"function"==typeof r&&r(),v(!0)},[r]),y=function(a){try{return a&&a.getTotalLength&&a.getTotalLength()||0}catch(a){return 0}}(d.current),z=f.current;return n.createElement(oi,{begin:j,duration:k,isActive:i,easing:l,from:{t:0},to:{t:1},onAnimationEnd:w,onAnimationStart:x,key:t},a=>{var i,{t:j}=a,k=Math.min(B(z,y+z)(j),y);if(i=h?((a,b,c)=>{var d=c.reduce((a,b)=>a+b);if(!d)return ry(b,a);for(var e=Math.floor(a/d),f=a%d,g=b-a,h=[],i=0,j=0;i<c.length;j+=c[i],++i)if(j+c[i]>f){h=[...c.slice(0,i),f-j];break}var k=h.length%2==0?[0,g]:[g];return[...function(a,b){for(var c=a.length%2!=0?[...a,0]:a,d=[],e=0;e<b;++e)d=[...d,...c];return d}(c,e),...h,...k].map(a=>"".concat(a,"px")).join(", ")})(k,y,"".concat(h).split(/[,\s]+/gim).map(a=>parseFloat(a))):ry(y,k),s){var l=s.length/g.length,q=1===j?g:g.map((a,b)=>{var c=Math.floor(b*l);if(s[c]){var d=s[c],e=B(d.x,a.x),f=B(d.y,a.y);return ru(ru({},a),{},{x:e(j),y:f(j)})}if(m){var g=B(2*o,a.x),h=B(p/2,a.y);return ru(ru({},a),{},{x:g(j),y:h(j)})}return ru(ru({},a),{},{x:a.x,y:a.y})});return e.current=q,n.createElement(rA,{props:c,points:q,clipPathId:b,pathRef:d,showLabels:!u,strokeDasharray:i})}return j>0&&y>0&&(e.current=g,f.current=k),n.createElement(rA,{props:c,points:g,clipPathId:b,pathRef:d,showLabels:!u,strokeDasharray:i})})}function rC(a){var{clipPathId:b,props:c}=a,{points:d,isAnimationActive:e}=c,f=(0,n.useRef)(null),g=(0,n.useRef)(0),h=(0,n.useRef)(null),i=f.current;return e&&d&&d.length&&i!==d?n.createElement(rB,{props:c,clipPathId:b,previousPointsRef:f,longestAnimatedLengthRef:g,pathRef:h}):n.createElement(rA,{props:c,points:d,clipPathId:b,pathRef:h,showLabels:!0})}var rD=(a,b)=>({x:a.x,y:a.y,value:a.value,errorVal:cA(a.payload,b)});class rE extends n.Component{constructor(){super(...arguments),rv(this,"id",y("recharts-line-"))}render(){var a,{hide:b,dot:c,points:d,className:e,xAxisId:f,yAxisId:g,top:h,left:i,width:j,height:k,id:l,needClip:o,layout:p}=this.props;if(b)return null;var q=(0,m.$)("recharts-line",e),r=null==l?this.id:l,{r:s=3,strokeWidth:t=2}=null!=(a=l1(c,!1))?a:{r:3,strokeWidth:2},u=l0(c),v=2*s+t;return n.createElement(n.Fragment,null,n.createElement(mV,{className:q},o&&n.createElement("defs",null,n.createElement(ri,{clipPathId:r,xAxisId:f,yAxisId:g}),!u&&n.createElement("clipPath",{id:"clipPath-dots-".concat(r)},n.createElement("rect",{x:i-v/2,y:h-v/2,width:j+v,height:k+v}))),n.createElement(rC,{props:this.props,clipPathId:r}),n.createElement(ra,{direction:"horizontal"===p?"y":"x"},n.createElement(q2,{xAxisId:f,yAxisId:g,data:d,dataPointFormatter:rD,errorBarOffset:0},this.props.children))),n.createElement(rg,{activeDot:this.props.activeDot,points:d,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var rF={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!nu.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function rG(a){var b=mp(a,rF),{activeDot:c,animateNewValues:d,animationBegin:e,animationDuration:f,animationEasing:g,connectNulls:h,dot:i,hide:j,isAnimationActive:k,label:l,legendType:m,xAxisId:o,yAxisId:p}=b,q=rs(b,rr),{needClip:r}=rh(o,p),{height:s,width:t,x:u,y:v}=mj(),w=c6(),x=cZ(),y=(0,n.useMemo)(()=>({dataKey:a.dataKey,data:a.data}),[a.dataKey,a.data]),z=cj(a=>rp(a,o,p,x,y));return"horizontal"!==w&&"vertical"!==w?null:n.createElement(rE,rw({},q,{connectNulls:h,dot:i,activeDot:c,animateNewValues:d,animationBegin:e,animationDuration:f,animationEasing:g,isAnimationActive:k,hide:j,label:l,legendType:m,xAxisId:o,yAxisId:p,points:z,layout:w,height:s,width:t,left:u,top:v,needClip:r}))}class rH extends n.PureComponent{render(){return n.createElement(q3,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},n.createElement(oZ,{legendPayload:(a=>{var{dataKey:b,name:c,stroke:d,legendType:e,hide:f}=a;return[{inactive:f,dataKey:b,type:e,color:d,value:cK(c,b),payload:a}]})(this.props)}),n.createElement(oY,{fn:rx,args:this.props}),n.createElement(rG,this.props))}}rv(rH,"displayName","Line"),rv(rH,"defaultProps",rF);var rI=c(39850),rJ=c(33465),rK=c(61489),rL=c(35536),rM=c(73458),rN=c(31212),rO=class extends rL.Q{constructor(a,b){super(),this.options=b,this.#a=a,this.#b=null,this.#c=(0,rM.T)(),this.options.experimental_prefetchInRender||this.#c.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(b)}#a;#d=void 0;#e=void 0;#f=void 0;#g;#h;#c;#b;#i;#j;#k;#l;#m;#n;#o=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#d.addObserver(this),rP(this.#d,this.options)?this.#p():this.updateResult(),this.#q())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return rQ(this.#d,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return rQ(this.#d,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#r(),this.#s(),this.#d.removeObserver(this)}setOptions(a){let b=this.options,c=this.#d;if(this.options=this.#a.defaultQueryOptions(a),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,rN.Eh)(this.options.enabled,this.#d))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#t(),this.#d.setOptions(this.options),b._defaulted&&!(0,rN.f8)(this.options,b)&&this.#a.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#d,observer:this});let d=this.hasListeners();d&&rR(this.#d,c,this.options,b)&&this.#p(),this.updateResult(),d&&(this.#d!==c||(0,rN.Eh)(this.options.enabled,this.#d)!==(0,rN.Eh)(b.enabled,this.#d)||(0,rN.d2)(this.options.staleTime,this.#d)!==(0,rN.d2)(b.staleTime,this.#d))&&this.#u();let e=this.#v();d&&(this.#d!==c||(0,rN.Eh)(this.options.enabled,this.#d)!==(0,rN.Eh)(b.enabled,this.#d)||e!==this.#n)&&this.#w(e)}getOptimisticResult(a){var b,c;let d=this.#a.getQueryCache().build(this.#a,a),e=this.createResult(d,a);return b=this,c=e,(0,rN.f8)(b.getCurrentResult(),c)||(this.#f=e,this.#h=this.options,this.#g=this.#d.state),e}getCurrentResult(){return this.#f}trackResult(a,b){return new Proxy(a,{get:(a,c)=>(this.trackProp(c),b?.(c),Reflect.get(a,c))})}trackProp(a){this.#o.add(a)}getCurrentQuery(){return this.#d}refetch({...a}={}){return this.fetch({...a})}fetchOptimistic(a){let b=this.#a.defaultQueryOptions(a),c=this.#a.getQueryCache().build(this.#a,b);return c.fetch().then(()=>this.createResult(c,b))}fetch(a){return this.#p({...a,cancelRefetch:a.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#f))}#p(a){this.#t();let b=this.#d.fetch(this.options,a);return a?.throwOnError||(b=b.catch(rN.lQ)),b}#u(){this.#r();let a=(0,rN.d2)(this.options.staleTime,this.#d);if(rN.S$||this.#f.isStale||!(0,rN.gn)(a))return;let b=(0,rN.j3)(this.#f.dataUpdatedAt,a);this.#l=setTimeout(()=>{this.#f.isStale||this.updateResult()},b+1)}#v(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#d):this.options.refetchInterval)??!1}#w(a){this.#s(),this.#n=a,!rN.S$&&!1!==(0,rN.Eh)(this.options.enabled,this.#d)&&(0,rN.gn)(this.#n)&&0!==this.#n&&(this.#m=setInterval(()=>{(this.options.refetchIntervalInBackground||rI.m.isFocused())&&this.#p()},this.#n))}#q(){this.#u(),this.#w(this.#v())}#r(){this.#l&&(clearTimeout(this.#l),this.#l=void 0)}#s(){this.#m&&(clearInterval(this.#m),this.#m=void 0)}createResult(a,b){let c,d=this.#d,e=this.options,f=this.#f,g=this.#g,h=this.#h,i=a!==d?a.state:this.#e,{state:j}=a,k={...j},l=!1;if(b._optimisticResults){let c=this.hasListeners(),f=!c&&rP(a,b),g=c&&rR(a,d,b,e);(f||g)&&(k={...k,...(0,rK.k)(j.data,a.options)}),"isRestoring"===b._optimisticResults&&(k.fetchStatus="idle")}let{error:m,errorUpdatedAt:n,status:o}=k;c=k.data;let p=!1;if(void 0!==b.placeholderData&&void 0===c&&"pending"===o){let a;f?.isPlaceholderData&&b.placeholderData===h?.placeholderData?(a=f.data,p=!0):a="function"==typeof b.placeholderData?b.placeholderData(this.#k?.state.data,this.#k):b.placeholderData,void 0!==a&&(o="success",c=(0,rN.pl)(f?.data,a,b),l=!0)}if(b.select&&void 0!==c&&!p)if(f&&c===g?.data&&b.select===this.#i)c=this.#j;else try{this.#i=b.select,c=b.select(c),c=(0,rN.pl)(f?.data,c,b),this.#j=c,this.#b=null}catch(a){this.#b=a}this.#b&&(m=this.#b,c=this.#j,n=Date.now(),o="error");let q="fetching"===k.fetchStatus,r="pending"===o,s="error"===o,t=r&&q,u=void 0!==c,v={status:o,fetchStatus:k.fetchStatus,isPending:r,isSuccess:"success"===o,isError:s,isInitialLoading:t,isLoading:t,data:c,dataUpdatedAt:k.dataUpdatedAt,error:m,errorUpdatedAt:n,failureCount:k.fetchFailureCount,failureReason:k.fetchFailureReason,errorUpdateCount:k.errorUpdateCount,isFetched:k.dataUpdateCount>0||k.errorUpdateCount>0,isFetchedAfterMount:k.dataUpdateCount>i.dataUpdateCount||k.errorUpdateCount>i.errorUpdateCount,isFetching:q,isRefetching:q&&!r,isLoadingError:s&&!u,isPaused:"paused"===k.fetchStatus,isPlaceholderData:l,isRefetchError:s&&u,isStale:rS(a,b),refetch:this.refetch,promise:this.#c,isEnabled:!1!==(0,rN.Eh)(b.enabled,a)};if(this.options.experimental_prefetchInRender){let b=a=>{"error"===v.status?a.reject(v.error):void 0!==v.data&&a.resolve(v.data)},c=()=>{b(this.#c=v.promise=(0,rM.T)())},e=this.#c;switch(e.status){case"pending":a.queryHash===d.queryHash&&b(e);break;case"fulfilled":("error"===v.status||v.data!==e.value)&&c();break;case"rejected":("error"!==v.status||v.error!==e.reason)&&c()}}return v}updateResult(){let a=this.#f,b=this.createResult(this.#d,this.options);if(this.#g=this.#d.state,this.#h=this.options,void 0!==this.#g.data&&(this.#k=this.#d),(0,rN.f8)(b,a))return;this.#f=b;let c=()=>{if(!a)return!0;let{notifyOnChangeProps:b}=this.options,c="function"==typeof b?b():b;if("all"===c||!c&&!this.#o.size)return!0;let d=new Set(c??this.#o);return this.options.throwOnError&&d.add("error"),Object.keys(this.#f).some(b=>this.#f[b]!==a[b]&&d.has(b))};this.#x({listeners:c()})}#t(){let a=this.#a.getQueryCache().build(this.#a,this.options);if(a===this.#d)return;let b=this.#d;this.#d=a,this.#e=a.state,this.hasListeners()&&(b?.removeObserver(this),a.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#q()}#x(a){rJ.jG.batch(()=>{a.listeners&&this.listeners.forEach(a=>{a(this.#f)}),this.#a.getQueryCache().notify({query:this.#d,type:"observerResultsUpdated"})})}};function rP(a,b){return!1!==(0,rN.Eh)(b.enabled,a)&&void 0===a.state.data&&("error"!==a.state.status||!1!==b.retryOnMount)||void 0!==a.state.data&&rQ(a,b,b.refetchOnMount)}function rQ(a,b,c){if(!1!==(0,rN.Eh)(b.enabled,a)&&"static"!==(0,rN.d2)(b.staleTime,a)){let d="function"==typeof c?c(a):c;return"always"===d||!1!==d&&rS(a,b)}return!1}function rR(a,b,c,d){return(a!==b||!1===(0,rN.Eh)(d.enabled,a))&&(!c.suspense||"error"!==a.state.status)&&rS(a,c)}function rS(a,b){return!1!==(0,rN.Eh)(b.enabled,a)&&a.isStaleByTime((0,rN.d2)(b.staleTime,a))}var rT=c(8693),rU=n.createContext(function(){let a=!1;return{clearReset:()=>{a=!1},reset:()=>{a=!0},isReset:()=>a}}()),rV=n.createContext(!1);rV.Provider;var rW=(a,b,c)=>b.fetchOptimistic(a).catch(()=>{c.clearReset()});function rX(){let{data:a,isLoading:b,error:c}=function(a,b,c){let d=n.useContext(rV),e=n.useContext(rU),f=(0,rT.jE)(c),g=f.defaultQueryOptions(a);if(f.getDefaultOptions().queries?._experimental_beforeQuery?.(g),g._optimisticResults=d?"isRestoring":"optimistic",g.suspense){let a=a=>"static"===a?a:Math.max(a??1e3,1e3),b=g.staleTime;g.staleTime="function"==typeof b?(...c)=>a(b(...c)):a(b),"number"==typeof g.gcTime&&(g.gcTime=Math.max(g.gcTime,1e3))}(g.suspense||g.throwOnError||g.experimental_prefetchInRender)&&!e.isReset()&&(g.retryOnMount=!1),n.useEffect(()=>{e.clearReset()},[e]);let h=!f.getQueryCache().get(g.queryHash),[i]=n.useState(()=>new b(f,g)),j=i.getOptimisticResult(g),k=!d&&!1!==a.subscribed;if(n.useSyncExternalStore(n.useCallback(a=>{let b=k?i.subscribe(rJ.jG.batchCalls(a)):rN.lQ;return i.updateResult(),b},[i,k]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),n.useEffect(()=>{i.setOptions(g)},[g,i]),g?.suspense&&j.isPending)throw rW(g,i,e);if((({result:a,errorResetBoundary:b,throwOnError:c,query:d,suspense:e})=>a.isError&&!b.isReset()&&!a.isFetching&&d&&(e&&void 0===a.data||(0,rN.GU)(c,[a.error,d])))({result:j,errorResetBoundary:e,throwOnError:g.throwOnError,query:f.getQueryCache().get(g.queryHash),suspense:g.suspense}))throw j.error;if(f.getDefaultOptions().queries?._experimental_afterQuery?.(g,j),g.experimental_prefetchInRender&&!rN.S$&&j.isLoading&&j.isFetching&&!d){let a=h?rW(g,i,e):f.getQueryCache().get(g.queryHash)?.promise;a?.catch(rN.lQ).finally(()=>{i.updateResult()})}return g.notifyOnChangeProps?j:i.trackResult(j)}({queryKey:["dashboard-stats"],queryFn:async()=>{let a=await fetch("/api/dashboard/stats");if(!a.ok)throw Error("Failed to fetch dashboard stats");return a.json()},staleTime:3e5},rO,void 0);if(b)return(0,e.jsxs)("div",{className:"space-y-6",children:[(0,e.jsx)("h1",{className:"text-3xl font-bold",children:"Dashboard"}),(0,e.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,e.jsxs)(f.Zp,{children:[(0,e.jsx)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,e.jsx)(f.ZB,{className:"text-sm font-medium",children:"Loading..."})}),(0,e.jsx)(f.Wu,{children:(0,e.jsx)("div",{className:"text-2xl font-bold",children:"-"})})]},b))})]});if(c||!a)return(0,e.jsxs)("div",{className:"space-y-6",children:[(0,e.jsx)("h1",{className:"text-3xl font-bold",children:"Dashboard"}),(0,e.jsxs)("p",{children:["Failed to load dashboard data. ",c?.message]})]});let{overview:d}=a;return(0,e.jsxs)("div",{className:"space-y-6",children:[(0,e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,e.jsx)("h1",{className:"text-3xl font-bold",children:"Dashboard"}),(0,e.jsxs)(g.E,{variant:"outline",className:"text-sm",children:[d.conversionRate,"% conversion rate"]})]}),(0,e.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,e.jsxs)(f.Zp,{children:[(0,e.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,e.jsx)(f.ZB,{className:"text-sm font-medium",children:"Total Leads"}),(0,e.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,e.jsxs)(f.Wu,{children:[(0,e.jsx)("div",{className:"text-2xl font-bold",children:d.totalLeads}),(0,e.jsxs)("p",{className:"text-xs text-muted-foreground",children:[d.leadsThisMonth," this month"]})]})]}),(0,e.jsxs)(f.Zp,{children:[(0,e.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,e.jsx)(f.ZB,{className:"text-sm font-medium",children:"New Leads"}),(0,e.jsx)(j,{className:"h-4 w-4 text-muted-foreground"})]}),(0,e.jsxs)(f.Wu,{children:[(0,e.jsx)("div",{className:"text-2xl font-bold",children:d.newLeads}),(0,e.jsxs)("p",{className:"text-xs text-muted-foreground",children:[d.leadsThisWeek," this week"]})]})]}),(0,e.jsxs)(f.Zp,{children:[(0,e.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,e.jsx)(f.ZB,{className:"text-sm font-medium",children:"Converted"}),(0,e.jsx)(k,{className:"h-4 w-4 text-muted-foreground"})]}),(0,e.jsxs)(f.Wu,{children:[(0,e.jsx)("div",{className:"text-2xl font-bold",children:d.convertedLeads}),(0,e.jsxs)("p",{className:"text-xs text-muted-foreground",children:[d.conversionRate,"% conversion rate"]})]})]}),(0,e.jsxs)(f.Zp,{children:[(0,e.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,e.jsx)(f.ZB,{className:"text-sm font-medium",children:"Follow-ups"}),(0,e.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,e.jsxs)(f.Wu,{children:[(0,e.jsx)("div",{className:"text-2xl font-bold",children:d.upcomingFollowUps}),(0,e.jsx)("p",{className:"text-xs text-muted-foreground",children:"upcoming tasks"})]})]})]}),(0,e.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,e.jsxs)(f.Zp,{children:[(0,e.jsxs)(f.aR,{children:[(0,e.jsx)(f.ZB,{children:"Lead Status Distribution"}),(0,e.jsx)(f.BT,{children:"Current breakdown of lead statuses"})]}),(0,e.jsx)(f.Wu,{children:(0,e.jsx)("div",{className:"h-80",children:(0,e.jsx)(H,{width:"100%",height:"100%",children:(0,e.jsxs)(mw,{children:[(0,e.jsx)(pe,{data:[{name:"New",value:d.newLeads,color:"#3b82f6"},{name:"Contacted",value:d.contactedLeads,color:"#6b7280"},{name:"Interested",value:d.interestedLeads,color:"#eab308"},{name:"Converted",value:d.convertedLeads,color:"#22c55e"},{name:"Lost",value:d.lostLeads,color:"#ef4444"}].filter(a=>a.value>0),cx:"50%",cy:"50%",labelLine:!1,label:({name:a,percent:b})=>`${a} ${(100*b).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:[{name:"New",value:d.newLeads,color:"#3b82f6"},{name:"Contacted",value:d.contactedLeads,color:"#6b7280"},{name:"Interested",value:d.interestedLeads,color:"#eab308"},{name:"Converted",value:d.convertedLeads,color:"#22c55e"},{name:"Lost",value:d.lostLeads,color:"#ef4444"}].filter(a=>a.value>0).map((a,b)=>(0,e.jsx)(nU,{fill:a.color},`cell-${b}`))}),(0,e.jsx)(pL,{})]})})})})]}),(0,e.jsxs)(f.Zp,{children:[(0,e.jsxs)(f.aR,{children:[(0,e.jsx)(f.ZB,{children:"Lead Conversion Trend"}),(0,e.jsx)(f.BT,{children:"Monthly lead conversion over time"})]}),(0,e.jsx)(f.Wu,{children:(0,e.jsx)("div",{className:"h-80",children:(0,e.jsx)(H,{width:"100%",height:"100%",children:(0,e.jsxs)(pR,{data:a.conversionChart,children:[(0,e.jsx)(qw,{strokeDasharray:"3 3"}),(0,e.jsx)(qF,{dataKey:"month"}),(0,e.jsx)(qN,{}),(0,e.jsx)(pL,{}),(0,e.jsx)(rH,{type:"monotone",dataKey:"new",stroke:"#3b82f6",name:"New"}),(0,e.jsx)(rH,{type:"monotone",dataKey:"converted",stroke:"#22c55e",name:"Converted"}),(0,e.jsx)(rH,{type:"monotone",dataKey:"lost",stroke:"#ef4444",name:"Lost"})]})})})})]})]}),(0,e.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,e.jsxs)(f.Zp,{children:[(0,e.jsxs)(f.aR,{children:[(0,e.jsx)(f.ZB,{children:"Lead Status Breakdown"}),(0,e.jsx)(f.BT,{children:"Current distribution of lead statuses"})]}),(0,e.jsxs)(f.Wu,{className:"space-y-4",children:[(0,e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,e.jsx)("span",{className:"text-sm",children:"New"}),(0,e.jsx)(g.E,{variant:"secondary",children:d.newLeads})]}),(0,e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,e.jsx)("span",{className:"text-sm",children:"Contacted"}),(0,e.jsx)(g.E,{variant:"outline",children:d.contactedLeads})]}),(0,e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,e.jsx)("span",{className:"text-sm",children:"Interested"}),(0,e.jsx)(g.E,{className:"bg-yellow-500 text-white",children:d.interestedLeads})]}),(0,e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,e.jsx)("span",{className:"text-sm",children:"Converted"}),(0,e.jsx)(g.E,{className:"bg-green-500 text-white",children:d.convertedLeads})]}),(0,e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,e.jsx)("span",{className:"text-sm",children:"Lost"}),(0,e.jsx)(g.E,{variant:"destructive",children:d.lostLeads})]})]})]}),(0,e.jsxs)(f.Zp,{children:[(0,e.jsxs)(f.aR,{children:[(0,e.jsx)(f.ZB,{children:"Recent Activity"}),(0,e.jsx)(f.BT,{children:"Summary of recent lead activity"})]}),(0,e.jsxs)(f.Wu,{className:"space-y-4",children:[(0,e.jsxs)("div",{className:"text-sm",children:[(0,e.jsx)("p",{className:"font-medium",children:"This Month"}),(0,e.jsxs)("p",{className:"text-muted-foreground",children:[d.leadsThisMonth," new leads added"]})]}),(0,e.jsxs)("div",{className:"text-sm",children:[(0,e.jsx)("p",{className:"font-medium",children:"This Week"}),(0,e.jsxs)("p",{className:"text-muted-foreground",children:[d.leadsThisWeek," new leads added"]})]}),(0,e.jsxs)("div",{className:"text-sm",children:[(0,e.jsx)("p",{className:"font-medium",children:"Upcoming"}),(0,e.jsxs)("p",{className:"text-muted-foreground",children:[d.upcomingFollowUps," follow-ups scheduled"]})]})]})]})]})]})}},45263:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(59618),e=c(92681),f=c(90830),g=c(17617);b.uniqBy=function(a,b=e.identity){return f.isArrayLikeObject(a)?d.uniqBy(Array.from(a),g.iteratee(b)):[]}},48130:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isLength=function(a){return Number.isSafeInteger(a)&&a>=0}},49899:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObjectLike=function(a){return"object"==typeof a&&null!==a}},52371:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(92923);b.cloneDeep=function(a){return d.cloneDeepWithImpl(a,void 0,a,new Map,void 0)}},53332:(a,b,c)=>{"use strict";var d=c(43210),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useState,g=d.useEffect,h=d.useLayoutEffect,i=d.useDebugValue;function j(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!e(a,c)}catch(a){return!0}}var k="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,b){return b()}:function(a,b){var c=b(),d=f({inst:{value:c,getSnapshot:b}}),e=d[0].inst,k=d[1];return h(function(){e.value=c,e.getSnapshot=b,j(e)&&k({inst:e})},[a,c,b]),g(function(){return j(e)&&k({inst:e}),a(function(){j(e)&&k({inst:e})})},[a]),i(c),c};b.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:k},55100:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.debounce=function(a,b,{signal:c,edges:d}={}){let e,f=null,g=null!=d&&d.includes("leading"),h=null==d||d.includes("trailing"),i=()=>{null!==f&&(a.apply(e,f),e=void 0,f=null)},j=null,k=()=>{null!=j&&clearTimeout(j),j=setTimeout(()=>{j=null,h&&i(),l()},b)},l=()=>{null!==j&&(clearTimeout(j),j=null),e=void 0,f=null},m=function(...a){if(c?.aborted)return;e=this,f=a;let b=null==j;k(),g&&b&&i()};return m.schedule=k,m.cancel=l,m.flush=()=>{i()},c?.addEventListener("abort",l,{once:!0}),m}},57379:(a,b,c)=>{"use strict";a.exports=c(53332)},57841:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(76431),e=c(98150),f=c(29243),g=c(43574);b.has=function(a,b){let c;if(0===(c=Array.isArray(b)?b:"string"==typeof b&&d.isDeepKey(b)&&a?.[b]==null?g.toPath(b):[b]).length)return!1;let h=a;for(let a=0;a<c.length;a++){let b=c[a];if((null==h||!Object.hasOwn(h,b))&&!((Array.isArray(h)||f.isArguments(h))&&e.isIndex(b)&&b<h.length))return!1;h=h[b]}return!0}},59138:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(29862);b.cloneDeep=function(a){return d.cloneDeepWith(a)}},59618:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.uniqBy=function(a,b){let c=new Map;for(let d=0;d<a.length;d++){let e=a[d],f=b(e);c.has(f)||c.set(f,e)}return Array.from(c.values())}},60324:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if("object"!=typeof a||null==a)return!1;if(null===Object.getPrototypeOf(a))return!0;if("[object Object]"!==Object.prototype.toString.call(a)){let b=a[Symbol.toStringTag];return null!=b&&!!Object.getOwnPropertyDescriptor(a,Symbol.toStringTag)?.writable&&a.toString()===`[object ${b}]`}let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66777:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(98150),e=c(26349),f=c(1640),g=c(1706);b.isIterateeCall=function(a,b,c){return!!f.isObject(c)&&(!!("number"==typeof b&&e.isArrayLike(c)&&d.isIndex(b))&&b<c.length||"string"==typeof b&&b in c)&&g.eq(c[b],a)}},67766:(a,b,c)=>{a.exports=c(43084).throttle},68163:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,80559)),"C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,63144)),"C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\dashboard\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},69404:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(175),e=c(91653),f=c(91428),g=c(27469),h=c(1706);b.isEqualWith=function(a,b,c){return function a(b,c,i,j,k,l,m){let n=m(b,c,i,j,k,l);if(void 0!==n)return n;if(typeof b==typeof c)switch(typeof b){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return b===c;case"number":return b===c||Object.is(b,c)}return function b(c,i,j,k){if(Object.is(c,i))return!0;let l=f.getTag(c),m=f.getTag(i);if(l===g.argumentsTag&&(l=g.objectTag),m===g.argumentsTag&&(m=g.objectTag),l!==m)return!1;switch(l){case g.stringTag:return c.toString()===i.toString();case g.numberTag:{let a=c.valueOf(),b=i.valueOf();return h.eq(a,b)}case g.booleanTag:case g.dateTag:case g.symbolTag:return Object.is(c.valueOf(),i.valueOf());case g.regexpTag:return c.source===i.source&&c.flags===i.flags;case g.functionTag:return c===i}let n=(j=j??new Map).get(c),o=j.get(i);if(null!=n&&null!=o)return n===i;j.set(c,i),j.set(i,c);try{switch(l){case g.mapTag:if(c.size!==i.size)return!1;for(let[b,d]of c.entries())if(!i.has(b)||!a(d,i.get(b),b,c,i,j,k))return!1;return!0;case g.setTag:{if(c.size!==i.size)return!1;let b=Array.from(c.values()),d=Array.from(i.values());for(let e=0;e<b.length;e++){let f=b[e],g=d.findIndex(b=>a(f,b,void 0,c,i,j,k));if(-1===g)return!1;d.splice(g,1)}return!0}case g.arrayTag:case g.uint8ArrayTag:case g.uint8ClampedArrayTag:case g.uint16ArrayTag:case g.uint32ArrayTag:case g.bigUint64ArrayTag:case g.int8ArrayTag:case g.int16ArrayTag:case g.int32ArrayTag:case g.bigInt64ArrayTag:case g.float32ArrayTag:case g.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(c)!==Buffer.isBuffer(i)||c.length!==i.length)return!1;for(let b=0;b<c.length;b++)if(!a(c[b],i[b],b,c,i,j,k))return!1;return!0;case g.arrayBufferTag:if(c.byteLength!==i.byteLength)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.dataViewTag:if(c.byteLength!==i.byteLength||c.byteOffset!==i.byteOffset)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.errorTag:return c.name===i.name&&c.message===i.message;case g.objectTag:{if(!(b(c.constructor,i.constructor,j,k)||d.isPlainObject(c)&&d.isPlainObject(i)))return!1;let f=[...Object.keys(c),...e.getSymbols(c)],g=[...Object.keys(i),...e.getSymbols(i)];if(f.length!==g.length)return!1;for(let b=0;b<f.length;b++){let d=f[b],e=c[d];if(!Object.hasOwn(i,d))return!1;let g=i[d];if(!a(e,g,d,c,i,j,k))return!1}return!0}default:return!1}}finally{j.delete(c),j.delete(i)}}(b,c,l,m)}(a,b,void 0,void 0,void 0,void 0,c)}},71337:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(66777),e=c(42750);b.range=function(a,b,c){c&&"number"!=typeof c&&d.isIterateeCall(a,b,c)&&(b=c=void 0),a=e.toFinite(a),void 0===b?(b=a,a=0):b=e.toFinite(b),c=void 0===c?a<b?1:-1:e.toFinite(c);let f=Math.max(Math.ceil((b-a)/(c||1)),0),g=Array(f);for(let b=0;b<f;b++)g[b]=a,a+=c;return g}},74838:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(42066),e=c(52371);b.matches=function(a){return a=e.cloneDeep(a),b=>d.isMatch(b,a)}},75446:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(37586),e=c(28382),f=c(66777);b.sortBy=function(a,...b){let c=b.length;return c>1&&f.isIterateeCall(a,b[0],b[1])?b=[]:c>2&&f.isIterateeCall(b[0],b[1],b[2])&&(b=[b[0]]),d.orderBy(a,e.flatten(b),["asc"])}},76021:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(95819),e=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,f=/^\w*$/;b.isKey=function(a,b){return!Array.isArray(a)&&(!!("number"==typeof a||"boolean"==typeof a||null==a||d.isSymbol(a))||"string"==typeof a&&(f.test(a)||!e.test(a))||null!=b&&Object.hasOwn(b,a))}},76431:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isDeepKey=function(a){switch(typeof a){case"number":case"symbol":return!1;case"string":return a.includes(".")||a.includes("[")||a.includes("]")}}},80559:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\project\\\\leads\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\dashboard\\page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87509:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(35314),e=c(76431),f=c(30657),g=c(43574);b.get=function a(b,c,h){if(null==b)return h;switch(typeof c){case"string":{if(d.isUnsafeProperty(c))return h;let f=b[c];if(void 0===f)if(e.isDeepKey(c))return a(b,g.toPath(c),h);else return h;return f}case"number":case"symbol":{"number"==typeof c&&(c=f.toKey(c));let a=b[c];if(void 0===a)return h;return a}default:{if(Array.isArray(c)){var i=b,j=c,k=h;if(0===j.length)return k;let a=i;for(let b=0;b<j.length;b++){if(null==a||d.isUnsafeProperty(j[b]))return k;a=a[j[b]]}return void 0===a?k:a}if(c=Object.is(c?.valueOf(),-0)?"-0":String(c),d.isUnsafeProperty(c))return h;let a=b[c];if(void 0===a)return h;return a}}}},90015:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.noop=function(){}},90830:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(26349),e=c(49899);b.isArrayLikeObject=function(a){return e.isObjectLike(a)&&d.isArrayLike(a)}},91428:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getTag=function(a){return null==a?void 0===a?"[object Undefined]":"[object Null]":Object.prototype.toString.call(a)}},91653:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getSymbols=function(a){return Object.getOwnPropertySymbols(a).filter(b=>Object.prototype.propertyIsEnumerable.call(a,b))}},92292:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(69404),e=c(90015);b.isEqual=function(a,b){return d.isEqualWith(a,b,e.noop)}},92681:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.identity=function(a){return a}},92867:(a,b,c)=>{a.exports=c(60324).isPlainObject},92923:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(91653),e=c(91428),f=c(27469),g=c(23457),h=c(21251);function i(a,b,c,d=new Map,k){let l=k?.(a,b,c,d);if(null!=l)return l;if(g.isPrimitive(a))return a;if(d.has(a))return d.get(a);if(Array.isArray(a)){let b=Array(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return Object.hasOwn(a,"index")&&(b.index=a.index),Object.hasOwn(a,"input")&&(b.input=a.input),b}if(a instanceof Date)return new Date(a.getTime());if(a instanceof RegExp){let b=new RegExp(a.source,a.flags);return b.lastIndex=a.lastIndex,b}if(a instanceof Map){let b=new Map;for(let[e,f]of(d.set(a,b),a))b.set(e,i(f,e,c,d,k));return b}if(a instanceof Set){let b=new Set;for(let e of(d.set(a,b),a))b.add(i(e,void 0,c,d,k));return b}if("undefined"!=typeof Buffer&&Buffer.isBuffer(a))return a.subarray();if(h.isTypedArray(a)){let b=new(Object.getPrototypeOf(a)).constructor(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return b}if(a instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&a instanceof SharedArrayBuffer)return a.slice(0);if(a instanceof DataView){let b=new DataView(a.buffer.slice(0),a.byteOffset,a.byteLength);return d.set(a,b),j(b,a,c,d,k),b}if("undefined"!=typeof File&&a instanceof File){let b=new File([a],a.name,{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Blob){let b=new Blob([a],{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Error){let b=new a.constructor;return d.set(a,b),b.message=a.message,b.name=a.name,b.stack=a.stack,b.cause=a.cause,j(b,a,c,d,k),b}if("object"==typeof a&&function(a){switch(e.getTag(a)){case f.argumentsTag:case f.arrayTag:case f.arrayBufferTag:case f.dataViewTag:case f.booleanTag:case f.dateTag:case f.float32ArrayTag:case f.float64ArrayTag:case f.int8ArrayTag:case f.int16ArrayTag:case f.int32ArrayTag:case f.mapTag:case f.numberTag:case f.objectTag:case f.regexpTag:case f.setTag:case f.stringTag:case f.symbolTag:case f.uint8ArrayTag:case f.uint8ClampedArrayTag:case f.uint16ArrayTag:case f.uint32ArrayTag:return!0;default:return!1}}(a)){let b=Object.create(Object.getPrototypeOf(a));return d.set(a,b),j(b,a,c,d,k),b}return a}function j(a,b,c=a,e,f){let g=[...Object.keys(b),...d.getSymbols(b)];for(let d=0;d<g.length;d++){let h=g[d],j=Object.getOwnPropertyDescriptor(a,h);(null==j||j.writable)&&(a[h]=i(b[h],h,c,e,f))}}b.cloneDeepWith=function(a,b){return i(a,void 0,a,new Map,b)},b.cloneDeepWithImpl=i,b.copyProperties=j},95819:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isSymbol=function(a){return"symbol"==typeof a||a instanceof Symbol}},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(4780);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},97668:(a,b)=>{"use strict";var c="function"==typeof Symbol&&Symbol.for,d=c?Symbol.for("react.element"):60103,e=c?Symbol.for("react.portal"):60106,f=c?Symbol.for("react.fragment"):60107,g=c?Symbol.for("react.strict_mode"):60108,h=c?Symbol.for("react.profiler"):60114,i=c?Symbol.for("react.provider"):60109,j=c?Symbol.for("react.context"):60110,k=c?Symbol.for("react.async_mode"):60111,l=c?Symbol.for("react.concurrent_mode"):60111,m=c?Symbol.for("react.forward_ref"):60112,n=c?Symbol.for("react.suspense"):60113,o=(c&&Symbol.for("react.suspense_list"),c?Symbol.for("react.memo"):60115),p=c?Symbol.for("react.lazy"):60116;function q(a){if("object"==typeof a&&null!==a){var b=a.$$typeof;switch(b){case d:switch(a=a.type){case k:case l:case f:case h:case g:case n:return a;default:switch(a=a&&a.$$typeof){case j:case m:case p:case o:case i:return a;default:return b}}case e:return b}}}c&&Symbol.for("react.block"),c&&Symbol.for("react.fundamental"),c&&Symbol.for("react.responder"),c&&Symbol.for("react.scope");b.isFragment=function(a){return q(a)===f}},97766:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.last=function(a){return a[a.length-1]}},98150:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let c=/^(?:0|[1-9]\d*)$/;b.isIndex=function(a,b=Number.MAX_SAFE_INTEGER){switch(typeof a){case"number":return Number.isInteger(a)&&a>=0&&a<b;case"symbol":return!1;case"string":return c.test(a)}}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,647,984,394],()=>b(b.s=68163));module.exports=c})();