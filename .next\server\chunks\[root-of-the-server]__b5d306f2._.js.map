{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n}\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload\n  } catch {\n    return null\n  }\n}\n\nexport function getTokenFromRequest(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization')\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7)\n  }\n  \n  // Also check for token in cookies\n  const token = request.cookies.get('auth-token')?.value\n  return token || null\n}\n\nexport function getUserFromRequest(request: NextRequest): JWTPayload | null {\n  const token = getTokenFromRequest(request)\n  console.log('Auth check:', {\n    hasToken: !!token,\n    tokenLength: token?.length,\n    cookies: request.cookies.getAll().map(c => ({ name: c.name, hasValue: !!c.value }))\n  })\n\n  if (!token) return null\n\n  const user = verifyToken(token)\n  console.log('Token verification:', { isValid: !!user, userId: user?.userId })\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAOtC,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,oBAAoB,OAAoB;IACtD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,kCAAkC;IAClC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,SAAS,mBAAmB,OAAoB;IACrD,MAAM,QAAQ,oBAAoB;IAClC,QAAQ,GAAG,CAAC,eAAe;QACzB,UAAU,CAAC,CAAC;QACZ,aAAa,OAAO;QACpB,SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,MAAM,EAAE,IAAI;gBAAE,UAAU,CAAC,CAAC,EAAE,KAAK;YAAC,CAAC;IACnF;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,OAAO,YAAY;IACzB,QAAQ,GAAG,CAAC,uBAAuB;QAAE,SAAS,CAAC,CAAC;QAAM,QAAQ,MAAM;IAAO;IAE3E,OAAO;AACT", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/app/api/test-auth/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getUserFromRequest } from '@/lib/auth'\n\nexport async function GET(request: NextRequest) {\n  const user = getUserFromRequest(request)\n  \n  return NextResponse.json({\n    authenticated: !!user,\n    user: user || null,\n    cookies: request.cookies.getAll(),\n    headers: {\n      authorization: request.headers.get('authorization'),\n      cookie: request.headers.get('cookie'),\n    }\n  })\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;IAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,eAAe,CAAC,CAAC;QACjB,MAAM,QAAQ;QACd,SAAS,QAAQ,OAAO,CAAC,MAAM;QAC/B,SAAS;YACP,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;YACnC,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC;QAC9B;IACF;AACF", "debugId": null}}]}