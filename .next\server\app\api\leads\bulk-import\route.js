(()=>{var a={};a.id=224,a.ids=[224],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(a,b,c)=>{"use strict";c.d(b,{BE:()=>i,Er:()=>h,HU:()=>j,rL:()=>k});var d=c(85663),e=c(43205),f=c.n(e);let g=process.env.JWT_SECRET||"fallback-secret-key";async function h(a){return d.Ay.hash(a,12)}async function i(a,b){return d.Ay.compare(a,b)}function j(a){return f().sign(a,g,{expiresIn:"7d"})}function k(a){let b=function(a){let b=a.headers.get("authorization");return b&&b.startsWith("Bearer ")?b.substring(7):a.cookies.get("auth-token")?.value||null}(a);if(!b)return null;try{return f().verify(b,g)}catch{return null}}},23928:function(a,b,c){var d,e;void 0===(e="function"==typeof(d=function a(){"use strict";var b="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==b?b:{},d=!b.document&&!!b.postMessage,e=b.IS_PAPA_WORKER||!1,f={},g=0,h={};if(h.parse=function(c,d){var e,i=(d=d||{}).dynamicTyping||!1;if(x(i)&&(d.dynamicTypingFunction=i,i={}),d.dynamicTyping=i,d.transform=!!x(d.transform)&&d.transform,d.worker&&h.WORKERS_SUPPORTED){var j=function(){if(!h.WORKERS_SUPPORTED)return!1;var c,d,e=(c=b.URL||b.webkitURL||null,d=a.toString(),h.BLOB_URL||(h.BLOB_URL=c.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ","(",d,")();"],{type:"text/javascript"})))),i=new b.Worker(e);return i.onmessage=s,i.id=g++,f[i.id]=i,i}();j.userStep=d.step,j.userChunk=d.chunk,j.userComplete=d.complete,j.userError=d.error,d.step=x(d.step),d.chunk=x(d.chunk),d.complete=x(d.complete),d.error=x(d.error),delete d.worker,j.postMessage({input:c,config:d,workerId:j.id});return}var p=null;return c===h.NODE_STREAM_INPUT&&"undefined"==typeof PAPA_BROWSER_CONTEXT?(p=new o(d)).getStream():("string"==typeof c?(c=65279===(e=c).charCodeAt(0)?e.slice(1):e,p=d.download?new k(d):new m(d)):!0===c.readable&&x(c.read)&&x(c.on)?p=new n(d):(b.File&&c instanceof File||c instanceof Object)&&(p=new l(d)),p.stream(c))},h.unparse=function(a,b){var c=!1,d=!0,e=",",f="\r\n",g='"',i=g+g,j=!1,k=null,l=!1;if("object"==typeof b){if("string"!=typeof b.delimiter||h.BAD_DELIMITERS.filter(function(a){return -1!==b.delimiter.indexOf(a)}).length||(e=b.delimiter),("boolean"==typeof b.quotes||"function"==typeof b.quotes||Array.isArray(b.quotes))&&(c=b.quotes),("boolean"==typeof b.skipEmptyLines||"string"==typeof b.skipEmptyLines)&&(j=b.skipEmptyLines),"string"==typeof b.newline&&(f=b.newline),"string"==typeof b.quoteChar&&(g=b.quoteChar),"boolean"==typeof b.header&&(d=b.header),Array.isArray(b.columns)){if(0===b.columns.length)throw Error("Option columns is empty");k=b.columns}void 0!==b.escapeChar&&(i=b.escapeChar+g),b.escapeFormulae instanceof RegExp?l=b.escapeFormulae:"boolean"==typeof b.escapeFormulae&&b.escapeFormulae&&(l=/^[=+\-@\t\r].*$/)}var m=RegExp(q(g),"g");if("string"==typeof a&&(a=JSON.parse(a)),Array.isArray(a)){if(!a.length||Array.isArray(a[0]))return n(null,a,j);else if("object"==typeof a[0])return n(k||Object.keys(a[0]),a,j)}else if("object"==typeof a)return"string"==typeof a.data&&(a.data=JSON.parse(a.data)),Array.isArray(a.data)&&(a.fields||(a.fields=a.meta&&a.meta.fields||k),a.fields||(a.fields=Array.isArray(a.data[0])?a.fields:"object"==typeof a.data[0]?Object.keys(a.data[0]):[]),Array.isArray(a.data[0])||"object"==typeof a.data[0]||(a.data=[a.data])),n(a.fields||[],a.data||[],j);throw Error("Unable to serialize unrecognized input");function n(a,b,c){var g="";"string"==typeof a&&(a=JSON.parse(a)),"string"==typeof b&&(b=JSON.parse(b));var h=Array.isArray(a)&&a.length>0,i=!Array.isArray(b[0]);if(h&&d){for(var j=0;j<a.length;j++)j>0&&(g+=e),g+=o(a[j],j);b.length>0&&(g+=f)}for(var k=0;k<b.length;k++){var l=h?a.length:b[k].length,m=!1,n=h?0===Object.keys(b[k]).length:0===b[k].length;if(c&&!h&&(m="greedy"===c?""===b[k].join("").trim():1===b[k].length&&0===b[k][0].length),"greedy"===c&&h){for(var p=[],q=0;q<l;q++){var r=i?a[q]:q;p.push(b[k][r])}m=""===p.join("").trim()}if(!m){for(var s=0;s<l;s++){s>0&&!n&&(g+=e);var t=h&&i?a[s]:s;g+=o(b[k][t],s)}k<b.length-1&&(!c||l>0&&!n)&&(g+=f)}}return g}function o(a,b){if(null==a)return"";if(a.constructor===Date)return JSON.stringify(a).slice(1,25);var d=!1;l&&"string"==typeof a&&l.test(a)&&(a="'"+a,d=!0);var f=a.toString().replace(m,i);return(d=d||!0===c||"function"==typeof c&&c(a,b)||Array.isArray(c)&&c[b]||function(a,b){for(var c=0;c<b.length;c++)if(a.indexOf(b[c])>-1)return!0;return!1}(f,h.BAD_DELIMITERS)||f.indexOf(e)>-1||" "===f.charAt(0)||" "===f.charAt(f.length-1))?g+f+g:f}},h.RECORD_SEP="\x1e",h.UNIT_SEP="\x1f",h.BYTE_ORDER_MARK="\uFEFF",h.BAD_DELIMITERS=["\r","\n",'"',h.BYTE_ORDER_MARK],h.WORKERS_SUPPORTED=!d&&!!b.Worker,h.NODE_STREAM_INPUT=1,h.LocalChunkSize=0xa00000,h.RemoteChunkSize=5242880,h.DefaultDelimiter=",",h.Parser=r,h.ParserHandle=p,h.NetworkStreamer=k,h.FileStreamer=l,h.StringStreamer=m,h.ReadableStreamStreamer=n,"undefined"==typeof PAPA_BROWSER_CONTEXT&&(h.DuplexStreamStreamer=o),b.jQuery){var i=b.jQuery;i.fn.parse=function(a){var c=a.config||{},d=[];return this.each(function(a){if(!("INPUT"===i(this).prop("tagName").toUpperCase()&&"file"===i(this).attr("type").toLowerCase()&&b.FileReader)||!this.files||0===this.files.length)return!0;for(var e=0;e<this.files.length;e++)d.push({file:this.files[e],inputElem:this,instanceConfig:i.extend({},c)})}),e(),this;function e(){if(0===d.length){x(a.complete)&&a.complete();return}var b=d[0];if(x(a.before)){var c,e,g,j,k=a.before(b.file,b.inputElem);if("object"==typeof k)if("abort"===k.action){return void(c="AbortError",e=b.file,g=b.inputElem,j=k.reason,x(a.error)&&a.error({name:c},e,g,j))}else{if("skip"===k.action)return void f();"object"==typeof k.config&&(b.instanceConfig=i.extend(b.instanceConfig,k.config))}else if("skip"===k)return void f()}var l=b.instanceConfig.complete;b.instanceConfig.complete=function(a){x(l)&&l(a,b.file,b.inputElem),f()},h.parse(b.file,b.instanceConfig)}function f(){d.splice(0,1),e()}}}function j(a){this._handle=null,this._finished=!1,this._completed=!1,this._halted=!1,this._input=null,this._baseIndex=0,this._partialLine="",this._rowCount=0,this._start=0,this._nextChunk=null,this.isFirstChunk=!0,this._completeResults={data:[],errors:[],meta:{}},(function(a){var b=v(a);b.chunkSize=parseInt(b.chunkSize),a.step||a.chunk||(b.chunkSize=null),this._handle=new p(b),this._handle.streamer=this,this._config=b}).call(this,a),this.parseChunk=function(a,c){let d=parseInt(this._config.skipFirstNLines)||0;if(this.isFirstChunk&&d>0){let b=this._config.newline;if(!b){let c=this._config.quoteChar||'"';b=this._handle.guessLineEndings(a,c)}a=[...a.split(b).slice(d)].join(b)}if(this.isFirstChunk&&x(this._config.beforeFirstChunk)){var f=this._config.beforeFirstChunk(a);void 0!==f&&(a=f)}this.isFirstChunk=!1,this._halted=!1;var g=this._partialLine+a;this._partialLine="";var i=this._handle.parse(g,this._baseIndex,!this._finished);if(this._handle.paused()||this._handle.aborted()){this._halted=!0;return}var j=i.meta.cursor;this._finished||(this._partialLine=g.substring(j-this._baseIndex),this._baseIndex=j),i&&i.data&&(this._rowCount+=i.data.length);var k=this._finished||this._config.preview&&this._rowCount>=this._config.preview;if(e)b.postMessage({results:i,workerId:h.WORKER_ID,finished:k});else if(x(this._config.chunk)&&!c){if(this._config.chunk(i,this._handle),this._handle.paused()||this._handle.aborted()){this._halted=!0;return}i=void 0,this._completeResults=void 0}return this._config.step||this._config.chunk||(this._completeResults.data=this._completeResults.data.concat(i.data),this._completeResults.errors=this._completeResults.errors.concat(i.errors),this._completeResults.meta=i.meta),!this._completed&&k&&x(this._config.complete)&&(!i||!i.meta.aborted)&&(this._config.complete(this._completeResults,this._input),this._completed=!0),k||i&&i.meta.paused||this._nextChunk(),i},this._sendError=function(a){x(this._config.error)?this._config.error(a):e&&this._config.error&&b.postMessage({workerId:h.WORKER_ID,error:a,finished:!1})}}function k(a){var b;(a=a||{}).chunkSize||(a.chunkSize=h.RemoteChunkSize),j.call(this,a),d?this._nextChunk=function(){this._readChunk(),this._chunkLoaded()}:this._nextChunk=function(){this._readChunk()},this.stream=function(a){this._input=a,this._nextChunk()},this._readChunk=function(){if(this._finished)return void this._chunkLoaded();if(b=new XMLHttpRequest,this._config.withCredentials&&(b.withCredentials=this._config.withCredentials),d||(b.onload=w(this._chunkLoaded,this),b.onerror=w(this._chunkError,this)),b.open(this._config.downloadRequestBody?"POST":"GET",this._input,!d),this._config.downloadRequestHeaders){var a=this._config.downloadRequestHeaders;for(var c in a)b.setRequestHeader(c,a[c])}if(this._config.chunkSize){var e=this._start+this._config.chunkSize-1;b.setRequestHeader("Range","bytes="+this._start+"-"+e)}try{b.send(this._config.downloadRequestBody)}catch(a){this._chunkError(a.message)}d&&0===b.status&&this._chunkError()},this._chunkLoaded=function(){if(4===b.readyState){var a;if(b.status<200||b.status>=400)return void this._chunkError();this._start+=this._config.chunkSize?this._config.chunkSize:b.responseText.length,this._finished=!this._config.chunkSize||this._start>=(null===(a=b.getResponseHeader("Content-Range"))?-1:parseInt(a.substring(a.lastIndexOf("/")+1))),this.parseChunk(b.responseText)}},this._chunkError=function(a){var c=b.statusText||a;this._sendError(Error(c))}}function l(a){(a=a||{}).chunkSize||(a.chunkSize=h.LocalChunkSize),j.call(this,a);var b,c,d="undefined"!=typeof FileReader;this.stream=function(a){this._input=a,c=a.slice||a.webkitSlice||a.mozSlice,d?((b=new FileReader).onload=w(this._chunkLoaded,this),b.onerror=w(this._chunkError,this)):b=new FileReaderSync,this._nextChunk()},this._nextChunk=function(){this._finished||this._config.preview&&!(this._rowCount<this._config.preview)||this._readChunk()},this._readChunk=function(){var a=this._input;if(this._config.chunkSize){var e=Math.min(this._start+this._config.chunkSize,this._input.size);a=c.call(a,this._start,e)}var f=b.readAsText(a,this._config.encoding);d||this._chunkLoaded({target:{result:f}})},this._chunkLoaded=function(a){this._start+=this._config.chunkSize,this._finished=!this._config.chunkSize||this._start>=this._input.size,this.parseChunk(a.target.result)},this._chunkError=function(){this._sendError(b.error)}}function m(a){var b;a=a||{},j.call(this,a),this.stream=function(a){return b=a,this._nextChunk()},this._nextChunk=function(){if(!this._finished){var a,c=this._config.chunkSize;return c?(a=b.substring(0,c),b=b.substring(c)):(a=b,b=""),this._finished=!b,this.parseChunk(a)}}}function n(a){a=a||{},j.call(this,a);var b=[],c=!0,d=!1;this.pause=function(){j.prototype.pause.apply(this,arguments),this._input.pause()},this.resume=function(){j.prototype.resume.apply(this,arguments),this._input.resume()},this.stream=function(a){this._input=a,this._input.on("data",this._streamData),this._input.on("end",this._streamEnd),this._input.on("error",this._streamError)},this._checkIsFinished=function(){d&&1===b.length&&(this._finished=!0)},this._nextChunk=function(){this._checkIsFinished(),b.length?this.parseChunk(b.shift()):c=!0},this._streamData=w(function(a){try{b.push("string"==typeof a?a:a.toString(this._config.encoding)),c&&(c=!1,this._checkIsFinished(),this.parseChunk(b.shift()))}catch(a){this._streamError(a)}},this),this._streamError=w(function(a){this._streamCleanUp(),this._sendError(a)},this),this._streamEnd=w(function(){this._streamCleanUp(),d=!0,this._streamData("")},this),this._streamCleanUp=w(function(){this._input.removeListener("data",this._streamData),this._input.removeListener("end",this._streamEnd),this._input.removeListener("error",this._streamError)},this)}function o(a){var b=c(27910).Duplex,d=v(a),e=!0,f=!1,g=[],h=null;this._onCsvData=function(a){var b=a.data;h.push(b)||this._handle.paused()||this._handle.pause()},this._onCsvComplete=function(){h.push(null)},d.step=w(this._onCsvData,this),d.complete=w(this._onCsvComplete,this),j.call(this,d),this._nextChunk=function(){f&&1===g.length&&(this._finished=!0),g.length?g.shift()():e=!0},this._addToParseQueue=function(a,b){g.push(w(function(){if(this.parseChunk("string"==typeof a?a:a.toString(d.encoding)),x(b))return b()},this)),e&&(e=!1,this._nextChunk())},this._onRead=function(){this._handle.paused()&&this._handle.resume()},this._onWrite=function(a,b,c){this._addToParseQueue(a,c)},this._onWriteComplete=function(){f=!0,this._addToParseQueue("")},this.getStream=function(){return h},(h=new b({readableObjectMode:!0,decodeStrings:!1,read:w(this._onRead,this),write:w(this._onWrite,this)})).once("finish",w(this._onWriteComplete,this))}function p(a){var b,c,d,e=/^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,f=/^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/,g=this,i=0,j=0,k=!1,l=!1,m=[],n={data:[],errors:[],meta:{}};if(x(a.step)){var o=a.step;a.step=function(b){if(n=b,t())s();else{if(s(),0===n.data.length)return;i+=b.data.length,a.preview&&i>a.preview?c.abort():(n.data=n.data[0],o(n,g))}}}function p(b){return"greedy"===a.skipEmptyLines?""===b.join("").trim():1===b.length&&0===b[0].length}function s(){return n&&d&&(u("Delimiter","UndetectableDelimiter","Unable to auto-detect delimiting character; defaulted to '"+h.DefaultDelimiter+"'"),d=!1),a.skipEmptyLines&&(n.data=n.data.filter(function(a){return!p(a)})),t()&&function(){if(n)if(Array.isArray(n.data[0])){for(var b=0;t()&&b<n.data.length;b++)n.data[b].forEach(c);n.data.splice(0,1)}else n.data.forEach(c);function c(b,c){x(a.transformHeader)&&(b=a.transformHeader(b,c)),m.push(b)}}(),function(){if(!n||!a.header&&!a.dynamicTyping&&!a.transform)return n;function b(b,c){var d,g=a.header?{}:[];for(d=0;d<b.length;d++){var h=d,i=b[d];a.header&&(h=d>=m.length?"__parsed_extra":m[d]),a.transform&&(i=a.transform(i,h)),i=function(b,c){if(a.dynamicTypingFunction&&void 0===a.dynamicTyping[b]&&(a.dynamicTyping[b]=a.dynamicTypingFunction(b)),!0===(a.dynamicTyping[b]||a.dynamicTyping))if("true"===c||"TRUE"===c)return!0;else if("false"===c||"FALSE"===c)return!1;else if(function(a){if(e.test(a)){var b=parseFloat(a);if(b>-0x20000000000000&&b<0x20000000000000)return!0}return!1}(c))return parseFloat(c);else if(f.test(c))return new Date(c);else return""===c?null:c;return c}(h,i),"__parsed_extra"===h?(g[h]=g[h]||[],g[h].push(i)):g[h]=i}return a.header&&(d>m.length?u("FieldMismatch","TooManyFields","Too many fields: expected "+m.length+" fields but parsed "+d,j+c):d<m.length&&u("FieldMismatch","TooFewFields","Too few fields: expected "+m.length+" fields but parsed "+d,j+c)),g}var c=1;return!n.data.length||Array.isArray(n.data[0])?(n.data=n.data.map(b),c=n.data.length):n.data=b(n.data,0),a.header&&n.meta&&(n.meta.fields=m),j+=c,n}()}function t(){return a.header&&0===m.length}function u(a,b,c,d){var e={type:a,code:b,message:c};void 0!==d&&(e.row=d),n.errors.push(e)}this.parse=function(e,f,g){var i=a.quoteChar||'"';if(a.newline||(a.newline=this.guessLineEndings(e,i)),d=!1,a.delimiter)x(a.delimiter)&&(a.delimiter=a.delimiter(e),n.meta.delimiter=a.delimiter);else{var j=function(b,c,d,e,f){var g,i,j,k;f=f||[",","	","|",";",h.RECORD_SEP,h.UNIT_SEP];for(var l=0;l<f.length;l++){var m=f[l],n=0,o=0,q=0;j=void 0;for(var s=new r({comments:e,delimiter:m,newline:c,preview:10}).parse(b),t=0;t<s.data.length;t++){if(d&&p(s.data[t])){q++;continue}var u=s.data[t].length;if(o+=u,void 0===j){j=u;continue}u>0&&(n+=Math.abs(u-j),j=u)}s.data.length>0&&(o/=s.data.length-q),(void 0===i||n<=i)&&(void 0===k||o>k)&&o>1.99&&(i=n,g=m,k=o)}return a.delimiter=g,{successful:!!g,bestDelimiter:g}}(e,a.newline,a.skipEmptyLines,a.comments,a.delimitersToGuess);j.successful?a.delimiter=j.bestDelimiter:(d=!0,a.delimiter=h.DefaultDelimiter),n.meta.delimiter=a.delimiter}var l=v(a);return a.preview&&a.header&&l.preview++,b=e,n=(c=new r(l)).parse(b,f,g),s(),k?{meta:{paused:!0}}:n||{meta:{paused:!1}}},this.paused=function(){return k},this.pause=function(){k=!0,c.abort(),b=x(a.chunk)?"":b.substring(c.getCharIndex())},this.resume=function(){g.streamer._halted?(k=!1,g.streamer.parseChunk(b,!0)):setTimeout(g.resume,3)},this.aborted=function(){return l},this.abort=function(){l=!0,c.abort(),n.meta.aborted=!0,x(a.complete)&&a.complete(n),b=""},this.guessLineEndings=function(a,b){a=a.substring(0,1048576);var c=RegExp(q(b)+"([^]*?)"+q(b),"gm"),d=(a=a.replace(c,"")).split("\r"),e=a.split("\n"),f=e.length>1&&e[0].length<d[0].length;if(1===d.length||f)return"\n";for(var g=0,h=0;h<d.length;h++)"\n"===d[h][0]&&g++;return g>=d.length/2?"\r\n":"\r"}}function q(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function r(a){var b,c=(a=a||{}).delimiter,d=a.newline,e=a.comments,f=a.step,g=a.preview,i=a.fastMode,j=null,k=!1,l=b=void 0===a.quoteChar||null===a.quoteChar?'"':a.quoteChar;if(void 0!==a.escapeChar&&(l=a.escapeChar),("string"!=typeof c||h.BAD_DELIMITERS.indexOf(c)>-1)&&(c=","),e===c)throw Error("Comment character same as delimiter");!0===e?e="#":("string"!=typeof e||h.BAD_DELIMITERS.indexOf(e)>-1)&&(e=!1),"\n"!==d&&"\r"!==d&&"\r\n"!==d&&(d="\n");var m=0,n=!1;this.parse=function(h,o,p){if("string"!=typeof h)throw Error("Input must be a string");var r=h.length,s=c.length,t=d.length,u=e.length,v=x(f);m=0;var w=[],y=[],z=[],A=0;if(!h)return N();if(i||!1!==i&&-1===h.indexOf(b)){for(var B=h.split(d),C=0;C<B.length;C++){if(z=B[C],m+=z.length,C!==B.length-1)m+=d.length;else if(p)break;if(!e||z.substring(0,u)!==e){if(v){if(w=[],J(z.split(c)),O(),n)return N()}else J(z.split(c));if(g&&C>=g)return w=w.slice(0,g),N(!0)}}return N()}for(var D=h.indexOf(c,m),E=h.indexOf(d,m),F=RegExp(q(l)+q(b),"g"),G=h.indexOf(b,m);;){if(h[m]===b){for(G=m,m++;;){if(-1===(G=h.indexOf(b,G+1)))return p||y.push({type:"Quotes",code:"MissingQuotes",message:"Quoted field unterminated",row:w.length,index:m}),L();if(G===r-1)return L(h.substring(m,G).replace(F,b));if(b===l&&h[G+1]===l){G++;continue}if(b===l||0===G||h[G-1]!==l){-1!==D&&D<G+1&&(D=h.indexOf(c,G+1)),-1!==E&&E<G+1&&(E=h.indexOf(d,G+1));var H=K(-1===E?D:Math.min(D,E));if(h.substr(G+1+H,s)===c){z.push(h.substring(m,G).replace(F,b)),m=G+1+H+s,h[G+1+H+s]!==b&&(G=h.indexOf(b,m)),D=h.indexOf(c,m),E=h.indexOf(d,m);break}var I=K(E);if(h.substring(G+1+I,G+1+I+t)===d){if(z.push(h.substring(m,G).replace(F,b)),M(G+1+I+t),D=h.indexOf(c,m),G=h.indexOf(b,m),v&&(O(),n))return N();if(g&&w.length>=g)return N(!0);break}y.push({type:"Quotes",code:"InvalidQuotes",message:"Trailing quote on quoted field is malformed",row:w.length,index:m}),G++;continue}}continue}if(e&&0===z.length&&h.substring(m,m+u)===e){if(-1===E)return N();m=E+t,E=h.indexOf(d,m),D=h.indexOf(c,m);continue}if(-1!==D&&(D<E||-1===E)){z.push(h.substring(m,D)),m=D+s,D=h.indexOf(c,m);continue}if(-1!==E){if(z.push(h.substring(m,E)),M(E+t),v&&(O(),n))return N();if(g&&w.length>=g)return N(!0);continue}break}return L();function J(a){w.push(a),A=m}function K(a){var b=0;if(-1!==a){var c=h.substring(G+1,a);c&&""===c.trim()&&(b=c.length)}return b}function L(a){return p||(void 0===a&&(a=h.substring(m)),z.push(a),m=r,J(z),v&&O()),N()}function M(a){m=a,J(z),z=[],E=h.indexOf(d,m)}function N(b){if(a.header&&!o&&w.length&&!k){let b=w[0],c=Object.create(null),d=new Set(b),e=!1;for(let f=0;f<b.length;f++){let g=b[f];if(x(a.transformHeader)&&(g=a.transformHeader(g,f)),c[g]){let a,h=c[g];do a=`${g}_${h}`,h++;while(d.has(a));d.add(a),b[f]=a,c[g]++,e=!0,null===j&&(j={}),j[a]=g}else c[g]=1,b[f]=g;d.add(g)}e&&console.warn("Duplicate headers found and renamed."),k=!0}return{data:w,errors:y,meta:{delimiter:c,linebreak:d,aborted:n,truncated:!!b,cursor:A+(o||0),renamedHeaders:j}}}function O(){f(N()),w=[],y=[]}},this.abort=function(){n=!0},this.getCharIndex=function(){return m}}function s(a){var b=a.data,c=f[b.workerId],d=!1;if(b.error)c.userError(b.error,b.file);else if(b.results&&b.results.data){var e={abort:function(){d=!0,t(b.workerId,{data:[],errors:[],meta:{aborted:!0}})},pause:u,resume:u};if(x(c.userStep)){for(var g=0;g<b.results.data.length&&(c.userStep({data:b.results.data[g],errors:b.results.errors,meta:b.results.meta},e),!d);g++);delete b.results}else x(c.userChunk)&&(c.userChunk(b.results,e,b.file),delete b.results)}b.finished&&!d&&t(b.workerId,b.results)}function t(a,b){var c=f[a];x(c.userComplete)&&c.userComplete(b),c.terminate(),delete f[a]}function u(){throw Error("Not implemented.")}function v(a){if("object"!=typeof a||null===a)return a;var b=Array.isArray(a)?[]:{};for(var c in a)b[c]=v(a[c]);return b}function w(a,b){return function(){a.apply(b,arguments)}}function x(a){return"function"==typeof a}return e&&(b.onmessage=function(a){var c=a.data;if(void 0===h.WORKER_ID&&c&&(h.WORKER_ID=c.workerId),"string"==typeof c.input)b.postMessage({workerId:h.WORKER_ID,results:h.parse(c.input,c.config),finished:!0});else if(b.File&&c.input instanceof File||c.input instanceof Object){var d=h.parse(c.input,c.config);d&&b.postMessage({workerId:h.WORKER_ID,results:d,finished:!0})}}),k.prototype=Object.create(j.prototype),k.prototype.constructor=k,l.prototype=Object.create(j.prototype),l.prototype.constructor=l,m.prototype=Object.create(m.prototype),m.prototype.constructor=m,n.prototype=Object.create(j.prototype),n.prototype.constructor=n,"undefined"==typeof PAPA_BROWSER_CONTEXT&&(o.prototype=Object.create(j.prototype),o.prototype.constructor=o),h})?d.apply(b,[]):d)||(a.exports=e)},25673:(a,b,c)=>{"use strict";let d;c.r(b),c.d(b,{handler:()=>f5,patchFetch:()=>f4,routeModule:()=>f0,serverHooks:()=>f3,workAsyncStorage:()=>f1,workUnitAsyncStorage:()=>f2});var e,f,g,h={};c.r(h),c.d(h,{POST:()=>f_});var i=c(96559),j=c(48088),k=c(37719),l=c(26191),m=c(81289),n=c(261),o=c(92603),p=c(39893),q=c(14823),r=c(47220),s=c(66946),t=c(47912),u=c(99786),v=c(46143),w=c(86439),x=c(43365),y=c(32190),z=c(94747),A=c(12909),B=c(85463),C=c(23928),D=c.n(C),E={};E.version="0.18.5";var F=1200,G=1252,H=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],I={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},J=function(a){-1!=H.indexOf(a)&&(G=I[0]=a)},K=function(a){F=a,J(a)};function L(){K(1200),J(1252)}function M(a){for(var b=[],c=0,d=a.length;c<d;++c)b[c]=a.charCodeAt(c);return b}function N(a){for(var b=[],c=0;c<a.length>>1;++c)b[c]=String.fromCharCode(a.charCodeAt(2*c+1)+(a.charCodeAt(2*c)<<8));return b.join("")}var O=function(a){var b=a.charCodeAt(0),c=a.charCodeAt(1);if(255==b&&254==c){for(var d=a.slice(2),e=[],f=0;f<d.length>>1;++f)e[f]=String.fromCharCode(d.charCodeAt(2*f)+(d.charCodeAt(2*f+1)<<8));return e.join("")}return 254==b&&255==c?N(a.slice(2)):65279==b?a.slice(1):a},P=function(a){return String.fromCharCode(a)},Q=function(a){return String.fromCharCode(a)},R="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function S(a){for(var b="",c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;j<a.length;)f=(c=a.charCodeAt(j++))>>2,g=(3&c)<<4|(d=a.charCodeAt(j++))>>4,h=(15&d)<<2|(e=a.charCodeAt(j++))>>6,i=63&e,isNaN(d)?h=i=64:isNaN(e)&&(i=64),b+=R.charAt(f)+R.charAt(g)+R.charAt(h)+R.charAt(i);return b}function T(a){var b="",c=0,d=0,e=0,f=0,g=0,h=0;a=a.replace(/[^\w\+\/\=]/g,"");for(var i=0;i<a.length;)b+=String.fromCharCode((e=R.indexOf(a.charAt(i++)))<<2|(f=R.indexOf(a.charAt(i++)))>>4),c=(15&f)<<4|(g=R.indexOf(a.charAt(i++)))>>2,64!==g&&(b+=String.fromCharCode(c)),d=(3&g)<<6|(h=R.indexOf(a.charAt(i++))),64!==h&&(b+=String.fromCharCode(d));return b}var U="undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node,V=function(){if("undefined"!=typeof Buffer){var a=!Buffer.from;if(!a)try{Buffer.from("foo","utf8")}catch(b){a=!0}return a?function(a,b){return b?new Buffer(a,b):new Buffer(a)}:Buffer.from.bind(Buffer)}return function(){}}();function W(a){return U?Buffer.alloc?Buffer.alloc(a):new Buffer(a):"undefined"!=typeof Uint8Array?new Uint8Array(a):Array(a)}function X(a){return U?Buffer.allocUnsafe?Buffer.allocUnsafe(a):new Buffer(a):"undefined"!=typeof Uint8Array?new Uint8Array(a):Array(a)}var Y=function(a){return U?V(a,"binary"):a.split("").map(function(a){return 255&a.charCodeAt(0)})};function Z(a){if(Array.isArray(a))return a.map(function(a){return String.fromCharCode(a)}).join("");for(var b=[],c=0;c<a.length;++c)b[c]=String.fromCharCode(a[c]);return b.join("")}function _(a){if("undefined"==typeof ArrayBuffer)throw Error("Unsupported");if(a instanceof ArrayBuffer)return _(new Uint8Array(a));for(var b=Array(a.length),c=0;c<a.length;++c)b[c]=a[c];return b}var aa=U?function(a){return Buffer.concat(a.map(function(a){return Buffer.isBuffer(a)?a:V(a)}))}:function(a){if("undefined"!=typeof Uint8Array){var b=0,c=0;for(b=0;b<a.length;++b)c+=a[b].length;var d=new Uint8Array(c),e=0;for(b=0,c=0;b<a.length;c+=e,++b)if(e=a[b].length,a[b]instanceof Uint8Array)d.set(a[b],c);else if("string"==typeof a[b])throw"wtf";else d.set(new Uint8Array(a[b]),c);return d}return[].concat.apply([],a.map(function(a){return Array.isArray(a)?a:[].slice.call(a)}))},ab=/\u0000/g,ac=/[\u0001-\u0006]/g;function ad(a){for(var b="",c=a.length-1;c>=0;)b+=a.charAt(c--);return b}function ae(a,b){var c=""+a;return c.length>=b?c:a4("0",b-c.length)+c}function af(a,b){var c=""+a;return c.length>=b?c:a4(" ",b-c.length)+c}function ag(a,b){var c=""+a;return c.length>=b?c:c+a4(" ",b-c.length)}function ah(a,b){var c,d;return a>0x100000000||a<-0x100000000?(c=""+Math.round(a)).length>=b?c:a4("0",b-c.length)+c:(d=""+Math.round(a)).length>=b?d:a4("0",b-d.length)+d}function ai(a,b){return b=b||0,a.length>=7+b&&(32|a.charCodeAt(b))==103&&(32|a.charCodeAt(b+1))==101&&(32|a.charCodeAt(b+2))==110&&(32|a.charCodeAt(b+3))==101&&(32|a.charCodeAt(b+4))==114&&(32|a.charCodeAt(b+5))==97&&(32|a.charCodeAt(b+6))==108}var aj=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],ak=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]],al={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},am={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},an={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function ao(a,b,c){for(var d=a<0?-1:1,e=a*d,f=0,g=1,h=0,i=1,j=0,k=0,l=Math.floor(e);j<b&&(h=(l=Math.floor(e))*g+f,k=l*j+i,!(e-l<5e-8));)e=1/(e-l),f=g,g=h,i=j,j=k;if(k>b&&(j>b?(k=i,h=f):(k=j,h=g)),!c)return[0,d*h,k];var m=Math.floor(d*h/k);return[m,d*h-m*k,k]}function ap(a,b,c){if(a>2958465||a<0)return null;var d=0|a,e=Math.floor(86400*(a-d)),f=0,g=[],h={D:d,T:e,u:86400*(a-d)-e,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(1e-6>Math.abs(h.u)&&(h.u=0),b&&b.date1904&&(d+=1462),h.u>.9999&&(h.u=0,86400==++e&&(h.T=e=0,++d,++h.D)),60===d)g=c?[1317,10,29]:[1900,2,29],f=3;else if(0===d)g=c?[1317,8,29]:[1900,1,0],f=6;else{d>60&&--d;var i,j,k,l=new Date(1900,0,1);l.setDate(l.getDate()+d-1),g=[l.getFullYear(),l.getMonth()+1,l.getDate()],f=l.getDay(),d<60&&(f=(f+6)%7),c&&(i=l,j=g,j[0]-=581,k=i.getDay(),i<60&&(k=(k+6)%7),f=k)}return h.y=g[0],h.m=g[1],h.d=g[2],h.S=e%60,h.M=(e=Math.floor(e/60))%60,h.H=e=Math.floor(e/60),h.q=f,h}var aq=new Date(1899,11,31,0,0,0),ar=aq.getTime(),as=new Date(1900,2,1,0,0,0);function at(a,b){var c=a.getTime();return b?c-=1262304e5:a>=as&&(c+=864e5),(c-(ar+(a.getTimezoneOffset()-aq.getTimezoneOffset())*6e4))/864e5}function au(a){return -1==a.indexOf(".")?a:a.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function av(a){var b,c,d,e,f,g=Math.floor(Math.log(Math.abs(a))*Math.LOG10E);return g>=-4&&g<=-1?f=a.toPrecision(10+g):9>=Math.abs(g)?(b=a<0?12:11,f=(c=au(a.toFixed(12))).length<=b||(c=a.toPrecision(10)).length<=b?c:a.toExponential(5)):f=10===g?a.toFixed(10).substr(0,12):(d=au(a.toFixed(11))).length>(a<0?12:11)||"0"===d||"-0"===d?a.toPrecision(6):d,au(-1==(e=f.toUpperCase()).indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2"))}function aw(a,b){switch(typeof a){case"string":return a;case"boolean":return a?"TRUE":"FALSE";case"number":return(0|a)===a?a.toString(10):av(a);case"undefined":return"";case"object":if(null==a)return"";if(a instanceof Date)return aL(14,at(a,b&&b.date1904),b)}throw Error("unsupported value in General format: "+a)}function ax(a){if(a.length<=3)return a;for(var b=a.length%3,c=a.substr(0,b);b!=a.length;b+=3)c+=(c.length>0?",":"")+a.substr(b,3);return c}var ay=/%/g,az=/# (\?+)( ?)\/( ?)(\d+)/,aA=/^#*0*\.([0#]+)/,aB=/\).*[0#]/,aC=/\(###\) ###\\?-####/;function aD(a){for(var b,c="",d=0;d!=a.length;++d)switch(b=a.charCodeAt(d)){case 35:break;case 63:c+=" ";break;case 48:c+="0";break;default:c+=String.fromCharCode(b)}return c}function aE(a,b){var c=Math.pow(10,b);return""+Math.round(a*c)/c}function aF(a,b){var c=a-Math.floor(a),d=Math.pow(10,b);return b<(""+Math.round(c*d)).length?0:Math.round(c*d)}function aG(a,b,c){return(0|c)===c?function a(b,c,d){if(40===b.charCodeAt(0)&&!c.match(aB)){var e,f=c.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return d>=0?a("n",f,d):"("+a("n",f,-d)+")"}if(44===c.charCodeAt(c.length-1)){for(var g=c,h=g.length-1;44===g.charCodeAt(h-1);)--h;return aG(b,g.substr(0,h),d/Math.pow(10,3*(g.length-h)))}if(-1!==c.indexOf("%"))return j=(i=c).replace(ay,""),k=i.length-j.length,aG(b,j,d*Math.pow(10,2*k))+a4("%",k);if(-1!==c.indexOf("E"))return function a(b,c){var d,e=b.indexOf("E")-b.indexOf(".")-1;if(b.match(/^#+0.0E\+0$/)){if(0==c)return"0.0E+0";if(c<0)return"-"+a(b,-c);var f=b.indexOf(".");-1===f&&(f=b.indexOf("E"));var g=Math.floor(Math.log(c)*Math.LOG10E)%f;if(g<0&&(g+=f),!(d=(c/Math.pow(10,g)).toPrecision(e+1+(f+g)%f)).match(/[Ee]/)){var h=Math.floor(Math.log(c)*Math.LOG10E);-1===d.indexOf(".")?d=d.charAt(0)+"."+d.substr(1)+"E+"+(h-d.length+g):d+="E+"+(h-g),d=d.replace(/\+-/,"-")}d=d.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(a,b,c,d){return b+c+d.substr(0,(f+g)%f)+"."+d.substr(g)+"E"})}else d=c.toExponential(e);return b.match(/E\+00$/)&&d.match(/e[+-]\d$/)&&(d=d.substr(0,d.length-1)+"0"+d.charAt(d.length-1)),b.match(/E\-/)&&d.match(/e\+/)&&(d=d.replace(/e\+/,"e")),d.replace("e","E")}(c,d);if(36===c.charCodeAt(0))return"$"+a(b,c.substr(" "==c.charAt(1)?2:1),d);var i,j,k,l,m,n,o,p=Math.abs(d),q=d<0?"-":"";if(c.match(/^00+$/))return q+ae(p,c.length);if(c.match(/^[#?]+$/))return l=""+d,0===d&&(l=""),l.length>c.length?l:aD(c.substr(0,c.length-l.length))+l;if(m=c.match(az))return q+(0===p?"":""+p)+a4(" ",(e=m)[1].length+2+e[4].length);if(c.match(/^#+0+$/))return q+ae(p,c.length-c.indexOf("0"));if(m=c.match(aA))return l=(l=(""+d).replace(/^([^\.]+)$/,"$1."+aD(m[1])).replace(/\.$/,"."+aD(m[1]))).replace(/\.(\d*)$/,function(a,b){return"."+b+a4("0",aD(m[1]).length-b.length)}),-1!==c.indexOf("0.")?l:l.replace(/^0\./,".");if(m=(c=c.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return q+(""+p).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,m[1].length?"0.":".");if(m=c.match(/^#{1,3},##0(\.?)$/))return q+ax(""+p);if(m=c.match(/^#,##0\.([#0]*0)$/))return d<0?"-"+a(b,c,-d):ax(""+d)+"."+a4("0",m[1].length);if(m=c.match(/^#,#*,#0/))return a(b,c.replace(/^#,#*,/,""),d);if(m=c.match(/^([0#]+)(\\?-([0#]+))+$/))return l=ad(a(b,c.replace(/[\\-]/g,""),d)),n=0,ad(ad(c.replace(/\\/g,"")).replace(/[0#]/g,function(a){return n<l.length?l.charAt(n++):"0"===a?"0":""}));if(c.match(aC))return"("+(l=a(b,"##########",d)).substr(0,3)+") "+l.substr(3,3)+"-"+l.substr(6);var r="";if(m=c.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return o=ao(p,Math.pow(10,n=Math.min(m[4].length,7))-1,!1),l=""+q," "==(r=aG("n",m[1],o[1])).charAt(r.length-1)&&(r=r.substr(0,r.length-1)+"0"),l+=r+m[2]+"/"+m[3],(r=ag(o[2],n)).length<m[4].length&&(r=aD(m[4].substr(m[4].length-r.length))+r),l+=r;if(m=c.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return q+((o=ao(p,Math.pow(10,n=Math.min(Math.max(m[1].length,m[4].length),7))-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?af(o[1],n)+m[2]+"/"+m[3]+ag(o[2],n):a4(" ",2*n+1+m[2].length+m[3].length));if(m=c.match(/^[#0?]+$/))return(l=""+d,c.length<=l.length)?l:aD(c.substr(0,c.length-l.length))+l;if(m=c.match(/^([#0]+)\.([#0]+)$/)){n=(l=""+d.toFixed(Math.min(m[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var s=c.indexOf(".")-n,t=c.length-l.length-s;return aD(c.substr(0,s)+l+c.substr(c.length-t))}if(m=c.match(/^00,000\.([#0]*0)$/))return d<0?"-"+a(b,c,-d):ax(""+d).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(a){return"00,"+(a.length<3?ae(0,3-a.length):"")+a})+"."+ae(0,m[1].length);switch(c){case"###,###":case"##,###":case"#,###":var u=ax(""+p);return"0"!==u?q+u:"";default:if(c.match(/\.[0#?]*$/))return a(b,c.slice(0,c.lastIndexOf(".")),d)+aD(c.slice(c.lastIndexOf(".")))}throw Error("unsupported format |"+c+"|")}(a,b,c):function a(b,c,d){if(40===b.charCodeAt(0)&&!c.match(aB)){var e,f,g,h,i,j,k=c.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return d>=0?a("n",k,d):"("+a("n",k,-d)+")"}if(44===c.charCodeAt(c.length-1)){for(var l=c,m=l.length-1;44===l.charCodeAt(m-1);)--m;return aG(b,l.substr(0,m),d/Math.pow(10,3*(l.length-m)))}if(-1!==c.indexOf("%"))return o=(n=c).replace(ay,""),p=n.length-o.length,aG(b,o,d*Math.pow(10,2*p))+a4("%",p);if(-1!==c.indexOf("E"))return function a(b,c){var d,e=b.indexOf("E")-b.indexOf(".")-1;if(b.match(/^#+0.0E\+0$/)){if(0==c)return"0.0E+0";if(c<0)return"-"+a(b,-c);var f=b.indexOf(".");-1===f&&(f=b.indexOf("E"));var g=Math.floor(Math.log(c)*Math.LOG10E)%f;if(g<0&&(g+=f),-1===(d=(c/Math.pow(10,g)).toPrecision(e+1+(f+g)%f)).indexOf("e")){var h=Math.floor(Math.log(c)*Math.LOG10E);for(-1===d.indexOf(".")?d=d.charAt(0)+"."+d.substr(1)+"E+"+(h-d.length+g):d+="E+"+(h-g);"0."===d.substr(0,2);)d=(d=d.charAt(0)+d.substr(2,f)+"."+d.substr(2+f)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");d=d.replace(/\+-/,"-")}d=d.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(a,b,c,d){return b+c+d.substr(0,(f+g)%f)+"."+d.substr(g)+"E"})}else d=c.toExponential(e);return b.match(/E\+00$/)&&d.match(/e[+-]\d$/)&&(d=d.substr(0,d.length-1)+"0"+d.charAt(d.length-1)),b.match(/E\-/)&&d.match(/e\+/)&&(d=d.replace(/e\+/,"e")),d.replace("e","E")}(c,d);if(36===c.charCodeAt(0))return"$"+a(b,c.substr(" "==c.charAt(1)?2:1),d);var n,o,p,q,r,s,t,u=Math.abs(d),v=d<0?"-":"";if(c.match(/^00+$/))return v+ah(u,c.length);if(c.match(/^[#?]+$/))return"0"===(q=ah(d,0))&&(q=""),q.length>c.length?q:aD(c.substr(0,c.length-q.length))+q;if(r=c.match(az))return h=Math.floor((g=Math.round(u*(f=parseInt((e=r)[4],10))))/f),i=g-h*f,v+(0===h?"":""+h)+" "+(0===i?a4(" ",e[1].length+1+e[4].length):af(i,e[1].length)+e[2]+"/"+e[3]+ae(f,e[4].length));if(c.match(/^#+0+$/))return v+ah(u,c.length-c.indexOf("0"));if(r=c.match(aA))return q=aE(d,r[1].length).replace(/^([^\.]+)$/,"$1."+aD(r[1])).replace(/\.$/,"."+aD(r[1])).replace(/\.(\d*)$/,function(a,b){return"."+b+a4("0",aD(r[1]).length-b.length)}),-1!==c.indexOf("0.")?q:q.replace(/^0\./,".");if(r=(c=c.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return v+aE(u,r[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,r[1].length?"0.":".");if(r=c.match(/^#{1,3},##0(\.?)$/))return v+ax(ah(u,0));if(r=c.match(/^#,##0\.([#0]*0)$/))return d<0?"-"+a(b,c,-d):ax(""+(Math.floor(d)+ +((j=r[1].length)<(""+Math.round((d-Math.floor(d))*Math.pow(10,j))).length)))+"."+ae(aF(d,r[1].length),r[1].length);if(r=c.match(/^#,#*,#0/))return a(b,c.replace(/^#,#*,/,""),d);if(r=c.match(/^([0#]+)(\\?-([0#]+))+$/))return q=ad(a(b,c.replace(/[\\-]/g,""),d)),s=0,ad(ad(c.replace(/\\/g,"")).replace(/[0#]/g,function(a){return s<q.length?q.charAt(s++):"0"===a?"0":""}));if(c.match(aC))return"("+(q=a(b,"##########",d)).substr(0,3)+") "+q.substr(3,3)+"-"+q.substr(6);var w="";if(r=c.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return t=ao(u,Math.pow(10,s=Math.min(r[4].length,7))-1,!1),q=""+v," "==(w=aG("n",r[1],t[1])).charAt(w.length-1)&&(w=w.substr(0,w.length-1)+"0"),q+=w+r[2]+"/"+r[3],(w=ag(t[2],s)).length<r[4].length&&(w=aD(r[4].substr(r[4].length-w.length))+w),q+=w;if(r=c.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return v+((t=ao(u,Math.pow(10,s=Math.min(Math.max(r[1].length,r[4].length),7))-1,!0))[0]||(t[1]?"":"0"))+" "+(t[1]?af(t[1],s)+r[2]+"/"+r[3]+ag(t[2],s):a4(" ",2*s+1+r[2].length+r[3].length));if(r=c.match(/^[#0?]+$/))return(q=ah(d,0),c.length<=q.length)?q:aD(c.substr(0,c.length-q.length))+q;if(r=c.match(/^([#0?]+)\.([#0]+)$/)){s=(q=""+d.toFixed(Math.min(r[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var x=c.indexOf(".")-s,y=c.length-q.length-x;return aD(c.substr(0,x)+q+c.substr(c.length-y))}if(r=c.match(/^00,000\.([#0]*0)$/))return s=aF(d,r[1].length),d<0?"-"+a(b,c,-d):ax(d<0x7fffffff&&d>-0x80000000?""+(d>=0?0|d:d-1|0):""+Math.floor(d)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(a){return"00,"+(a.length<3?ae(0,3-a.length):"")+a})+"."+ae(s,r[1].length);switch(c){case"###,##0.00":return a(b,"#,##0.00",d);case"###,###":case"##,###":case"#,###":var z=ax(ah(u,0));return"0"!==z?v+z:"";case"###,###.00":return a(b,"###,##0.00",d).replace(/^0\./,".");case"#,###.00":return a(b,"#,##0.00",d).replace(/^0\./,".")}throw Error("unsupported format |"+c+"|")}(a,b,c)}var aH=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function aI(a){for(var b=0,c="",d="";b<a.length;)switch(c=a.charAt(b)){case"G":ai(a,b)&&(b+=6),b++;break;case'"':for(;34!==a.charCodeAt(++b)&&b<a.length;);++b;break;case"\\":case"_":b+=2;break;case"@":++b;break;case"B":case"b":if("1"===a.charAt(b+1)||"2"===a.charAt(b+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===a.substr(b,3).toUpperCase()||"AM/PM"===a.substr(b,5).toUpperCase()||"上午/下午"===a.substr(b,5).toUpperCase())return!0;++b;break;case"[":for(d=c;"]"!==a.charAt(b++)&&b<a.length;)d+=a.charAt(b);if(d.match(aH))return!0;break;case".":case"0":case"#":for(;b<a.length&&("0#?.,E+-%".indexOf(c=a.charAt(++b))>-1||"\\"==c&&"-"==a.charAt(b+1)&&"0#".indexOf(a.charAt(b+2))>-1););break;case"?":for(;a.charAt(++b)===c;);break;case"*":++b,(" "==a.charAt(b)||"*"==a.charAt(b))&&++b;break;case"(":case")":case" ":default:++b;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;b<a.length&&"0123456789".indexOf(a.charAt(++b))>-1;);}return!1}var aJ=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function aK(a,b){if(null==b)return!1;var c=parseFloat(b[2]);switch(b[1]){case"=":if(a==c)return!0;break;case">":if(a>c)return!0;break;case"<":if(a<c)return!0;break;case"<>":if(a!=c)return!0;break;case">=":if(a>=c)return!0;break;case"<=":if(a<=c)return!0}return!1}function aL(a,b,c){null==c&&(c={});var d="";switch(typeof a){case"string":d="m/d/yy"==a&&c.dateNF?c.dateNF:a;break;case"number":null==(d=14==a&&c.dateNF?c.dateNF:(null!=c.table?c.table:al)[a])&&(d=c.table&&c.table[am[a]]||al[am[a]]),null==d&&(d=an[a]||"General")}if(ai(d,0))return aw(b,c);b instanceof Date&&(b=at(b,c.date1904));var e=function(a,b){var c=function(a){for(var b=[],c=!1,d=0,e=0;d<a.length;++d)switch(a.charCodeAt(d)){case 34:c=!c;break;case 95:case 42:case 92:++d;break;case 59:b[b.length]=a.substr(e,d-e),e=d+1}if(b[b.length]=a.substr(e),!0===c)throw Error("Format |"+a+"| unterminated string ");return b}(a),d=c.length,e=c[d-1].indexOf("@");if(d<4&&e>-1&&--d,c.length>4)throw Error("cannot find right format for |"+c.join("|")+"|");if("number"!=typeof b)return[4,4===c.length||e>-1?c[c.length-1]:"@"];switch(c.length){case 1:c=e>-1?["General","General","General",c[0]]:[c[0],c[0],c[0],"@"];break;case 2:c=e>-1?[c[0],c[0],c[0],c[1]]:[c[0],c[1],c[0],"@"];break;case 3:c=e>-1?[c[0],c[1],c[0],c[2]]:[c[0],c[1],c[2],"@"]}var f=b>0?c[0]:b<0?c[1]:c[2];if(-1===c[0].indexOf("[")&&-1===c[1].indexOf("["))return[d,f];if(null!=c[0].match(/\[[=<>]/)||null!=c[1].match(/\[[=<>]/)){var g=c[0].match(aJ),h=c[1].match(aJ);return aK(b,g)?[d,c[0]]:aK(b,h)?[d,c[1]]:[d,c[null!=g&&null!=h?2:1]]}return[d,f]}(d,b);if(ai(e[1]))return aw(b,c);if(!0===b)b="TRUE";else if(!1===b)b="FALSE";else if(""===b||null==b)return"";return function(a,b,c,d){for(var e,f,g,h=[],i="",j=0,k="",l="t",m="H";j<a.length;)switch(k=a.charAt(j)){case"G":if(!ai(a,j))throw Error("unrecognized character "+k+" in "+a);h[h.length]={t:"G",v:"General"},j+=7;break;case'"':for(i="";34!==(g=a.charCodeAt(++j))&&j<a.length;)i+=String.fromCharCode(g);h[h.length]={t:"t",v:i},++j;break;case"\\":var n=a.charAt(++j),o="("===n||")"===n?n:"t";h[h.length]={t:o,v:n},++j;break;case"_":h[h.length]={t:"t",v:" "},j+=2;break;case"@":h[h.length]={t:"T",v:b},++j;break;case"B":case"b":if("1"===a.charAt(j+1)||"2"===a.charAt(j+1)){if(null==e&&null==(e=ap(b,c,"2"===a.charAt(j+1))))return"";h[h.length]={t:"X",v:a.substr(j,2)},l=k,j+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":k=k.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(b<0||null==e&&null==(e=ap(b,c)))return"";for(i=k;++j<a.length&&a.charAt(j).toLowerCase()===k;)i+=k;"m"===k&&"h"===l.toLowerCase()&&(k="M"),"h"===k&&(k=m),h[h.length]={t:k,v:i},l=k;break;case"A":case"a":case"上":var p={t:k,v:k};if(null==e&&(e=ap(b,c)),"A/P"===a.substr(j,3).toUpperCase()?(null!=e&&(p.v=e.H>=12?"P":"A"),p.t="T",m="h",j+=3):"AM/PM"===a.substr(j,5).toUpperCase()?(null!=e&&(p.v=e.H>=12?"PM":"AM"),p.t="T",j+=5,m="h"):"上午/下午"===a.substr(j,5).toUpperCase()?(null!=e&&(p.v=e.H>=12?"下午":"上午"),p.t="T",j+=5,m="h"):(p.t="t",++j),null==e&&"T"===p.t)return"";h[h.length]=p,l=k;break;case"[":for(i=k;"]"!==a.charAt(j++)&&j<a.length;)i+=a.charAt(j);if("]"!==i.slice(-1))throw'unterminated "[" block: |'+i+"|";if(i.match(aH)){if(null==e&&null==(e=ap(b,c)))return"";h[h.length]={t:"Z",v:i.toLowerCase()},l=i.charAt(1)}else i.indexOf("$")>-1&&(i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$",aI(a)||(h[h.length]={t:"t",v:i}));break;case".":if(null!=e){for(i=k;++j<a.length&&"0"===(k=a.charAt(j));)i+=k;h[h.length]={t:"s",v:i};break}case"0":case"#":for(i=k;++j<a.length&&"0#?.,E+-%".indexOf(k=a.charAt(j))>-1;)i+=k;h[h.length]={t:"n",v:i};break;case"?":for(i=k;a.charAt(++j)===k;)i+=k;h[h.length]={t:k,v:i},l=k;break;case"*":++j,(" "==a.charAt(j)||"*"==a.charAt(j))&&++j;break;case"(":case")":h[h.length]={t:1===d?"t":k,v:k},++j;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(i=k;j<a.length&&"0123456789".indexOf(a.charAt(++j))>-1;)i+=a.charAt(j);h[h.length]={t:"D",v:i};break;case" ":h[h.length]={t:k,v:k},++j;break;case"$":h[h.length]={t:"t",v:"$"},++j;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(k))throw Error("unrecognized character "+k+" in "+a);h[h.length]={t:"t",v:k},++j}var q,r=0,s=0;for(j=h.length-1,l="t";j>=0;--j)switch(h[j].t){case"h":case"H":h[j].t=m,l="h",r<1&&(r=1);break;case"s":(q=h[j].v.match(/\.0+$/))&&(s=Math.max(s,q[0].length-1)),r<3&&(r=3);case"d":case"y":case"M":case"e":l=h[j].t;break;case"m":"s"===l&&(h[j].t="M",r<2&&(r=2));break;case"X":break;case"Z":r<1&&h[j].v.match(/[Hh]/)&&(r=1),r<2&&h[j].v.match(/[Mm]/)&&(r=2),r<3&&h[j].v.match(/[Ss]/)&&(r=3)}switch(r){case 0:break;case 1:e.u>=.5&&(e.u=0,++e.S),e.S>=60&&(e.S=0,++e.M),e.M>=60&&(e.M=0,++e.H);break;case 2:e.u>=.5&&(e.u=0,++e.S),e.S>=60&&(e.S=0,++e.M)}var t,u="";for(j=0;j<h.length;++j)switch(h[j].t){case"t":case"T":case" ":case"D":break;case"X":h[j].v="",h[j].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":h[j].v=function(a,b,c,d){var e,f="",g=0,h=0,i=c.y,j=0;switch(a){case 98:i=c.y+543;case 121:switch(b.length){case 1:case 2:e=i%100,j=2;break;default:e=i%1e4,j=4}break;case 109:switch(b.length){case 1:case 2:e=c.m,j=b.length;break;case 3:return ak[c.m-1][1];case 5:return ak[c.m-1][0];default:return ak[c.m-1][2]}break;case 100:switch(b.length){case 1:case 2:e=c.d,j=b.length;break;case 3:return aj[c.q][0];default:return aj[c.q][1]}break;case 104:switch(b.length){case 1:case 2:e=1+(c.H+11)%12,j=b.length;break;default:throw"bad hour format: "+b}break;case 72:switch(b.length){case 1:case 2:e=c.H,j=b.length;break;default:throw"bad hour format: "+b}break;case 77:switch(b.length){case 1:case 2:e=c.M,j=b.length;break;default:throw"bad minute format: "+b}break;case 115:if("s"!=b&&"ss"!=b&&".0"!=b&&".00"!=b&&".000"!=b)throw"bad second format: "+b;if(0===c.u&&("s"==b||"ss"==b))return ae(c.S,b.length);if((g=Math.round((h=d>=2?3===d?1e3:100:1===d?10:1)*(c.S+c.u)))>=60*h&&(g=0),"s"===b)return 0===g?"0":""+g/h;if(f=ae(g,2+d),"ss"===b)return f.substr(0,2);return"."+f.substr(2,b.length-1);case 90:switch(b){case"[h]":case"[hh]":e=24*c.D+c.H;break;case"[m]":case"[mm]":e=(24*c.D+c.H)*60+c.M;break;case"[s]":case"[ss]":e=((24*c.D+c.H)*60+c.M)*60+Math.round(c.S+c.u);break;default:throw"bad abstime format: "+b}j=3===b.length?1:2;break;case 101:e=i,j=1}return j>0?ae(e,j):""}(h[j].t.charCodeAt(0),h[j].v,e,s),h[j].t="t";break;case"n":case"?":for(t=j+1;null!=h[t]&&("?"===(k=h[t].t)||"D"===k||(" "===k||"t"===k)&&null!=h[t+1]&&("?"===h[t+1].t||"t"===h[t+1].t&&"/"===h[t+1].v)||"("===h[j].t&&(" "===k||"n"===k||")"===k)||"t"===k&&("/"===h[t].v||" "===h[t].v&&null!=h[t+1]&&"?"==h[t+1].t));)h[j].v+=h[t].v,h[t]={v:"",t:";"},++t;u+=h[j].v,j=t-1;break;case"G":h[j].t="t",h[j].v=aw(b,c)}var v,w,x="";if(u.length>0){40==u.charCodeAt(0)?(v=b<0&&45===u.charCodeAt(0)?-b:b,w=aG("n",u,v)):(w=aG("n",u,v=b<0&&d>1?-b:b),v<0&&h[0]&&"t"==h[0].t&&(w=w.substr(1),h[0].v="-"+h[0].v)),t=w.length-1;var y=h.length;for(j=0;j<h.length;++j)if(null!=h[j]&&"t"!=h[j].t&&h[j].v.indexOf(".")>-1){y=j;break}var z=h.length;if(y===h.length&&-1===w.indexOf("E")){for(j=h.length-1;j>=0;--j)null!=h[j]&&-1!=="n?".indexOf(h[j].t)&&(t>=h[j].v.length-1?(t-=h[j].v.length,h[j].v=w.substr(t+1,h[j].v.length)):t<0?h[j].v="":(h[j].v=w.substr(0,t+1),t=-1),h[j].t="t",z=j);t>=0&&z<h.length&&(h[z].v=w.substr(0,t+1)+h[z].v)}else if(y!==h.length&&-1===w.indexOf("E")){for(t=w.indexOf(".")-1,j=y;j>=0;--j)if(null!=h[j]&&-1!=="n?".indexOf(h[j].t)){for(f=h[j].v.indexOf(".")>-1&&j===y?h[j].v.indexOf(".")-1:h[j].v.length-1,x=h[j].v.substr(f+1);f>=0;--f)t>=0&&("0"===h[j].v.charAt(f)||"#"===h[j].v.charAt(f))&&(x=w.charAt(t--)+x);h[j].v=x,h[j].t="t",z=j}for(t>=0&&z<h.length&&(h[z].v=w.substr(0,t+1)+h[z].v),t=w.indexOf(".")+1,j=y;j<h.length;++j)if(null!=h[j]&&(-1!=="n?(".indexOf(h[j].t)||j===y)){for(f=h[j].v.indexOf(".")>-1&&j===y?h[j].v.indexOf(".")+1:0,x=h[j].v.substr(0,f);f<h[j].v.length;++f)t<w.length&&(x+=w.charAt(t++));h[j].v=x,h[j].t="t",z=j}}}for(j=0;j<h.length;++j)null!=h[j]&&"n?".indexOf(h[j].t)>-1&&(v=d>1&&b<0&&j>0&&"-"===h[j-1].v?-b:b,h[j].v=aG(h[j].t,h[j].v,v),h[j].t="t");var A="";for(j=0;j!==h.length;++j)null!=h[j]&&(A+=h[j].v);return A}(e[1],b,c,e[0])}function aM(a,b){if("number"!=typeof b){b=+b||-1;for(var c=0;c<392;++c){if(void 0==al[c]){b<0&&(b=c);continue}if(al[c]==a){b=c;break}}b<0&&(b=391)}return al[b]=a,b}function aN(){var a;a||(a={}),a[0]="General",a[1]="0",a[2]="0.00",a[3]="#,##0",a[4]="#,##0.00",a[9]="0%",a[10]="0.00%",a[11]="0.00E+00",a[12]="# ?/?",a[13]="# ??/??",a[14]="m/d/yy",a[15]="d-mmm-yy",a[16]="d-mmm",a[17]="mmm-yy",a[18]="h:mm AM/PM",a[19]="h:mm:ss AM/PM",a[20]="h:mm",a[21]="h:mm:ss",a[22]="m/d/yy h:mm",a[37]="#,##0 ;(#,##0)",a[38]="#,##0 ;[Red](#,##0)",a[39]="#,##0.00;(#,##0.00)",a[40]="#,##0.00;[Red](#,##0.00)",a[45]="mm:ss",a[46]="[h]:mm:ss",a[47]="mmss.0",a[48]="##0.0E+0",a[49]="@",a[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',al=a}var aO={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},aP=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g,aQ=function(){var a={};a.version="1.2.0";var b=function(){for(var a=0,b=Array(256),c=0;256!=c;++c)a=1&(a=1&(a=1&(a=1&(a=1&(a=1&(a=1&(a=1&(a=c)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1)?-0x12477ce0^a>>>1:a>>>1,b[c]=a;return"undefined"!=typeof Int32Array?new Int32Array(b):b}(),c=function(a){var b=0,c=0,d=0,e="undefined"!=typeof Int32Array?new Int32Array(4096):Array(4096);for(d=0;256!=d;++d)e[d]=a[d];for(d=0;256!=d;++d)for(c=a[d],b=256+d;b<4096;b+=256)c=e[b]=c>>>8^a[255&c];var f=[];for(d=1;16!=d;++d)f[d-1]="undefined"!=typeof Int32Array?e.subarray(256*d,256*d+256):e.slice(256*d,256*d+256);return f}(b),d=c[0],e=c[1],f=c[2],g=c[3],h=c[4],i=c[5],j=c[6],k=c[7],l=c[8],m=c[9],n=c[10],o=c[11],p=c[12],q=c[13],r=c[14];return a.table=b,a.bstr=function(a,c){for(var d=-1^c,e=0,f=a.length;e<f;)d=d>>>8^b[(d^a.charCodeAt(e++))&255];return~d},a.buf=function(a,c){for(var s=-1^c,t=a.length-15,u=0;u<t;)s=r[a[u++]^255&s]^q[a[u++]^s>>8&255]^p[a[u++]^s>>16&255]^o[a[u++]^s>>>24]^n[a[u++]]^m[a[u++]]^l[a[u++]]^k[a[u++]]^j[a[u++]]^i[a[u++]]^h[a[u++]]^g[a[u++]]^f[a[u++]]^e[a[u++]]^d[a[u++]]^b[a[u++]];for(t+=15;u<t;)s=s>>>8^b[(s^a[u++])&255];return~s},a.str=function(a,c){for(var d=-1^c,e=0,f=a.length,g=0,h=0;e<f;)(g=a.charCodeAt(e++))<128?d=d>>>8^b[(d^g)&255]:g<2048?d=(d=d>>>8^b[(d^(192|g>>6&31))&255])>>>8^b[(d^(128|63&g))&255]:g>=55296&&g<57344?(g=(1023&g)+64,h=1023&a.charCodeAt(e++),d=(d=(d=(d=d>>>8^b[(d^(240|g>>8&7))&255])>>>8^b[(d^(128|g>>2&63))&255])>>>8^b[(d^(128|h>>6&15|(3&g)<<4))&255])>>>8^b[(d^(128|63&h))&255]):d=(d=(d=d>>>8^b[(d^(224|g>>12&15))&255])>>>8^b[(d^(128|g>>6&63))&255])>>>8^b[(d^(128|63&g))&255];return~d},a}(),aR=function(){var a,b,c={};function d(a){if("/"==a.charAt(a.length-1))return -1===a.slice(0,-1).indexOf("/")?a:d(a.slice(0,-1));var b=a.lastIndexOf("/");return -1===b?a:a.slice(0,b+1)}function e(a){if("/"==a.charAt(a.length-1))return e(a.slice(0,-1));var b=a.lastIndexOf("/");return -1===b?a:a.slice(b+1)}function f(a){ci(a,0);for(var b={},c=0;a.l<=a.length-4;){var d=a.read_shift(2),e=a.read_shift(2),f=a.l+e,g={};21589===d&&(1&(c=a.read_shift(1))&&(g.mtime=a.read_shift(4)),e>5&&(2&c&&(g.atime=a.read_shift(4)),4&c&&(g.ctime=a.read_shift(4))),g.mtime&&(g.mt=new Date(1e3*g.mtime))),a.l=f,b[d]=g}return b}function g(){return a||(a={})}function h(a,b){if(80==a[0]&&75==a[1])return ah(a,b);if((32|a[0])==109&&(32|a[1])==105)return function(a,b){if("mime-version:"!=s(a.slice(0,13)).toLowerCase())throw Error("Unsupported MAD header");var c=b&&b.root||"",d=(U&&Buffer.isBuffer(a)?a.toString("binary"):s(a)).split("\r\n"),e=0,f="";for(e=0;e<d.length;++e)if((f=d[e],/^Content-Location:/i.test(f))&&(f=f.slice(f.indexOf("file")),c||(c=f.slice(0,f.lastIndexOf("/")+1)),f.slice(0,c.length)!=c))for(;c.length>0&&(c=(c=c.slice(0,c.length-1)).slice(0,c.lastIndexOf("/")+1),f.slice(0,c.length)!=c););var g=(d[1]||"").match(/boundary="(.*?)"/);if(!g)throw Error("MAD cannot find boundary");var h="--"+(g[1]||""),i={FileIndex:[],FullPaths:[]};j(i);var k,l=0;for(e=0;e<d.length;++e){var m=d[e];(m===h||m===h+"--")&&(l++&&function(a,b,c){for(var d,e="",f="",g="",h=0;h<10;++h){var i=b[h];if(!i||i.match(/^\s*$/))break;var j=i.match(/^(.*?):\s*([^\s].*)$/);if(j)switch(j[1].toLowerCase()){case"content-location":e=j[2].trim();break;case"content-type":g=j[2].trim();break;case"content-transfer-encoding":f=j[2].trim()}}switch(++h,f.toLowerCase()){case"base64":d=Y(T(b.slice(h).join("")));break;case"quoted-printable":d=function(a){for(var b=[],c=0;c<a.length;++c){for(var d=a[c];c<=a.length&&"="==d.charAt(d.length-1);)d=d.slice(0,d.length-1)+a[++c];b.push(d)}for(var e=0;e<b.length;++e)b[e]=b[e].replace(/[=][0-9A-Fa-f]{2}/g,function(a){return String.fromCharCode(parseInt(a.slice(1),16))});return Y(b.join("\r\n"))}(b.slice(h));break;default:throw Error("Unsupported Content-Transfer-Encoding "+f)}var k=aj(a,e.slice(c.length),d,{unsafe:!0});g&&(k.ctype=g)}(i,d.slice(k,e),c),k=e)}return i}(a,b);if(a.length<512)throw Error("CFB file size "+a.length+" < 512");var c=3,d=512,e=0,f=0,g=0,h=0,k=0,l=[],p=a.slice(0,512);ci(p,0);var q=function(a){if(80==a[a.l]&&75==a[a.l+1])return[0,0];a.chk(o,"Header Signature: "),a.l+=16;var b=a.read_shift(2,"u");return[a.read_shift(2,"u"),b]}(p);switch(c=q[0]){case 3:d=512;break;case 4:d=4096;break;case 0:if(0==q[1])return ah(a,b);default:throw Error("Major Version: Expected 3 or 4 saw "+c)}512!==d&&ci(p=a.slice(0,d),28);var r=a.slice(0,d),t=p,u=c,v=9;switch(t.l+=2,v=t.read_shift(2)){case 9:if(3!=u)throw Error("Sector Shift: Expected 9 saw "+v);break;case 12:if(4!=u)throw Error("Sector Shift: Expected 12 saw "+v);break;default:throw Error("Sector Shift: Expected 9 or 12 saw "+v)}t.chk("0600","Mini Sector Shift: "),t.chk("000000000000","Reserved: ");var w=p.read_shift(4,"i");if(3===c&&0!==w)throw Error("# Directory Sectors: Expected 0 saw "+w);p.l+=4,g=p.read_shift(4,"i"),p.l+=4,p.chk("00100000","Mini Stream Cutoff Size: "),h=p.read_shift(4,"i"),e=p.read_shift(4,"i"),k=p.read_shift(4,"i"),f=p.read_shift(4,"i");for(var x=-1,y=0;y<109&&!((x=p.read_shift(4,"i"))<0);++y)l[y]=x;var z=function(a,b){for(var c=Math.ceil(a.length/b)-1,d=[],e=1;e<c;++e)d[e-1]=a.slice(e*b,(e+1)*b);return d[c-1]=a.slice(c*b),d}(a,d);!function a(b,c,d,e,f){var g=n;if(b===n){if(0!==c)throw Error("DIFAT chain shorter than expected")}else if(-1!==b){var h=d[b],i=(e>>>2)-1;if(!h)return;for(var j=0;j<i&&(g=cb(h,4*j))!==n;++j)f.push(g);a(cb(h,e-4),c-1,d,e,f)}}(k,f,z,d,l);var A=function(a,b,c,d){var e=a.length,f=[],g=[],h=[],i=[],j=d-1,k=0,l=0,m=0,n=0;for(k=0;k<e;++k)if(h=[],(m=k+b)>=e&&(m-=e),!g[m]){i=[];var o=[];for(l=m;l>=0;){o[l]=!0,g[l]=!0,h[h.length]=l,i.push(a[l]);var p=c[Math.floor(4*l/d)];if(d<4+(n=4*l&j))throw Error("FAT boundary crossed: "+l+" 4 "+d);if(!a[p]||o[l=cb(a[p],n)])break}f[m]={nodes:h,data:bP([i])}}return f}(z,g,l,d);A[g].name="!Directory",e>0&&h!==n&&(A[h].name="!MiniFAT"),A[l[0]].name="!FAT",A.fat_addrs=l,A.ssz=d;var B=[],C=[],D=[];(function(a,b,c,d,e,f,g,h){for(var j,k=0,l=2*!!d.length,o=b[a].data,p=0,q=0;p<o.length;p+=128){var r=o.slice(p,p+128);ci(r,64),q=r.read_shift(2),j=bR(r,0,q-l),d.push(j);var s={name:j,type:r.read_shift(1),color:r.read_shift(1),L:r.read_shift(4,"i"),R:r.read_shift(4,"i"),C:r.read_shift(4,"i"),clsid:r.read_shift(16),state:r.read_shift(4,"i"),start:0,size:0};0!==r.read_shift(2)+r.read_shift(2)+r.read_shift(2)+r.read_shift(2)&&(s.ct=i(r,r.l-8)),0!==r.read_shift(2)+r.read_shift(2)+r.read_shift(2)+r.read_shift(2)&&(s.mt=i(r,r.l-8)),s.start=r.read_shift(4,"i"),s.size=r.read_shift(4,"i"),s.size<0&&s.start<0&&(s.size=s.type=0,s.start=n,s.name=""),5===s.type?(k=s.start,e>0&&k!==n&&(b[k].name="!StreamData")):s.size>=4096?(s.storage="fat",void 0===b[s.start]&&(b[s.start]=function(a,b,c,d,e){var f=[],g=[];e||(e=[]);var h=d-1,i=0,j=0;for(i=b;i>=0;){e[i]=!0,f[f.length]=i,g.push(a[i]);var k=c[Math.floor(4*i/d)];if(d<4+(j=4*i&h))throw Error("FAT boundary crossed: "+i+" 4 "+d);if(!a[k])break;i=cb(a[k],j)}return{nodes:f,data:bP([g])}}(c,s.start,b.fat_addrs,b.ssz)),b[s.start].name=s.name,s.content=b[s.start].data.slice(0,s.size)):(s.storage="minifat",s.size<0?s.size=0:k!==n&&s.start!==n&&b[k]&&(s.content=function(a,b,c){for(var d=a.start,e=a.size,f=[],g=d;c&&e>0&&g>=0;)f.push(b.slice(g*m,g*m+m)),e-=m,g=cb(c,4*g);return 0===f.length?ck(0):aa(f).slice(0,a.size)}(s,b[k].data,(b[h]||{}).data))),s.content&&ci(s.content,0),f[j]=s,g.push(s)}})(g,A,z,B,e,{},C,h),function(a,b,c){for(var d=0,e=0,f=0,g=0,h=0,i=c.length,j=[],k=[];d<i;++d)j[d]=k[d]=d,b[d]=c[d];for(;h<k.length;++h)e=a[d=k[h]].L,f=a[d].R,g=a[d].C,j[d]===d&&(-1!==e&&j[e]!==e&&(j[d]=j[e]),-1!==f&&j[f]!==f&&(j[d]=j[f])),-1!==g&&(j[g]=d),-1!==e&&d!=j[d]&&(j[e]=j[d],k.lastIndexOf(e)<h&&k.push(e)),-1!==f&&d!=j[d]&&(j[f]=j[d],k.lastIndexOf(f)<h&&k.push(f));for(d=1;d<i;++d)j[d]===d&&(-1!==f&&j[f]!==f?j[d]=j[f]:-1!==e&&j[e]!==e&&(j[d]=j[e]));for(d=1;d<i;++d)if(0!==a[d].type){if((h=d)!=j[h])do h=j[h],b[d]=b[h]+"/"+b[d];while(0!==h&&-1!==j[h]&&h!=j[h]);j[d]=-1}for(b[0]+="/",d=1;d<i;++d)2!==a[d].type&&(b[d]+="/")}(C,D,B),B.shift();var E={FileIndex:C,FullPaths:D};return b&&b.raw&&(E.raw={header:r,sectors:z}),E}function i(a,b){return new Date((ca(a,b+4)/1e7*0x100000000+ca(a,b)/1e7-0x2b6109100)*1e3)}function j(a,b){var c=b||{},d=c.root||"Root Entry";if(a.FullPaths||(a.FullPaths=[]),a.FileIndex||(a.FileIndex=[]),a.FullPaths.length!==a.FileIndex.length)throw Error("inconsistent CFB structure");0===a.FullPaths.length&&(a.FullPaths[0]=d+"/",a.FileIndex[0]={name:d,type:5}),c.CLSID&&(a.FileIndex[0].clsid=c.CLSID),function(a){var b="\x01Sh33tJ5";if(!aR.find(a,"/"+b)){var c=ck(4);c[0]=55,c[1]=c[3]=50,c[2]=54,a.FileIndex.push({name:b,type:2,content:c,size:4,L:69,R:69,C:69}),a.FullPaths.push(a.FullPaths[0]+b),k(a)}}(a)}function k(a,b){j(a);for(var c=!1,f=!1,g=a.FullPaths.length-1;g>=0;--g){var h=a.FileIndex[g];switch(h.type){case 0:f?c=!0:(a.FileIndex.pop(),a.FullPaths.pop());break;case 1:case 2:case 5:f=!0,isNaN(h.R*h.L*h.C)&&(c=!0),h.R>-1&&h.L>-1&&h.R==h.L&&(c=!0);break;default:c=!0}}if(c||b){var i=new Date(1987,1,19),k=0,l=Object.create?Object.create(null):{},m=[];for(g=0;g<a.FullPaths.length;++g)l[a.FullPaths[g]]=!0,0!==a.FileIndex[g].type&&m.push([a.FullPaths[g],a.FileIndex[g]]);for(g=0;g<m.length;++g){var n=d(m[g][0]);(f=l[n])||(m.push([n,{name:e(n).replace("/",""),type:1,clsid:q,ct:i,mt:i,content:null}]),l[n]=!0)}for(m.sort(function(a,b){return function(a,b){for(var c=a.split("/"),d=b.split("/"),e=0,f=0,g=Math.min(c.length,d.length);e<g;++e){if(f=c[e].length-d[e].length)return f;if(c[e]!=d[e])return c[e]<d[e]?-1:1}return c.length-d.length}(a[0],b[0])}),a.FullPaths=[],a.FileIndex=[],g=0;g<m.length;++g)a.FullPaths[g]=m[g][0],a.FileIndex[g]=m[g][1];for(g=0;g<m.length;++g){var o=a.FileIndex[g],p=a.FullPaths[g];if(o.name=e(p).replace("/",""),o.L=o.R=o.C=-(o.color=1),o.size=o.content?o.content.length:0,o.start=0,o.clsid=o.clsid||q,0===g)o.C=m.length>1?1:-1,o.size=0,o.type=5;else if("/"==p.slice(-1)){for(k=g+1;k<m.length&&d(a.FullPaths[k])!=p;++k);for(o.C=k>=m.length?-1:k,k=g+1;k<m.length&&d(a.FullPaths[k])!=d(p);++k);o.R=k>=m.length?-1:k,o.type=1}else d(a.FullPaths[g+1]||"")==d(p)&&(o.R=g+1),o.type=2}}}function l(a,c){var d=c||{};if("mad"==d.fileType)return function(a,b){for(var c=b||{},d=c.boundary||"SheetJS",e=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(d="------="+d).slice(2)+'"',"","",""],f=a.FullPaths[0],g=f,h=a.FileIndex[0],i=1;i<a.FullPaths.length;++i)if(g=a.FullPaths[i].slice(f.length),(h=a.FileIndex[i]).size&&h.content&&"\x01Sh33tJ5"!=g){g=g.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(a){return"_x"+a.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(a){return"_u"+a.charCodeAt(0).toString(16)+"_"});for(var j=h.content,k=U&&Buffer.isBuffer(j)?j.toString("binary"):s(j),l=0,m=Math.min(1024,k.length),n=0,o=0;o<=m;++o)(n=k.charCodeAt(o))>=32&&n<128&&++l;var p=l>=4*m/5;e.push(d),e.push("Content-Location: "+(c.root||"file:///C:/SheetJS/")+g),e.push("Content-Transfer-Encoding: "+(p?"quoted-printable":"base64")),e.push("Content-Type: "+function(a,b){if(a.ctype)return a.ctype;var c=a.name||"",d=c.match(/\.([^\.]+)$/);return d&&ai[d[1]]||b&&(d=(c=b).match(/[\.\\]([^\.\\])+$/))&&ai[d[1]]?ai[d[1]]:"application/octet-stream"}(h,g)),e.push(""),e.push(p?function(a){var b=a.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(a){var b=a.charCodeAt(0).toString(16).toUpperCase();return"="+(1==b.length?"0"+b:b)});"\n"==(b=b.replace(/ $/mg,"=20").replace(/\t$/mg,"=09")).charAt(0)&&(b="=0D"+b.slice(1)),b=b.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,"\n=0A").replace(/([^\r\n])\n/mg,"$1=0A");for(var c=[],d=b.split("\r\n"),e=0;e<d.length;++e){var f=d[e];if(0==f.length){c.push("");continue}for(var g=0;g<f.length;){var h=76,i=f.slice(g,g+h);"="==i.charAt(h-1)?h--:"="==i.charAt(h-2)?h-=2:"="==i.charAt(h-3)&&(h-=3),i=f.slice(g,g+h),(g+=h)<f.length&&(i+="="),c.push(i)}}return c.join("\r\n")}(k):function(a){for(var b=S(a),c=[],d=0;d<b.length;d+=76)c.push(b.slice(d,d+76));return c.join("\r\n")+"\r\n"}(k))}return e.push(d+"--\r\n"),e.join("\r\n")}(a,d);if(k(a),"zip"===d.fileType)return function(a,c){var d=[],e=[],f=ck(1),g=8*!!(c||{}).compression,h=0,i=0,j=0,k=0,l=a.FullPaths[0],m=l,n=a.FileIndex[0],o=[],p=0;for(h=1;h<a.FullPaths.length;++h)if(m=a.FullPaths[h].slice(l.length),(n=a.FileIndex[h]).size&&n.content&&"\x01Sh33tJ5"!=m){var q,r=j,s=ck(m.length);for(i=0;i<m.length;++i)s.write_shift(1,127&m.charCodeAt(i));s=s.slice(0,s.l),o[k]=aQ.buf(n.content,0);var t=n.content;8==g&&(q=t,t=b?b.deflateRawSync(q):Q(q)),(f=ck(30)).write_shift(4,0x4034b50),f.write_shift(2,20),f.write_shift(2,0),f.write_shift(2,g),n.mt?function(a,b){"string"==typeof b&&(b=new Date(b));var c=b.getHours();c=(c=c<<6|b.getMinutes())<<5|b.getSeconds()>>>1,a.write_shift(2,c);var d=b.getFullYear()-1980;d=(d=d<<4|b.getMonth()+1)<<5|b.getDate(),a.write_shift(2,d)}(f,n.mt):f.write_shift(4,0),f.write_shift(-4,(0,o[k])),f.write_shift(4,(0,t.length)),f.write_shift(4,(0,n.content.length)),f.write_shift(2,s.length),f.write_shift(2,0),j+=f.length,d.push(f),j+=s.length,d.push(s),j+=t.length,d.push(t),(f=ck(46)).write_shift(4,0x2014b50),f.write_shift(2,0),f.write_shift(2,20),f.write_shift(2,0),f.write_shift(2,g),f.write_shift(4,0),f.write_shift(-4,o[k]),f.write_shift(4,t.length),f.write_shift(4,n.content.length),f.write_shift(2,s.length),f.write_shift(2,0),f.write_shift(2,0),f.write_shift(2,0),f.write_shift(2,0),f.write_shift(4,0),f.write_shift(4,r),p+=f.l,e.push(f),p+=s.length,e.push(s),++k}return(f=ck(22)).write_shift(4,0x6054b50),f.write_shift(2,0),f.write_shift(2,0),f.write_shift(2,k),f.write_shift(2,k),f.write_shift(4,p),f.write_shift(4,j),f.write_shift(2,0),aa([aa(d),aa(e),f])}(a,d);var e=function(a){for(var b=0,c=0,d=0;d<a.FileIndex.length;++d){var e=a.FileIndex[d];if(e.content){var f=e.content.length;f>0&&(f<4096?b+=f+63>>6:c+=f+511>>9)}}for(var g=a.FullPaths.length+3>>2,h=b+7>>3,i=b+127>>7,j=h+c+g+i,k=j+127>>7,l=k<=109?0:Math.ceil((k-109)/127);j+k+l+127>>7>k;)l=++k<=109?0:Math.ceil((k-109)/127);var m=[1,l,k,i,g,c,b,0];return a.FileIndex[0].size=b<<6,m[7]=(a.FileIndex[0].start=m[0]+m[1]+m[2]+m[3]+m[4]+m[5])+(m[6]+7>>3),m}(a),f=ck(e[7]<<9),g=0,h=0;for(g=0;g<8;++g)f.write_shift(1,p[g]);for(g=0;g<8;++g)f.write_shift(2,0);for(f.write_shift(2,62),f.write_shift(2,3),f.write_shift(2,65534),f.write_shift(2,9),f.write_shift(2,6),g=0;g<3;++g)f.write_shift(2,0);for(f.write_shift(4,0),f.write_shift(4,e[2]),f.write_shift(4,e[0]+e[1]+e[2]+e[3]-1),f.write_shift(4,0),f.write_shift(4,4096),f.write_shift(4,e[3]?e[0]+e[1]+e[2]-1:n),f.write_shift(4,e[3]),f.write_shift(-4,e[1]?e[0]-1:n),f.write_shift(4,e[1]),g=0;g<109;++g)f.write_shift(-4,g<e[2]?e[1]+g:-1);if(e[1])for(h=0;h<e[1];++h){for(;g<236+127*h;++g)f.write_shift(-4,g<e[2]?e[1]+g:-1);f.write_shift(-4,h===e[1]-1?n:h+1)}var i=function(a){for(h+=a;g<h-1;++g)f.write_shift(-4,g+1);a&&(++g,f.write_shift(-4,n))};for(h=(g=0)+e[1];g<h;++g)f.write_shift(-4,r.DIFSECT);for(h+=e[2];g<h;++g)f.write_shift(-4,r.FATSECT);i(e[3]),i(e[4]);for(var j=0,l=0,m=a.FileIndex[0];j<a.FileIndex.length;++j)(m=a.FileIndex[j]).content&&((l=m.content.length)<4096||(m.start=h,i(l+511>>9)));for(i(e[6]+7>>3);511&f.l;)f.write_shift(-4,r.ENDOFCHAIN);for(j=0,h=g=0;j<a.FileIndex.length;++j)(m=a.FileIndex[j]).content&&(l=m.content.length)&&!(l>=4096)&&(m.start=h,i(l+63>>6));for(;511&f.l;)f.write_shift(-4,r.ENDOFCHAIN);for(g=0;g<e[4]<<2;++g){var o=a.FullPaths[g];if(!o||0===o.length){for(j=0;j<17;++j)f.write_shift(4,0);for(j=0;j<3;++j)f.write_shift(4,-1);for(j=0;j<12;++j)f.write_shift(4,0);continue}m=a.FileIndex[g],0===g&&(m.start=m.size?m.start-1:n);var q=0===g&&d.root||m.name;if(l=2*(q.length+1),f.write_shift(64,q,"utf16le"),f.write_shift(2,l),f.write_shift(1,m.type),f.write_shift(1,m.color),f.write_shift(-4,m.L),f.write_shift(-4,m.R),f.write_shift(-4,m.C),m.clsid)f.write_shift(16,m.clsid,"hex");else for(j=0;j<4;++j)f.write_shift(4,0);f.write_shift(4,m.state||0),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(4,m.start),f.write_shift(4,m.size),f.write_shift(4,0)}for(g=1;g<a.FileIndex.length;++g)if((m=a.FileIndex[g]).size>=4096)if(f.l=m.start+1<<9,U&&Buffer.isBuffer(m.content))m.content.copy(f,f.l,0,m.size),f.l+=m.size+511&-512;else{for(j=0;j<m.size;++j)f.write_shift(1,m.content[j]);for(;511&j;++j)f.write_shift(1,0)}for(g=1;g<a.FileIndex.length;++g)if((m=a.FileIndex[g]).size>0&&m.size<4096)if(U&&Buffer.isBuffer(m.content))m.content.copy(f,f.l,0,m.size),f.l+=m.size+63&-64;else{for(j=0;j<m.size;++j)f.write_shift(1,m.content[j]);for(;63&j;++j)f.write_shift(1,0)}if(U)f.l=f.length;else for(;f.l<f.length;)f.write_shift(1,0);return f}c.version="1.2.1";var m=64,n=-2,o="d0cf11e0a1b11ae1",p=[208,207,17,224,161,177,26,225],q="00000000000000000000000000000000",r={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:-2,FREESECT:-1,HEADER_SIGNATURE:o,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:q,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function s(a){for(var b=Array(a.length),c=0;c<a.length;++c)b[c]=String.fromCharCode(a[c]);return b.join("")}for(var t=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],u=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],v=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],w="undefined"!=typeof Uint8Array,x=w?new Uint8Array(256):[],y=0;y<256;++y)x[y]=function(a){var b=(a<<1|a<<11)&139536|(a<<5|a<<15)&558144;return(b>>16|b>>8|b)&255}(y);function z(a,b){var c=7&b,d=b>>>3;return(a[d]|(c<=5?0:a[d+1]<<8))>>>c&7}function A(a,b){var c=7&b,d=b>>>3;return(a[d]|(c<=3?0:a[d+1]<<8))>>>c&31}function B(a,b){var c=7&b,d=b>>>3;return(a[d]|(c<=1?0:a[d+1]<<8))>>>c&127}function C(a,b,c){var d=7&b,e=b>>>3,f=(1<<c)-1,g=a[e]>>>d;return c<8-d||(g|=a[e+1]<<8-d,c<16-d||(g|=a[e+2]<<16-d,c<24-d))?g&f:(g|=a[e+3]<<24-d)&f}function D(a,b,c){var d=7&b,e=b>>>3;return d<=5?a[e]|=(7&c)<<d:(a[e]|=c<<d&255,a[e+1]=(7&c)>>8-d),b+3}function E(a,b,c){var d=b>>>3;return c<<=7&b,a[d]|=255&c,c>>>=8,a[d+1]=c,b+8}function F(a,b,c){var d=b>>>3;return c<<=7&b,a[d]|=255&c,c>>>=8,a[d+1]=255&c,a[d+2]=c>>>8,b+16}function G(a,b){var c=a.length,d=2*c>b?2*c:b+5,e=0;if(c>=b)return a;if(U){var f=X(d);if(a.copy)a.copy(f);else for(;e<a.length;++e)f[e]=a[e];return f}if(w){var g=new Uint8Array(d);if(g.set)g.set(a);else for(;e<c;++e)g[e]=a[e];return g}return a.length=d,a}function H(a){for(var b=Array(a),c=0;c<a;++c)b[c]=0;return b}function I(a,b,c){var d=1,e=0,f=0,g=0,h=0,i=a.length,j=w?new Uint16Array(32):H(32);for(f=0;f<32;++f)j[f]=0;for(f=i;f<c;++f)a[f]=0;i=a.length;var k=w?new Uint16Array(i):H(i);for(f=0;f<i;++f)j[e=a[f]]++,d<e&&(d=e),k[f]=0;for(f=1,j[0]=0;f<=d;++f)j[f+16]=h=h+j[f-1]<<1;for(f=0;f<i;++f)0!=(h=a[f])&&(k[f]=j[h+16]++);var l=0;for(f=0;f<i;++f)if(0!=(l=a[f]))for(h=function(a,b){var c=x[255&a];return b<=8?c>>>8-b:(c=c<<8|x[a>>8&255],b<=16)?c>>>16-b:(c=c<<8|x[a>>16&255])>>>24-b}(k[f],d)>>d-l,g=(1<<d+4-l)-1;g>=0;--g)b[h|g<<l]=15&l|f<<4;return d}var J=w?new Uint16Array(512):H(512),K=w?new Uint16Array(32):H(32);if(!w){for(var L=0;L<512;++L)J[L]=0;for(L=0;L<32;++L)K[L]=0}for(var M=[],N=0;N<32;N++)M.push(5);I(M,K,32);var O=[];for(N=0;N<=143;N++)O.push(8);for(;N<=255;N++)O.push(9);for(;N<=279;N++)O.push(7);for(;N<=287;N++)O.push(8);I(O,J,288);var P=function(){for(var a=w?new Uint8Array(32768):[],b=0,c=0;b<v.length-1;++b)for(;c<v[b+1];++c)a[c]=b;for(;c<32768;++c)a[c]=29;var d=w?new Uint8Array(259):[];for(b=0,c=0;b<u.length-1;++b)for(;c<u[b+1];++c)d[c]=b;return function(b,c){if(b.length<8){for(var e=0;e<b.length;){var f=Math.min(65535,b.length-e),g=e+f==b.length;for(c.write_shift(1,+g),c.write_shift(2,f),c.write_shift(2,65535&~f);f-- >0;)c[c.l++]=b[e++]}return c.l}return function(b,c){for(var e=0,f=0,g=w?new Uint16Array(32768):[];f<b.length;){var h=Math.min(65535,b.length-f);if(h<10){for(7&(e=D(c,e,+(f+h==b.length)))&&(e+=8-(7&e)),c.l=e/8|0,c.write_shift(2,h),c.write_shift(2,65535&~h);h-- >0;)c[c.l++]=b[f++];e=8*c.l;continue}e=D(c,e,+(f+h==b.length)+2);for(var i=0;h-- >0;){var j,k,l=b[f],m=-1,n=0;if((m=g[i=(i<<5^l)&32767])&&((m|=-32768&f)>f&&(m-=32768),m<f))for(;b[m+n]==b[f+n]&&n<250;)++n;if(n>2){(l=d[n])<=22?e=E(c,e,x[l+1]>>1)-1:(E(c,e,3),E(c,e+=5,x[l-23]>>5),e+=3);var o=l<8?0:l-4>>2;o>0&&(F(c,e,n-u[l]),e+=o),e=E(c,e,x[l=a[f-m]]>>3)-3;var p=l<4?0:l-2>>1;p>0&&(F(c,e,f-m-v[l]),e+=p);for(var q=0;q<n;++q)g[i]=32767&f,i=(i<<5^b[f])&32767,++f;h-=n-1}else l<=143?l+=48:(k=(1&(k=1))<<(7&(j=e)),c[j>>>3]|=k,e=j+1),e=E(c,e,x[l]),g[i]=32767&f,++f}e=E(c,e,0)-1}return c.l=(e+7)/8|0,c.l}(b,c)}}();function Q(a){var b=ck(50+Math.floor(1.1*a.length)),c=P(a,b);return b.slice(0,c)}var R=w?new Uint16Array(32768):H(32768),Z=w?new Uint16Array(32768):H(32768),_=w?new Uint16Array(128):H(128),ad=1,ae=1;function af(a,b){var c=function(a,b){if(3==a[0]&&!(3&a[1]))return[W(b),2];for(var c=0,d=0,e=X(b||262144),f=0,g=e.length>>>0,h=0,i=0;(1&d)==0;){if(d=z(a,c),c+=3,d>>>1==0){7&c&&(c+=8-(7&c));var j=a[c>>>3]|a[(c>>>3)+1]<<8;if(c+=32,j>0)for(!b&&g<f+j&&(g=(e=G(e,f+j)).length);j-- >0;)e[f++]=a[c>>>3],c+=8;continue}for(d>>1==1?(h=9,i=5):(c=function(a,b){var c,d,e,f=A(a,b)+257,g=A(a,b+=5)+1;b+=5;var h=(d=7&(c=b),((a[e=c>>>3]|(d<=4?0:a[e+1]<<8))>>>d&15)+4);b+=4;for(var i=0,j=w?new Uint8Array(19):H(19),k=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],l=1,m=w?new Uint8Array(8):H(8),n=w?new Uint8Array(8):H(8),o=j.length,p=0;p<h;++p)j[t[p]]=i=z(a,b),l<i&&(l=i),m[i]++,b+=3;var q=0;for(p=1,m[0]=0;p<=l;++p)n[p]=q=q+m[p-1]<<1;for(p=0;p<o;++p)0!=(q=j[p])&&(k[p]=n[q]++);var r=0;for(p=0;p<o;++p)if(0!=(r=j[p])){q=x[k[p]]>>8-r;for(var s=(1<<7-r)-1;s>=0;--s)_[q|s<<r]=7&r|p<<3}var u=[];for(l=1;u.length<f+g;)switch(q=_[B(a,b)],b+=7&q,q>>>=3){case 16:for(i=3+function(a,b){var c=7&b,d=b>>>3;return(a[d]|(c<=6?0:a[d+1]<<8))>>>c&3}(a,b),b+=2,q=u[u.length-1];i-- >0;)u.push(q);break;case 17:for(i=3+z(a,b),b+=3;i-- >0;)u.push(0);break;case 18:for(i=11+B(a,b),b+=7;i-- >0;)u.push(0);break;default:u.push(q),l<q&&(l=q)}var v=u.slice(0,f),y=u.slice(f);for(p=f;p<286;++p)v[p]=0;for(p=g;p<30;++p)y[p]=0;return ad=I(v,R,286),ae=I(y,Z,30),b}(a,c),h=ad,i=ae);;){!b&&g<f+32767&&(g=(e=G(e,f+32767)).length);var k=C(a,c,h),l=d>>>1==1?J[k]:R[k];if(c+=15&l,((l>>>=4)>>>8&255)==0)e[f++]=l;else if(256==l)break;else{var m=(l-=257)<8?0:l-4>>2;m>5&&(m=0);var n=f+u[l];m>0&&(n+=C(a,c,m),c+=m),k=C(a,c,i),c+=15&(l=d>>>1==1?K[k]:Z[k]);var o=(l>>>=4)<4?0:l-2>>1,p=v[l];for(o>0&&(p+=C(a,c,o),c+=o),!b&&g<n&&(g=(e=G(e,n+100)).length);f<n;)e[f]=e[f-p],++f}}}return b?[e,c+7>>>3]:[e.slice(0,f),c+7>>>3]}(a.slice(a.l||0),b);return a.l+=c[1],c[0]}function ag(a,b){if(a)"undefined"!=typeof console&&console.error(b);else throw Error(b)}function ah(a,c){ci(a,0);var d={FileIndex:[],FullPaths:[]};j(d,{root:c.root});for(var e=a.length-4;(80!=a[e]||75!=a[e+1]||5!=a[e+2]||6!=a[e+3])&&e>=0;)--e;a.l=e+4,a.l+=4;var g=a.read_shift(2);a.l+=6;var h=a.read_shift(4);for(e=0,a.l=h;e<g;++e){a.l+=20;var i=a.read_shift(4),k=a.read_shift(4),l=a.read_shift(2),m=a.read_shift(2),n=a.read_shift(2);a.l+=8;var o=a.read_shift(4),p=f(a.slice(a.l+l,a.l+l+m));a.l+=l+m+n;var q=a.l;a.l=o+4,function(a,c,d,e,g){a.l+=2;var h,i,j,k,l,m,n,o=a.read_shift(2),p=a.read_shift(2),q=(h=65535&a.read_shift(2),i=65535&a.read_shift(2),j=new Date,k=31&i,l=15&(i>>>=5),i>>>=4,j.setMilliseconds(0),j.setFullYear(i+1980),j.setMonth(l-1),j.setDate(k),m=31&h,n=63&(h>>>=5),h>>>=6,j.setHours(h),j.setMinutes(n),j.setSeconds(m<<1),j);if(8257&o)throw Error("Unsupported ZIP encryption");for(var r=a.read_shift(4),s=a.read_shift(4),t=a.read_shift(4),u=a.read_shift(2),v=a.read_shift(2),w="",x=0;x<u;++x)w+=String.fromCharCode(a[a.l++]);if(v){var y=f(a.slice(a.l,a.l+v));(y[21589]||{}).mt&&(q=y[21589].mt),((g||{})[21589]||{}).mt&&(q=g[21589].mt)}a.l+=v;var z=a.slice(a.l,a.l+s);switch(p){case 8:z=function(a,c){if(!b)return af(a,c);var d=new b.InflateRaw,e=d._processChunk(a.slice(a.l),d._finishFlushFlag);return a.l+=d.bytesRead,e}(a,t);break;case 0:break;default:throw Error("Unsupported ZIP Compression method "+p)}var A=!1;8&o&&(0x8074b50==a.read_shift(4)&&(a.read_shift(4),A=!0),s=a.read_shift(4),t=a.read_shift(4)),s!=c&&ag(A,"Bad compressed size: "+c+" != "+s),t!=d&&ag(A,"Bad uncompressed size: "+d+" != "+t),aj(e,w,z,{unsafe:!0,mt:q})}(a,i,k,d,p),a.l=q}return d}var ai={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function aj(a,b,c,d){var f=d&&d.unsafe;f||j(a);var g=!f&&aR.find(a,b);if(!g){var h=a.FullPaths[0];b.slice(0,h.length)==h?h=b:("/"!=h.slice(-1)&&(h+="/"),h=(h+b).replace("//","/")),g={name:e(b),type:2},a.FileIndex.push(g),a.FullPaths.push(h),f||aR.utils.cfb_gc(a)}return g.content=c,g.size=c?c.length:0,d&&(d.CLSID&&(g.clsid=d.CLSID),d.mt&&(g.mt=d.mt),d.ct&&(g.ct=d.ct)),g}return c.find=function(a,b){var c=a.FullPaths.map(function(a){return a.toUpperCase()}),d=c.map(function(a){var b=a.split("/");return b[b.length-("/"==a.slice(-1)?2:1)]}),e=!1;47===b.charCodeAt(0)?(e=!0,b=c[0].slice(0,-1)+b):e=-1!==b.indexOf("/");var f=b.toUpperCase(),g=!0===e?c.indexOf(f):d.indexOf(f);if(-1!==g)return a.FileIndex[g];var h=!f.match(ac);for(f=f.replace(ab,""),h&&(f=f.replace(ac,"!")),g=0;g<c.length;++g)if((h?c[g].replace(ac,"!"):c[g]).replace(ab,"")==f||(h?d[g].replace(ac,"!"):d[g]).replace(ab,"")==f)return a.FileIndex[g];return null},c.read=function(b,c){var d=c&&c.type;switch(!d&&U&&Buffer.isBuffer(b)&&(d="buffer"),d||"base64"){case"file":return g(),h(a.readFileSync(b),c);case"base64":return h(Y(T(b)),c);case"binary":return h(Y(b),c)}return h(b,c)},c.parse=h,c.write=function(b,c){var d=l(b,c);switch(c&&c.type||"buffer"){case"file":g(),a.writeFileSync(c.filename,d);break;case"binary":return"string"==typeof d?d:s(d);case"base64":return S("string"==typeof d?d:s(d));case"buffer":if(U)return Buffer.isBuffer(d)?d:V(d);case"array":return"string"==typeof d?Y(d):d}return d},c.writeFile=function(b,c,d){g();var e=l(b,d);a.writeFileSync(c,e)},c.utils={cfb_new:function(a){var b={};return j(b,a),b},cfb_add:aj,cfb_del:function(a,b){j(a);var c=aR.find(a,b);if(c){for(var d=0;d<a.FileIndex.length;++d)if(a.FileIndex[d]==c)return a.FileIndex.splice(d,1),a.FullPaths.splice(d,1),!0}return!1},cfb_mov:function(a,b,c){j(a);var d=aR.find(a,b);if(d){for(var f=0;f<a.FileIndex.length;++f)if(a.FileIndex[f]==d)return a.FileIndex[f].name=e(c),a.FullPaths[f]=c,!0}return!1},cfb_gc:function(a){k(a,!0)},ReadShift:cc,CheckField:ch,prep_blob:ci,bconcat:aa,use_zlib:function(a){try{var c=new a.InflateRaw;if(c._processChunk(new Uint8Array([3,0]),c._finishFlushFlag),c.bytesRead)b=a;else throw Error("zlib does not expose bytesRead")}catch(a){console.error("cannot use native zlib: "+(a.message||a))}},_deflateRaw:Q,_inflateRaw:af,consts:r},c}();function aS(a){for(var b=Object.keys(a),c=[],d=0;d<b.length;++d)Object.prototype.hasOwnProperty.call(a,b[d])&&c.push(b[d]);return c}function aT(a){for(var b=[],c=aS(a),d=0;d!==c.length;++d)b[a[c[d]]]=c[d];return b}var aU=new Date(1899,11,30,0,0,0);function aV(a,b){var c=a.getTime();return b&&(c-=1263168e5),(c-(aU.getTime()+(a.getTimezoneOffset()-aU.getTimezoneOffset())*6e4))/864e5}var aW=new Date,aX=aU.getTime()+(aW.getTimezoneOffset()-aU.getTimezoneOffset())*6e4,aY=aW.getTimezoneOffset();function aZ(a){var b=new Date;return b.setTime(24*a*36e5+aX),b.getTimezoneOffset()!==aY&&b.setTime(b.getTime()+(b.getTimezoneOffset()-aY)*6e4),b}var a$=new Date("2017-02-19T19:06:09.000Z"),a_=isNaN(a$.getFullYear())?new Date("2/19/17"):a$,a0=2017==a_.getFullYear();function a1(a,b){var c=new Date(a);if(a0)return b>0?c.setTime(c.getTime()+60*c.getTimezoneOffset()*1e3):b<0&&c.setTime(c.getTime()-60*c.getTimezoneOffset()*1e3),c;if(a instanceof Date)return a;if(1917==a_.getFullYear()&&!isNaN(c.getFullYear())){var d=c.getFullYear();return a.indexOf(""+d)>-1||c.setFullYear(c.getFullYear()+100),c}var e=a.match(/\d+/g)||["2017","2","19","0","0","0"],f=new Date(+e[0],e[1]-1,+e[2],+e[3]||0,+e[4]||0,+e[5]||0);return a.indexOf("Z")>-1&&(f=new Date(f.getTime()-60*f.getTimezoneOffset()*1e3)),f}function a2(a,b){if(U&&Buffer.isBuffer(a)){if(b){if(255==a[0]&&254==a[1])return bD(a.slice(2).toString("utf16le"));if(254==a[1]&&255==a[2])return bD(N(a.slice(2).toString("binary")))}return a.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(b){if(255==a[0]&&254==a[1])return bD(new TextDecoder("utf-16le").decode(a.slice(2)));if(254==a[0]&&255==a[1])return bD(new TextDecoder("utf-16be").decode(a.slice(2)))}var c={"€":"\x80","‚":"\x82",ƒ:"\x83","„":"\x84","…":"\x85","†":"\x86","‡":"\x87",ˆ:"\x88","‰":"\x89",Š:"\x8a","‹":"\x8b",Œ:"\x8c",Ž:"\x8e","‘":"\x91","’":"\x92","“":"\x93","”":"\x94","•":"\x95","–":"\x96","—":"\x97","˜":"\x98","™":"\x99",š:"\x9a","›":"\x9b",œ:"\x9c",ž:"\x9e",Ÿ:"\x9f"};return Array.isArray(a)&&(a=new Uint8Array(a)),new TextDecoder("latin1").decode(a).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(a){return c[a]||a})}catch(a){}for(var d=[],e=0;e!=a.length;++e)d.push(String.fromCharCode(a[e]));return d.join("")}function a3(a){if("undefined"!=typeof JSON&&!Array.isArray(a))return JSON.parse(JSON.stringify(a));if("object"!=typeof a||null==a)return a;if(a instanceof Date)return new Date(a.getTime());var b={};for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&(b[c]=a3(a[c]));return b}function a4(a,b){for(var c="";c.length<b;)c+=a;return c}function a5(a){var b=Number(a);if(!isNaN(b))return isFinite(b)?b:NaN;if(!/\d/.test(a))return b;var c=1,d=a.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return c*=100,""});return isNaN(b=Number(d))&&isNaN(b=Number(d=d.replace(/[(](.*)[)]/,function(a,b){return c=-c,b})))?b:b/c}var a6=["january","february","march","april","may","june","july","august","september","october","november","december"];function a7(a){var b=new Date(a),c=new Date(NaN),d=b.getYear(),e=b.getMonth(),f=b.getDate();if(isNaN(f))return c;var g=a.toLowerCase();if(g.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((g=g.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==a6.indexOf(g))return c}else if(g.match(/[a-z]/))return c;return d<0||d>8099?c:(e>0||f>1)&&101!=d?b:a.match(/[^-0-9:,\/\\]/)?c:b}var a8=function(){var a=5=="abacaba".split(/(:?b)/i).length;return function(b,c,d){if(a||"string"==typeof c)return b.split(c);for(var e=b.split(c),f=[e[0]],g=1;g<e.length;++g)f.push(d),f.push(e[g]);return f}}();function a9(a){return a?a.content&&a.type?a2(a.content,!0):a.data?O(a.data):a.asNodeBuffer&&U?O(a.asNodeBuffer().toString("binary")):a.asBinary?O(a.asBinary()):a._data&&a._data.getContent?O(a2(Array.prototype.slice.call(a._data.getContent(),0))):null:null}function ba(a){if(!a)return null;if(a.data)return M(a.data);if(a.asNodeBuffer&&U)return a.asNodeBuffer();if(a._data&&a._data.getContent){var b=a._data.getContent();return"string"==typeof b?M(b):Array.prototype.slice.call(b)}return a.content&&a.type?a.content:null}function bb(a,b){for(var c=a.FullPaths||aS(a.files),d=b.toLowerCase().replace(/[\/]/g,"\\"),e=d.replace(/\\/g,"/"),f=0;f<c.length;++f){var g=c[f].replace(/^Root Entry[\/]/,"").toLowerCase();if(d==g||e==g)return a.files?a.files[c[f]]:a.FileIndex[f]}return null}function bc(a,b){var c=bb(a,b);if(null==c)throw Error("Cannot find file "+b+" in zip");return c}function bd(a,b,c){if(!c){var d;return(d=bc(a,b))&&".bin"===d.name.slice(-4)?ba(d):a9(d)}if(!b)return null;try{return bd(a,b)}catch(a){return null}}function be(a,b,c){if(!c)return a9(bc(a,b));if(!b)return null;try{return be(a,b)}catch(a){return null}}function bf(a){for(var b=a.FullPaths||aS(a.files),c=[],d=0;d<b.length;++d)"/"!=b[d].slice(-1)&&c.push(b[d].replace(/^Root Entry[\/]/,""));return c.sort()}function bg(a,b){switch(b.type){case"base64":return aR.read(a,{type:"base64"});case"binary":return aR.read(a,{type:"binary"});case"buffer":case"array":return aR.read(a,{type:"buffer"})}throw Error("Unrecognized type "+b.type)}function bh(a,b){if("/"==a.charAt(0))return a.slice(1);var c=b.split("/");"/"!=b.slice(-1)&&c.pop();for(var d=a.split("/");0!==d.length;){var e=d.shift();".."===e?c.pop():"."!==e&&c.push(e)}return c.join("/")}var bi='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',bj=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,bk=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,bl=bi.match(bk)?bk:/<[^>]*>/g,bm=/<\w*:/,bn=/<(\/?)\w+:/;function bo(a,b,c){for(var d={},e=0,f=0;e!==a.length&&32!==(f=a.charCodeAt(e))&&10!==f&&13!==f;++e);if(b||(d[0]=a.slice(0,e)),e===a.length)return d;var g=a.match(bj),h=0,i="",j=0,k="",l="",m=1;if(g)for(j=0;j!=g.length;++j){for(f=0,l=g[j];f!=l.length&&61!==l.charCodeAt(f);++f);for(k=l.slice(0,f).trim();32==l.charCodeAt(f+1);)++f;for(h=0,m=+(34==(e=l.charCodeAt(f+1))||39==e),i=l.slice(f+1+m,l.length-m);h!=k.length&&58!==k.charCodeAt(h);++h);if(h===k.length)k.indexOf("_")>0&&(k=k.slice(0,k.indexOf("_"))),d[k]=i,c||(d[k.toLowerCase()]=i);else{var n=(5===h&&"xmlns"===k.slice(0,5)?"xmlns":"")+k.slice(h+1);if(d[n]&&"ext"==k.slice(h-3,h))continue;d[n]=i,c||(d[n.toLowerCase()]=i)}}return d}function bp(a){return a.replace(bn,"<$1")}var bq={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},br=aT(bq),bs=function(){var a=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,b=/_x([\da-fA-F]{4})_/ig;return function c(d){var e=d+"",f=e.indexOf("<![CDATA[");if(-1==f)return e.replace(a,function(a,b){return bq[a]||String.fromCharCode(parseInt(b,a.indexOf("x")>-1?16:10))||a}).replace(b,function(a,b){return String.fromCharCode(parseInt(b,16))});var g=e.indexOf("]]>");return c(e.slice(0,f))+e.slice(f+9,g)+c(e.slice(g+3))}}(),bt=/[&<>'"]/g,bu=/[\u0000-\u001f]/g;function bv(a){return(a+"").replace(bt,function(a){return br[a]}).replace(/\n/g,"<br/>").replace(bu,function(a){return"&#x"+("000"+a.charCodeAt(0).toString(16)).slice(-4)+";"})}var bw=function(){var a=/&#(\d+);/g;function b(a,b){return String.fromCharCode(parseInt(b,10))}return function(c){return c.replace(a,b)}}();function bx(a){switch(a){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function by(a){for(var b="",c=0,d=0,e=0,f=0,g=0,h=0;c<a.length;){if((d=a.charCodeAt(c++))<128){b+=String.fromCharCode(d);continue}if(e=a.charCodeAt(c++),d>191&&d<224){b+=String.fromCharCode((31&d)<<6|63&e);continue}if(f=a.charCodeAt(c++),d<240){b+=String.fromCharCode((15&d)<<12|(63&e)<<6|63&f);continue}b+=String.fromCharCode(55296+((h=((7&d)<<18|(63&e)<<12|(63&f)<<6|63&a.charCodeAt(c++))-65536)>>>10&1023)),b+=String.fromCharCode(56320+(1023&h))}return b}function bz(a){var b,c,d,e=W(2*a.length),f=1,g=0,h=0;for(c=0;c<a.length;c+=f)f=1,(d=a.charCodeAt(c))<128?b=d:d<224?(b=(31&d)*64+(63&a.charCodeAt(c+1)),f=2):d<240?(b=(15&d)*4096+(63&a.charCodeAt(c+1))*64+(63&a.charCodeAt(c+2)),f=3):(f=4,h=55296+((b=(7&d)*262144+(63&a.charCodeAt(c+1))*4096+(63&a.charCodeAt(c+2))*64+(63&a.charCodeAt(c+3))-65536)>>>10&1023),b=56320+(1023&b)),0!==h&&(e[g++]=255&h,e[g++]=h>>>8,h=0),e[g++]=b%256,e[g++]=b>>>8;return e.slice(0,g).toString("ucs2")}function bA(a){return V(a,"binary").toString("utf8")}var bB="foo bar baz\xe2\x98\x83\xf0\x9f\x8d\xa3",bC=U&&(bA(bB)==by(bB)&&bA||bz(bB)==by(bB)&&bz)||by,bD=U?function(a){return V(a,"utf8").toString("binary")}:function(a){for(var b=[],c=0,d=0,e=0;c<a.length;)switch(d=a.charCodeAt(c++),!0){case d<128:b.push(String.fromCharCode(d));break;case d<2048:b.push(String.fromCharCode(192+(d>>6))),b.push(String.fromCharCode(128+(63&d)));break;case d>=55296&&d<57344:d-=55296,b.push(String.fromCharCode(240+((e=a.charCodeAt(c++)-56320+(d<<10))>>18&7))),b.push(String.fromCharCode(144+(e>>12&63))),b.push(String.fromCharCode(128+(e>>6&63))),b.push(String.fromCharCode(128+(63&e)));break;default:b.push(String.fromCharCode(224+(d>>12))),b.push(String.fromCharCode(128+(d>>6&63))),b.push(String.fromCharCode(128+(63&d)))}return b.join("")},bE=function(){var a={};return function(b,c){var d=b+"|"+(c||"");return a[d]?a[d]:a[d]=RegExp("<(?:\\w+:)?"+b+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+b+">",c||"")}}(),bF=function(){var a=[["nbsp"," "],["middot","\xb7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(a){return[RegExp("&"+a[0]+";","ig"),a[1]]});return function(b){for(var c=b.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),d=0;d<a.length;++d)c=c.replace(a[d][0],a[d][1]);return c}}(),bG=function(){var a={};return function(b){return void 0!==a[b]?a[b]:a[b]=RegExp("<(?:vt:)?"+b+">([\\s\\S]*?)</(?:vt:)?"+b+">","g")}}(),bH=/<\/?(?:vt:)?variant>/g,bI=/<(?:vt:)([^>]*)>([\s\S]*)</;function bJ(a,b){var c=bo(a),d=a.match(bG(c.baseType))||[],e=[];if(d.length!=c.size){if(b.WTF)throw Error("unexpected vector length "+d.length+" != "+c.size);return e}return d.forEach(function(a){var b=a.replace(bH,"").match(bI);b&&e.push({v:bC(b[2]),t:b[1]})}),e}function bK(a){if(U&&Buffer.isBuffer(a))return a.toString("utf8");if("string"==typeof a)return a;if("undefined"!=typeof Uint8Array&&a instanceof Uint8Array)return bC(Z(_(a)));throw Error("Bad input format: expected Buffer or string")}var bL=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,bM={CT:"http://schemas.openxmlformats.org/package/2006/content-types"},bN=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],bO=function(a){for(var b=[],c=0;c<a[0].length;++c)if(a[0][c])for(var d=0,e=a[0][c].length;d<e;d+=10240)b.push.apply(b,a[0][c].slice(d,d+10240));return b},bP=U?function(a){return a[0].length>0&&Buffer.isBuffer(a[0][0])?Buffer.concat(a[0].map(function(a){return Buffer.isBuffer(a)?a:V(a)})):bO(a)}:bO,bQ=function(a,b,c){for(var d=[],e=b;e<c;e+=2)d.push(String.fromCharCode(b8(a,e)));return d.join("").replace(ab,"")},bR=U?function(a,b,c){return Buffer.isBuffer(a)?a.toString("utf16le",b,c).replace(ab,""):bQ(a,b,c)}:bQ,bS=function(a,b,c){for(var d=[],e=b;e<b+c;++e)d.push(("0"+a[e].toString(16)).slice(-2));return d.join("")},bT=U?function(a,b,c){return Buffer.isBuffer(a)?a.toString("hex",b,b+c):bS(a,b,c)}:bS,bU=function(a,b,c){for(var d=[],e=b;e<c;e++)d.push(String.fromCharCode(b7(a,e)));return d.join("")},bV=U?function(a,b,c){return Buffer.isBuffer(a)?a.toString("utf8",b,c):bU(a,b,c)}:bU,bW=function(a,b){var c=ca(a,b);return c>0?bV(a,b+4,b+4+c-1):""},bX=bW,bY=function(a,b){var c=ca(a,b);return c>0?bV(a,b+4,b+4+c-1):""},bZ=bY,b$=function(a,b){var c=2*ca(a,b);return c>0?bV(a,b+4,b+4+c-1):""},b_=b$,b0=function(a,b){var c=ca(a,b);return c>0?bR(a,b+4,b+4+c):""},b1=b0,b2=function(a,b){var c=ca(a,b);return c>0?bV(a,b+4,b+4+c):""},b3=b2,b4=function(a,b){for(var c=1-2*(a[b+7]>>>7),d=((127&a[b+7])<<4)+(a[b+6]>>>4&15),e=15&a[b+6],f=5;f>=0;--f)e=256*e+a[b+f];return 2047==d?0==e?1/0*c:NaN:(0==d?d=-1022:(d-=1023,e+=0x10000000000000),c*Math.pow(2,d-52)*e)},b5=b4,b6=function(a){return Array.isArray(a)||"undefined"!=typeof Uint8Array&&a instanceof Uint8Array};U&&(bX=function(a,b){if(!Buffer.isBuffer(a))return bW(a,b);var c=a.readUInt32LE(b);return c>0?a.toString("utf8",b+4,b+4+c-1):""},bZ=function(a,b){if(!Buffer.isBuffer(a))return bY(a,b);var c=a.readUInt32LE(b);return c>0?a.toString("utf8",b+4,b+4+c-1):""},b_=function(a,b){if(!Buffer.isBuffer(a))return b$(a,b);var c=2*a.readUInt32LE(b);return a.toString("utf16le",b+4,b+4+c-1)},b1=function(a,b){if(!Buffer.isBuffer(a))return b0(a,b);var c=a.readUInt32LE(b);return a.toString("utf16le",b+4,b+4+c)},b3=function(a,b){if(!Buffer.isBuffer(a))return b2(a,b);var c=a.readUInt32LE(b);return a.toString("utf8",b+4,b+4+c)},b5=function(a,b){return Buffer.isBuffer(a)?a.readDoubleLE(b):b4(a,b)},b6=function(a){return Buffer.isBuffer(a)||Array.isArray(a)||"undefined"!=typeof Uint8Array&&a instanceof Uint8Array}),void 0!==e&&(bR=function(a,b,c){return e.utils.decode(1200,a.slice(b,c)).replace(ab,"")},bV=function(a,b,c){return e.utils.decode(65001,a.slice(b,c))},bX=function(a,b){var c=ca(a,b);return c>0?e.utils.decode(G,a.slice(b+4,b+4+c-1)):""},bZ=function(a,b){var c=ca(a,b);return c>0?e.utils.decode(F,a.slice(b+4,b+4+c-1)):""},b_=function(a,b){var c=2*ca(a,b);return c>0?e.utils.decode(1200,a.slice(b+4,b+4+c-1)):""},b1=function(a,b){var c=ca(a,b);return c>0?e.utils.decode(1200,a.slice(b+4,b+4+c)):""},b3=function(a,b){var c=ca(a,b);return c>0?e.utils.decode(65001,a.slice(b+4,b+4+c)):""});var b7=function(a,b){return a[b]},b8=function(a,b){return 256*a[b+1]+a[b]},b9=function(a,b){var c=256*a[b+1]+a[b];return c<32768?c:-((65535-c+1)*1)},ca=function(a,b){return 0x1000000*a[b+3]+(a[b+2]<<16)+(a[b+1]<<8)+a[b]},cb=function(a,b){return a[b+3]<<24|a[b+2]<<16|a[b+1]<<8|a[b]};function cc(a,b){var c,d,f,g,h,i,j="",k=[];switch(b){case"dbcs":if(i=this.l,U&&Buffer.isBuffer(this))j=this.slice(this.l,this.l+2*a).toString("utf16le");else for(h=0;h<a;++h)j+=String.fromCharCode(b8(this,i)),i+=2;a*=2;break;case"utf8":j=bV(this,this.l,this.l+a);break;case"utf16le":a*=2,j=bR(this,this.l,this.l+a);break;case"wstr":if(void 0===e)return cc.call(this,a,"dbcs");j=e.utils.decode(F,this.slice(this.l,this.l+2*a)),a*=2;break;case"lpstr-ansi":j=bX(this,this.l),a=4+ca(this,this.l);break;case"lpstr-cp":j=bZ(this,this.l),a=4+ca(this,this.l);break;case"lpwstr":j=b_(this,this.l),a=4+2*ca(this,this.l);break;case"lpp4":a=4+ca(this,this.l),j=b1(this,this.l),2&a&&(a+=2);break;case"8lpp4":a=4+ca(this,this.l),j=b3(this,this.l),3&a&&(a+=4-(3&a));break;case"cstr":for(a=0,j="";0!==(f=b7(this,this.l+a++));)k.push(P(f));j=k.join("");break;case"_wstr":for(a=0,j="";0!==(f=b8(this,this.l+a));)k.push(P(f)),a+=2;a+=2,j=k.join("");break;case"dbcs-cont":for(h=0,j="",i=this.l;h<a;++h){if(this.lens&&-1!==this.lens.indexOf(i))return f=b7(this,i),this.l=i+1,g=cc.call(this,a-h,f?"dbcs-cont":"sbcs-cont"),k.join("")+g;k.push(P(b8(this,i))),i+=2}j=k.join(""),a*=2;break;case"cpstr":if(void 0!==e){j=e.utils.decode(F,this.slice(this.l,this.l+a));break}case"sbcs-cont":for(h=0,j="",i=this.l;h!=a;++h){if(this.lens&&-1!==this.lens.indexOf(i))return f=b7(this,i),this.l=i+1,g=cc.call(this,a-h,f?"dbcs-cont":"sbcs-cont"),k.join("")+g;k.push(P(b7(this,i))),i+=1}j=k.join("");break;default:switch(a){case 1:return c=b7(this,this.l),this.l++,c;case 2:return c=("i"===b?b9:b8)(this,this.l),this.l+=2,c;case 4:case -4:if("i"===b||(128&this[this.l+3])==0)return c=(a>0?cb:function(a,b){return a[b]<<24|a[b+1]<<16|a[b+2]<<8|a[b+3]})(this,this.l),this.l+=4,c;return d=ca(this,this.l),this.l+=4,d;case 8:case -8:if("f"===b)return d=8==a?b5(this,this.l):b5([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,d;a=8;case 16:j=bT(this,this.l,a)}}return this.l+=a,j}var cd=function(a,b,c){a[c]=255&b,a[c+1]=b>>>8&255,a[c+2]=b>>>16&255,a[c+3]=b>>>24&255},ce=function(a,b,c){a[c]=255&b,a[c+1]=b>>8&255,a[c+2]=b>>16&255,a[c+3]=b>>24&255},cf=function(a,b,c){a[c]=255&b,a[c+1]=b>>>8&255};function cg(a,b,c){var d=0,f=0;if("dbcs"===c){for(f=0;f!=b.length;++f)cf(this,b.charCodeAt(f),this.l+2*f);d=2*b.length}else if("sbcs"===c){if(void 0!==e&&874==G)for(f=0;f!=b.length;++f){var g=e.utils.encode(G,b.charAt(f));this[this.l+f]=g[0]}else for(f=0,b=b.replace(/[^\x00-\x7F]/g,"_");f!=b.length;++f)this[this.l+f]=255&b.charCodeAt(f);d=b.length}else if("hex"===c){for(;f<a;++f)this[this.l++]=parseInt(b.slice(2*f,2*f+2),16)||0;return this}else if("utf16le"===c){var h=Math.min(this.l+a,this.length);for(f=0;f<Math.min(b.length,a);++f){var i=b.charCodeAt(f);this[this.l++]=255&i,this[this.l++]=i>>8}for(;this.l<h;)this[this.l++]=0;return this}else switch(a){case 1:d=1,this[this.l]=255&b;break;case 2:d=2,this[this.l]=255&b,b>>>=8,this[this.l+1]=255&b;break;case 3:d=3,this[this.l]=255&b,b>>>=8,this[this.l+1]=255&b,b>>>=8,this[this.l+2]=255&b;break;case 4:d=4,cd(this,b,this.l);break;case 8:if(d=8,"f"===c){!function(a,b,c){var d=(b<0||1/b==-1/0)<<7,e=0,f=0,g=d?-b:b;isFinite(g)?0==g?e=f=0:(e=Math.floor(Math.log(g)/Math.LN2),f=g*Math.pow(2,52-e),e<=-1023&&(!isFinite(f)||f<0x10000000000000)?e=-1022:(f-=0x10000000000000,e+=1023)):(e=2047,f=26985*!!isNaN(b));for(var h=0;h<=5;++h,f/=256)a[c+h]=255&f;a[c+6]=(15&e)<<4|15&f,a[c+7]=e>>4|d}(this,b,this.l);break}case 16:break;case -4:d=4,ce(this,b,this.l)}return this.l+=d,this}function ch(a,b){var c=bT(this,this.l,a.length>>1);if(c!==a)throw Error(b+"Expected "+a+" saw "+c);this.l+=a.length>>1}function ci(a,b){a.l=b,a.read_shift=cc,a.chk=ch,a.write_shift=cg}function cj(a,b){a.l+=b}function ck(a){var b=W(a);return ci(b,0),b}function cl(a,b,c){if(a){ci(a,a.l||0);for(var d,e,f,g=a.length,h=0,i=0;a.l<g;){128&(h=a.read_shift(1))&&(h=(127&h)+((127&a.read_shift(1))<<7));var j=fB[h]||fB[65535];for(e=1,f=127&(d=a.read_shift(1));e<4&&128&d;++e)f+=(127&(d=a.read_shift(1)))<<7*e;i=a.l+f;var k=j.f&&j.f(a,f,c);if(a.l=i,b(k,j,h))return}}}function cm(){var a=[],b=U?256:2048,c=function(a){var b=ck(a);return ci(b,0),b},d=c(b),e=function(){d&&(d.length>d.l&&((d=d.slice(0,d.l)).l=d.length),d.length>0&&a.push(d),d=null)},f=function(a){return d&&a<d.length-d.l?d:(e(),d=c(Math.max(a+1,b)))};return{next:f,push:function(a){e(),null==(d=a).l&&(d.l=d.length),f(b)},end:function(){return e(),aa(a)},_bufs:a}}function cn(a,b,c){var d=a3(a);if(b.s?(d.cRel&&(d.c+=b.s.c),d.rRel&&(d.r+=b.s.r)):(d.cRel&&(d.c+=b.c),d.rRel&&(d.r+=b.r)),!c||c.biff<12){for(;d.c>=256;)d.c-=256;for(;d.r>=65536;)d.r-=65536}return d}function co(a,b,c){var d=a3(a);return d.s=cn(d.s,b.s,c),d.e=cn(d.e,b.s,c),d}function cp(a,b){if(a.cRel&&a.c<0)for(a=a3(a);a.c<0;)a.c+=b>8?16384:256;if(a.rRel&&a.r<0)for(a=a3(a);a.r<0;)a.r+=b>8?1048576:b>5?65536:16384;var c=cw(a);return a.cRel||null==a.cRel||(c=c.replace(/^([A-Z])/,"$$$1")),a.rRel||null==a.rRel||(c=c.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")),c}function cq(a,b){return 0!=a.s.r||a.s.rRel||a.e.r!=(b.biff>=12?1048575:b.biff>=8?65536:16384)||a.e.rRel?0!=a.s.c||a.s.cRel||a.e.c!=(b.biff>=12?16383:255)||a.e.cRel?cp(a.s,b.biff)+":"+cp(a.e,b.biff):(a.s.rRel?"":"$")+cs(a.s.r)+":"+(a.e.rRel?"":"$")+cs(a.e.r):(a.s.cRel?"":"$")+cu(a.s.c)+":"+(a.e.cRel?"":"$")+cu(a.e.c)}function cr(a){return parseInt(a.replace(/\$(\d+)$/,"$1"),10)-1}function cs(a){return""+(a+1)}function ct(a){for(var b=a.replace(/^\$([A-Z])/,"$1"),c=0,d=0;d!==b.length;++d)c=26*c+b.charCodeAt(d)-64;return c-1}function cu(a){if(a<0)throw Error("invalid column "+a);var b="";for(++a;a;a=Math.floor((a-1)/26))b=String.fromCharCode((a-1)%26+65)+b;return b}function cv(a){for(var b=0,c=0,d=0;d<a.length;++d){var e=a.charCodeAt(d);e>=48&&e<=57?b=10*b+(e-48):e>=65&&e<=90&&(c=26*c+(e-64))}return{c:c-1,r:b-1}}function cw(a){for(var b=a.c+1,c="";b;b=(b-1)/26|0)c=String.fromCharCode((b-1)%26+65)+c;return c+(a.r+1)}function cx(a){var b=a.indexOf(":");return -1==b?{s:cv(a),e:cv(a)}:{s:cv(a.slice(0,b)),e:cv(a.slice(b+1))}}function cy(a,b){return void 0===b||"number"==typeof b?cy(a.s,a.e):("string"!=typeof a&&(a=cw(a)),"string"!=typeof b&&(b=cw(b)),a==b?a:a+":"+b)}function cz(a){var b={s:{c:0,r:0},e:{c:0,r:0}},c=0,d=0,e=0,f=a.length;for(c=0;d<f&&!((e=a.charCodeAt(d)-64)<1)&&!(e>26);++d)c=26*c+e;for(b.s.c=--c,c=0;d<f&&!((e=a.charCodeAt(d)-48)<0)&&!(e>9);++d)c=10*c+e;if(b.s.r=--c,d===f||10!=e)return b.e.c=b.s.c,b.e.r=b.s.r,b;for(++d,c=0;d!=f&&!((e=a.charCodeAt(d)-64)<1)&&!(e>26);++d)c=26*c+e;for(b.e.c=--c,c=0;d!=f&&!((e=a.charCodeAt(d)-48)<0)&&!(e>9);++d)c=10*c+e;return b.e.r=--c,b}function cA(a,b){var c="d"==a.t&&b instanceof Date;if(null!=a.z)try{return a.w=aL(a.z,c?aV(b):b)}catch(a){}try{return a.w=aL((a.XF||{}).numFmtId||14*!!c,c?aV(b):b)}catch(a){return""+b}}function cB(a,b,c){return null==a||null==a.t||"z"==a.t?"":void 0!==a.w?a.w:("d"==a.t&&!a.z&&c&&c.dateNF&&(a.z=c.dateNF),"e"==a.t)?cT[a.v]||a.v:void 0==b?cA(a,a.v):cA(a,b)}function cC(a,b){var c=b&&b.sheet?b.sheet:"Sheet1",d={};return d[c]=a,{SheetNames:[c],Sheets:d}}function cD(a,b){return function(a,b,c){var d=c||{},e=(0,d.dense),f=a||(e?[]:{}),g=0,h=0;if(f&&null!=d.origin){if("number"==typeof d.origin)g=d.origin;else{var i="string"==typeof d.origin?cv(d.origin):d.origin;g=i.r,h=i.c}f["!ref"]||(f["!ref"]="A1:A1")}var j={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(f["!ref"]){var k=cz(f["!ref"]);j.s.c=k.s.c,j.s.r=k.s.r,j.e.c=Math.max(j.e.c,k.e.c),j.e.r=Math.max(j.e.r,k.e.r),-1==g&&(j.e.r=g=k.e.r+1)}for(var l=0;l!=b.length;++l)if(b[l]){if(!Array.isArray(b[l]))throw Error("aoa_to_sheet expects an array of arrays");for(var m=0;m!=b[l].length;++m)if(void 0!==b[l][m]){var n={v:b[l][m]},o=g+l,p=h+m;if(j.s.r>o&&(j.s.r=o),j.s.c>p&&(j.s.c=p),j.e.r<o&&(j.e.r=o),j.e.c<p&&(j.e.c=p),!b[l][m]||"object"!=typeof b[l][m]||Array.isArray(b[l][m])||b[l][m]instanceof Date)if(Array.isArray(n.v)&&(n.f=b[l][m][1],n.v=n.v[0]),null===n.v)if(n.f)n.t="n";else if(d.nullError)n.t="e",n.v=0;else{if(!d.sheetStubs)continue;n.t="z"}else"number"==typeof n.v?n.t="n":"boolean"==typeof n.v?n.t="b":n.v instanceof Date?(n.z=d.dateNF||al[14],d.cellDates?(n.t="d",n.w=aL(n.z,aV(n.v))):(n.t="n",n.v=aV(n.v),n.w=aL(n.z,n.v))):n.t="s";else n=b[l][m];if(e)f[o]||(f[o]=[]),f[o][p]&&f[o][p].z&&(n.z=f[o][p].z),f[o][p]=n;else{var q=cw({c:p,r:o});f[q]&&f[q].z&&(n.z=f[q].z),f[q]=n}}}return j.s.c<1e7&&(f["!ref"]=cy(j)),f}(null,a,b)}function cE(a){var b=a.read_shift(4);return 0===b?"":a.read_shift(b,"dbcs")}function cF(a,b){var c=a.l,d=a.read_shift(1),e=cE(a),f=[],g={t:e,h:e};if((1&d)!=0){for(var h=a.read_shift(4),i=0;i!=h;++i)f.push({ich:a.read_shift(2),ifnt:a.read_shift(2)});g.r=f}else g.r=[{ich:0,ifnt:0}];return a.l=c+b,g}function cG(a){var b=a.read_shift(4),c=a.read_shift(2);return c+=a.read_shift(1)<<16,a.l++,{c:b,iStyleRef:c}}function cH(a){var b=a.read_shift(2);return b+=a.read_shift(1)<<16,a.l++,{c:-1,iStyleRef:b}}function cI(a){var b=a.read_shift(4);return 0===b||0xffffffff===b?"":a.read_shift(b,"dbcs")}function cJ(a){var b=a.slice(a.l,a.l+4),c=1&b[0],d=2&b[0];a.l+=4;var e=0===d?b5([0,0,0,0,252&b[0],b[1],b[2],b[3]],0):cb(b,0)>>2;return c?e/100:e}function cK(a){var b={s:{},e:{}};return b.s.r=a.read_shift(4),b.e.r=a.read_shift(4),b.s.c=a.read_shift(4),b.e.c=a.read_shift(4),b}function cL(a){if(a.length-a.l<8)throw"XLS Xnum Buffer underflow";return a.read_shift(8,"f")}function cM(a,b){var c=a.read_shift(4);switch(c){case 0:return"";case 0xffffffff:case 0xfffffffe:return({2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"})[a.read_shift(4)]||""}if(c>400)throw Error("Unsupported Clipboard: "+c.toString(16));return a.l-=4,a.read_shift(0,1==b?"lpstr":"lpwstr")}var cN=[80,81],cO={1:{n:"CodePage",t:2},2:{n:"Category",t:80},3:{n:"PresentationFormat",t:80},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:80},15:{n:"Company",t:80},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:80},27:{n:"ContentStatus",t:80},28:{n:"Language",t:80},29:{n:"Version",t:80},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},cP={1:{n:"CodePage",t:2},2:{n:"Title",t:80},3:{n:"Subject",t:80},4:{n:"Author",t:80},5:{n:"Keywords",t:80},6:{n:"Comments",t:80},7:{n:"Template",t:80},8:{n:"LastAuthor",t:80},9:{n:"RevNumber",t:80},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:80},19:{n:"DocSecurity",t:3},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},cQ={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},cR=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],cS=a3([0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,8388608,32768,128,8421376,8388736,32896,0xc0c0c0,8421504,0x9999ff,0x993366,0xffffcc,0xccffff,6684774,0xff8080,26316,0xccccff,128,0xff00ff,0xffff00,65535,8388736,8388608,32896,255,52479,0xccffff,0xccffcc,0xffff99,0x99ccff,0xff99cc,0xcc99ff,0xffcc99,3368703,3394764,0x99cc00,0xffcc00,0xff9900,0xff6600,6710937,9868950,13158,3381606,13056,3355392,0x993300,0x993366,3355545,3355443,0xffffff,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].map(function(a){return[a>>16&255,a>>8&255,255&a]})),cT={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},cU={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},cV={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},cW={CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment"};function cX(a){var b=a.lastIndexOf("/");return a.slice(0,b+1)+"_rels/"+a.slice(b+1)+".rels"}function cY(a,b){var c={"!id":{}};if(!a)return c;"/"!==b.charAt(0)&&(b="/"+b);var d={};return(a.match(bl)||[]).forEach(function(a){var e=bo(a);if("<Relationship"===e[0]){var f={};f.Type=e.Type,f.Target=e.Target,f.Id=e.Id,e.TargetMode&&(f.TargetMode=e.TargetMode),c["External"===e.TargetMode?e.Target:bh(e.Target,b)]=f,d[e.Id]=f}}),c["!id"]=d,c}var cZ=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],c$=function(){for(var a=Array(cZ.length),b=0;b<cZ.length;++b){var c=cZ[b],d="(?:"+c[0].slice(0,c[0].indexOf(":"))+":)"+c[0].slice(c[0].indexOf(":")+1);a[b]=RegExp("<"+d+"[^>]*>([\\s\\S]*?)</"+d+">")}return a}();function c_(a){var b={};a=bC(a);for(var c=0;c<cZ.length;++c){var d=cZ[c],e=a.match(c$[c]);null!=e&&e.length>0&&(b[d[1]]=bs(e[1])),"date"===d[2]&&b[d[1]]&&(b[d[1]]=a1(b[d[1]]))}return b}var c0=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];function c1(a,b,c,d){var e=[];if("string"==typeof a)e=bJ(a,d);else for(var f=0;f<a.length;++f)e=e.concat(a[f].map(function(a){return{v:a}}));var g="string"==typeof b?bJ(b,d).map(function(a){return a.v}):b,h=0,i=0;if(g.length>0)for(var j=0;j!==e.length;j+=2){switch(i=+e[j+1].v,e[j].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsbl\xe4tter":case"\xc7alışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de c\xe1lculo":case"Planilhas":case"Regneark":case"Hojas de c\xe1lculo":case"Werkbladen":c.Worksheets=i,c.SheetNames=g.slice(h,h+i);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne omr\xe5der":c.NamedRanges=i,c.DefinedNames=g.slice(h,h+i);break;case"Charts":case"Diagramme":c.Chartsheets=i,c.ChartNames=g.slice(h,h+i)}h+=i}}var c2=/<[^>]+>[^<]*/g,c3={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function c4(a){var b=a.read_shift(4);return new Date((a.read_shift(4)/1e7*0x100000000+b/1e7-0x2b6109100)*1e3).toISOString().replace(/\.000/,"")}function c5(a,b,c){var d=a.l,e=a.read_shift(0,"lpstr-cp");if(c)for(;a.l-d&3;)++a.l;return e}function c6(a,b,c){var d=a.read_shift(0,"lpwstr");return c&&(a.l+=4-(d.length+1&3)&3),d}function c7(a,b,c){return 31===b?c6(a):c5(a,b,c)}function c8(a,b,c){return c7(a,b,4*(!1!==c))}function c9(a,b){for(var c=a.read_shift(4),d={},e=0;e!=c;++e){var f=a.read_shift(4),g=a.read_shift(4);d[f]=a.read_shift(g,1200===b?"utf16le":"utf8").replace(ab,"").replace(ac,"!"),1200===b&&g%2&&(a.l+=2)}return 3&a.l&&(a.l=a.l>>3<<2),d}function da(a){var b=a.read_shift(4),c=a.slice(a.l,a.l+b);return a.l+=b,(3&b)>0&&(a.l+=4-(3&b)&3),c}function db(a,b,c){var d,e,f=a.read_shift(2),g=c||{};if(a.l+=2,12!==b&&f!==b&&-1===cN.indexOf(b)&&((65534&b)!=4126||(65534&f)!=4126))throw Error("Expected type "+b+" saw "+f);switch(12===b?f:b){case 2:return e=a.read_shift(2,"i"),g.raw||(a.l+=2),e;case 3:return a.read_shift(4,"i");case 11:return 0!==a.read_shift(4);case 19:return a.read_shift(4);case 30:return c5(a,f,4).replace(ab,"");case 31:return c6(a);case 64:return c4(a);case 65:return da(a);case 71:return(d={}).Size=a.read_shift(4),a.l+=d.Size+3-(d.Size-1)%4,d;case 80:return c8(a,f,!g.raw).replace(ab,"");case 81:return(function(a,b){if(!b)throw Error("VtUnalignedString must have positive length");return c7(a,b,0)})(a,f).replace(ab,"");case 4108:for(var h=a.read_shift(4),i=[],j=0;j<h/2;++j)i.push(function(a){var b=a.l,c=db(a,81);return 0==a[a.l]&&0==a[a.l+1]&&a.l-b&2&&(a.l+=2),[c,db(a,3)]}(a));return i;case 4126:case 4127:return 4127==f?function(a){for(var b=a.read_shift(4),c=[],d=0;d!=b;++d){var e=a.l;c[d]=a.read_shift(0,"lpwstr").replace(ab,""),a.l-e&2&&(a.l+=2)}return c}(a):function(a){for(var b=a.read_shift(4),c=[],d=0;d!=b;++d)c[d]=a.read_shift(0,"lpstr-cp").replace(ab,"");return c}(a);default:throw Error("TypedPropertyValue unrecognized type "+b+" "+f)}}function dc(a,b){var c=a.l,d=a.read_shift(4),e=a.read_shift(4),f=[],g=0,h=0,i=-1,j={};for(g=0;g!=e;++g){var k=a.read_shift(4),l=a.read_shift(4);f[g]=[k,l+c]}f.sort(function(a,b){return a[1]-b[1]});var m={};for(g=0;g!=e;++g){if(a.l!==f[g][1]){var n=!0;if(g>0&&b)switch(b[f[g-1][0]].t){case 2:a.l+2===f[g][1]&&(a.l+=2,n=!1);break;case 80:case 4108:a.l<=f[g][1]&&(a.l=f[g][1],n=!1)}if((!b||0==g)&&a.l<=f[g][1]&&(n=!1,a.l=f[g][1]),n)throw Error("Read Error: Expected address "+f[g][1]+" at "+a.l+" :"+g)}if(b){var o=b[f[g][0]];if(m[o.n]=db(a,o.t,{raw:!0}),"version"===o.p&&(m[o.n]=String(m[o.n]>>16)+"."+("0000"+String(65535&m[o.n])).slice(-4)),"CodePage"==o.n)switch(m[o.n]){case 0:m[o.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case -536:case 65001:case -535:K(h=m[o.n]>>>0&65535);break;default:throw Error("Unsupported CodePage: "+m[o.n])}}else if(1===f[g][0]){if(K(h=m.CodePage=db(a,2)),-1!==i){var p=a.l;a.l=f[i][1],j=c9(a,h),a.l=p}}else if(0===f[g][0]){if(0===h){i=g,a.l=f[g+1][1];continue}j=c9(a,h)}else{var q,r=j[f[g][0]];switch(a[a.l]){case 65:a.l+=4,q=da(a);break;case 30:case 31:a.l+=4,q=c8(a,a[a.l-4]).replace(/\u0000+$/,"");break;case 3:a.l+=4,q=a.read_shift(4,"i");break;case 19:a.l+=4,q=a.read_shift(4);break;case 5:a.l+=4,q=a.read_shift(8,"f");break;case 11:a.l+=4,q=df(a,4);break;case 64:a.l+=4,q=a1(c4(a));break;default:throw Error("unparsed value: "+a[a.l])}m[r]=q}}return a.l=c+d,m}function dd(a,b,c){var d,e=a.content;if(!e)return{};ci(e,0);var f,g,h,i,j=0;e.chk("feff","Byte Order: "),e.read_shift(2);var k=e.read_shift(4),l=e.read_shift(16);if(l!==aR.utils.consts.HEADER_CLSID&&l!==c)throw Error("Bad PropertySet CLSID "+l);if(1!==(f=e.read_shift(4))&&2!==f)throw Error("Unrecognized #Sets: "+f);if(g=e.read_shift(16),i=e.read_shift(4),1===f&&i!==e.l)throw Error("Length mismatch: "+i+" !== "+e.l);2===f&&(h=e.read_shift(16),j=e.read_shift(4));var m=dc(e,b),n={SystemIdentifier:k};for(var o in m)n[o]=m[o];if(n.FMTID=g,1===f)return n;if(j-e.l==2&&(e.l+=2),e.l!==j)throw Error("Length mismatch 2: "+e.l+" !== "+j);try{d=dc(e,null)}catch(a){}for(o in d)n[o]=d[o];return n.FMTID=[g,h],n}function de(a,b){return a.read_shift(b),null}function df(a,b){return 1===a.read_shift(b)}function dg(a){return a.read_shift(2,"u")}function dh(a,b){for(var c=[],d=a.l+b;a.l<d;)c.push(dg(a,d-a.l));if(d!==a.l)throw Error("Slurp error");return c}function di(a,b,c){var d=a.read_shift(c&&c.biff>=12?2:1),e="sbcs-cont",f=F;c&&c.biff>=8&&(F=1200),c&&8!=c.biff?12==c.biff&&(e="wstr"):a.read_shift(1)&&(e="dbcs-cont"),c.biff>=2&&c.biff<=5&&(e="cpstr");var g=d?a.read_shift(d,e):"";return F=f,g}function dj(a,b,c){if(c){if(c.biff>=2&&c.biff<=5)return a.read_shift(b,"cpstr");if(c.biff>=12)return a.read_shift(b,"dbcs-cont")}return 0===a.read_shift(1)?a.read_shift(b,"sbcs-cont"):a.read_shift(b,"dbcs-cont")}function dk(a,b,c){var d=a.read_shift(c&&2==c.biff?1:2);return 0===d?(a.l++,""):dj(a,d,c)}function dl(a,b,c){if(c.biff>5)return dk(a,b,c);var d=a.read_shift(1);return 0===d?(a.l++,""):a.read_shift(d,c.biff<=4||!a.lens?"cpstr":"sbcs-cont")}function dm(a){var b=a.read_shift(4);return b>0?a.read_shift(b,"utf16le").replace(ab,""):""}function dn(a){return[a.read_shift(1),a.read_shift(1),a.read_shift(1),a.read_shift(1)]}function dp(a,b){var c=dn(a,b);return c[3]=0,c}function dq(a){return{r:a.read_shift(2),c:a.read_shift(2),ixfe:a.read_shift(2)}}function dr(a){return[a.read_shift(2),cJ(a)]}function ds(a){var b=a.read_shift(2),c=a.read_shift(2);return{s:{c:a.read_shift(2),r:b},e:{c:a.read_shift(2),r:c}}}function dt(a){var b=a.read_shift(2),c=a.read_shift(2);return{s:{c:a.read_shift(1),r:b},e:{c:a.read_shift(1),r:c}}}function du(a){a.l+=4;var b=a.read_shift(2),c=a.read_shift(2),d=a.read_shift(2);return a.l+=12,[c,b,d]}function dv(a){a.l+=2,a.l+=a.read_shift(2)}var dw={0:dv,4:dv,5:dv,6:dv,7:function(a){return a.l+=4,a.cf=a.read_shift(2),{}},8:dv,9:dv,10:dv,11:dv,12:dv,13:function(a){var b={};return a.l+=4,a.l+=16,b.fSharedNote=a.read_shift(2),a.l+=4,b},14:dv,15:dv,16:dv,17:dv,18:dv,19:dv,20:dv,21:du};function dx(a,b){var c={BIFFVer:0,dt:0};switch(c.BIFFVer=a.read_shift(2),(b-=2)>=2&&(c.dt=a.read_shift(2),a.l-=2),c.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(b>6)throw Error("Unexpected BIFF Ver "+c.BIFFVer)}return a.read_shift(b),c}function dy(a,b,c){var d=0;c&&2==c.biff||(d=a.read_shift(2));var e=a.read_shift(2);return c&&2==c.biff&&(d=1-(e>>15),e&=32767),[{Unsynced:1&d,DyZero:(2&d)>>1,ExAsc:(4&d)>>2,ExDsc:(8&d)>>3},e]}function dz(a,b,c){var d=a.l+b,e=8!=c.biff&&c.biff?2:4,f=a.read_shift(e),g=a.read_shift(e),h=a.read_shift(2),i=a.read_shift(2);return a.l=d,{s:{r:f,c:h},e:{r:g,c:i}}}function dA(a,b,c){var d,e=dq(a,6);(2==c.biff||9==b)&&++a.l;var f=(d=a.read_shift(1),1===a.read_shift(1)?d:1===d);return e.val=f,e.t=!0===f||!1===f?"b":"e",e}var dB=function(a,b,c){return 0===b?"":dl(a,b,c)};function dC(a,b,c){var d,e=a.read_shift(2),f={fBuiltIn:1&e,fWantAdvise:e>>>1&1,fWantPict:e>>>2&1,fOle:e>>>3&1,fOleLink:e>>>4&1,cf:e>>>5&1023,fIcon:e>>>15&1};return 14849===c.sbcch&&(d=function(a,b,c){a.l+=4,b-=4;var d=a.l+b,e=di(a,b,c),f=a.read_shift(2);if(f!==(d-=a.l))throw Error("Malformed AddinUdf: padding = "+d+" != "+f);return a.l+=f,e}(a,b-2,c)),f.body=d||a.read_shift(b-2),"string"==typeof d&&(f.Name=d),f}var dD=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function dE(a,b,c){var d,e,f,g,h,i,j,k=a.l+b,l=a.read_shift(2),m=a.read_shift(1),n=a.read_shift(1),o=a.read_shift(c&&2==c.biff?1:2),p=0;(!c||c.biff>=5)&&(5!=c.biff&&(a.l+=2),p=a.read_shift(2),5==c.biff&&(a.l+=2),a.l+=4);var q=dj(a,n,c);32&l&&(q=dD[q.charCodeAt(0)]);var r=k-a.l;return c&&2==c.biff&&--r,{chKey:m,Name:q,itab:p,rgce:k!=a.l&&0!==o&&r>0?(d=a,e=r,f=c,g=o,i=d.l+e,j=eO(d,g,f),i!==d.l&&(h=eN(d,i-d.l,j,f)),[j,h]):[]}}function dF(a,b,c){if(c.biff<8){var d,e,f,g;return d=a,e=b,f=c,3==d[d.l+1]&&d[d.l]++,3==(g=di(d,e,f)).charCodeAt(0)?g.slice(1):g}for(var h=[],i=a.l+b,j=a.read_shift(c.biff>8?4:2);0!=j--;)h.push(function(a,b,c){var d=c.biff>8?4:2;return[a.read_shift(d),a.read_shift(d,"i"),a.read_shift(d,"i")]}(a,c.biff,c));if(a.l!=i)throw Error("Bad ExternSheet: "+a.l+" != "+i);return h}function dG(a,b,c){var d=dt(a,6);switch(c.biff){case 2:a.l++,b-=7;break;case 3:case 4:a.l+=2,b-=8;break;default:a.l+=6,b-=12}return[d,function(a,b,c){var d,e,f=a.l+b,g=2==c.biff?1:2,h=a.read_shift(g);if(65535==h)return[[],(d=b-2,void(a.l+=d))];var i=eO(a,h,c);return b!==h+g&&(e=eN(a,b-h-g,i,c)),a.l=f,[i,e]}(a,b,c,d)]}var dH={8:function(a,b){var c=a.l+b;a.l+=10;var d=a.read_shift(2);a.l+=4,a.l+=2,a.l+=2,a.l+=2,a.l+=4;var e=a.read_shift(1);return a.l+=e,a.l=c,{fmt:d}}};function dI(a,b,c){if(!c.cellStyles)return void(a.l+=b);var d=c&&c.biff>=12?4:2,e=a.read_shift(d),f=a.read_shift(d),g=a.read_shift(d),h=a.read_shift(d),i=a.read_shift(2);2==d&&(a.l+=2);var j={s:e,e:f,w:g,ixfe:h,flags:i};return(c.biff>=5||!c.biff)&&(j.level=i>>8&7),j}var dJ=[2,3,48,49,131,139,140,245],dK=function(){var a={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},b=aT({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function c(b,c){var d=c||{};d.dateNF||(d.dateNF="yyyymmdd");var f=cD(function(b,c){var d=[],f=W(1);switch(c.type){case"base64":f=Y(T(b));break;case"binary":f=Y(b);break;case"buffer":case"array":f=b}ci(f,0);var g=f.read_shift(1),h=!!(136&g),i=!1,j=!1;switch(g){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:i=!0,h=!0;break;case 140:j=!0;break;default:throw Error("DBF Unsupported Version: "+g.toString(16))}var k=0,l=521;2==g&&(k=f.read_shift(2)),f.l+=3,2!=g&&(k=f.read_shift(4)),k>1048576&&(k=1e6),2!=g&&(l=f.read_shift(2));var m=f.read_shift(2),n=c.codepage||1252;2!=g&&(f.l+=16,f.read_shift(1),0!==f[f.l]&&(n=a[f[f.l]]),f.l+=1,f.l+=2),j&&(f.l+=36);for(var o=[],p={},q=Math.min(f.length,2==g?521:l-10-264*!!i),r=j?32:11;f.l<q&&13!=f[f.l];)switch((p={}).name=e.utils.decode(n,f.slice(f.l,f.l+r)).replace(/[\u0000\r\n].*$/g,""),f.l+=r,p.type=String.fromCharCode(f.read_shift(1)),2!=g&&!j&&(p.offset=f.read_shift(4)),p.len=f.read_shift(1),2==g&&(p.offset=f.read_shift(2)),p.dec=f.read_shift(1),p.name.length&&o.push(p),2!=g&&(f.l+=j?13:14),p.type){case"B":(!i||8!=p.len)&&c.WTF&&console.log("Skipping "+p.name+":"+p.type);break;case"G":case"P":c.WTF&&console.log("Skipping "+p.name+":"+p.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw Error("Unknown Field Type: "+p.type)}if(13!==f[f.l]&&(f.l=l-1),13!==f.read_shift(1))throw Error("DBF Terminator not found "+f.l+" "+f[f.l]);f.l=l;var s=0,t=0;for(t=0,d[0]=[];t!=o.length;++t)d[0][t]=o[t].name;for(;k-- >0;){if(42===f[f.l]){f.l+=m;continue}for(++f.l,d[++s]=[],t=0,t=0;t!=o.length;++t){var u=f.slice(f.l,f.l+o[t].len);f.l+=o[t].len,ci(u,0);var v=e.utils.decode(n,u);switch(o[t].type){case"C":v.trim().length&&(d[s][t]=v.replace(/\s+$/,""));break;case"D":8===v.length?d[s][t]=new Date(+v.slice(0,4),v.slice(4,6)-1,+v.slice(6,8)):d[s][t]=v;break;case"F":d[s][t]=parseFloat(v.trim());break;case"+":case"I":d[s][t]=j?0x80000000^u.read_shift(-4,"i"):u.read_shift(4,"i");break;case"L":switch(v.trim().toUpperCase()){case"Y":case"T":d[s][t]=!0;break;case"N":case"F":d[s][t]=!1;break;case"":case"?":break;default:throw Error("DBF Unrecognized L:|"+v+"|")}break;case"M":if(!h)throw Error("DBF Unexpected MEMO for type "+g.toString(16));d[s][t]="##MEMO##"+(j?parseInt(v.trim(),10):u.read_shift(4));break;case"N":(v=v.replace(/\u0000/g,"").trim())&&"."!=v&&(d[s][t]=+v||0);break;case"@":d[s][t]=new Date(u.read_shift(-8,"f")-621356832e5);break;case"T":d[s][t]=new Date((u.read_shift(4)-2440588)*864e5+u.read_shift(4));break;case"Y":d[s][t]=u.read_shift(4,"i")/1e4+u.read_shift(4,"i")/1e4*0x100000000;break;case"O":d[s][t]=-u.read_shift(-8,"f");break;case"B":if(i&&8==o[t].len){d[s][t]=u.read_shift(8,"f");break}case"G":case"P":u.l+=o[t].len;break;case"0":if("_NullFlags"===o[t].name)break;default:throw Error("DBF Unsupported data type "+o[t].type)}}}if(2!=g&&f.l<f.length&&26!=f[f.l++])throw Error("DBF EOF Marker missing "+(f.l-1)+" of "+f.length+" "+f[f.l-1].toString(16));return c&&c.sheetRows&&(d=d.slice(0,c.sheetRows)),c.DBF=o,d}(b,d),d);return f["!cols"]=d.DBF.map(function(a){return{wch:a.len,DBF:a}}),delete d.DBF,f}var d={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(a,b){try{return cC(c(a,b),b)}catch(a){if(b&&b.WTF)throw a}return{SheetNames:[],Sheets:{}}},to_sheet:c,from_sheet:function(a,c){var e=c||{};if(+e.codepage>=0&&K(+e.codepage),"string"==e.type)throw Error("Cannot write DBF to JS string");var f=cm(),g=fX(a,{header:1,raw:!0,cellDates:!0}),h=g[0],i=g.slice(1),j=a["!cols"]||[],k=0,l=0,m=0,n=1;for(k=0;k<h.length;++k){if(((j[k]||{}).DBF||{}).name){h[k]=j[k].DBF.name,++m;continue}if(null!=h[k]){if(++m,"number"==typeof h[k]&&(h[k]=h[k].toString(10)),"string"!=typeof h[k])throw Error("DBF Invalid column name "+h[k]+" |"+typeof h[k]+"|");if(h.indexOf(h[k])!==k){for(l=0;l<1024;++l)if(-1==h.indexOf(h[k]+"_"+l)){h[k]+="_"+l;break}}}}var o=cz(a["!ref"]),p=[],q=[],r=[];for(k=0;k<=o.e.c-o.s.c;++k){var s="",t="",u=0,v=[];for(l=0;l<i.length;++l)null!=i[l][k]&&v.push(i[l][k]);if(0==v.length||null==h[k]){p[k]="?";continue}for(l=0;l<v.length;++l){switch(typeof v[l]){case"number":t="B";break;case"string":default:t="C";break;case"boolean":t="L";break;case"object":t=v[l]instanceof Date?"D":"C"}u=Math.max(u,String(v[l]).length),s=s&&s!=t?"C":t}u>250&&(u=250),"C"==(t=((j[k]||{}).DBF||{}).type)&&j[k].DBF.len>u&&(u=j[k].DBF.len),"B"==s&&"N"==t&&(s="N",r[k]=j[k].DBF.dec,u=j[k].DBF.len),q[k]="C"==s||"N"==t?u:d[s]||0,n+=q[k],p[k]=s}var w=f.next(32);for(w.write_shift(4,0x13021130),w.write_shift(4,i.length),w.write_shift(2,296+32*m),w.write_shift(2,n),k=0;k<4;++k)w.write_shift(4,0);for(w.write_shift(4,(+b[G]||3)<<8),k=0,l=0;k<h.length;++k)if(null!=h[k]){var x=f.next(32),y=(h[k].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);x.write_shift(1,y,"sbcs"),x.write_shift(1,"?"==p[k]?"C":p[k],"sbcs"),x.write_shift(4,l),x.write_shift(1,q[k]||d[p[k]]||0),x.write_shift(1,r[k]||0),x.write_shift(1,2),x.write_shift(4,0),x.write_shift(1,0),x.write_shift(4,0),x.write_shift(4,0),l+=q[k]||d[p[k]]||0}var z=f.next(264);for(z.write_shift(4,13),k=0;k<65;++k)z.write_shift(4,0);for(k=0;k<i.length;++k){var A=f.next(n);for(A.write_shift(1,0),l=0;l<h.length;++l)if(null!=h[l])switch(p[l]){case"L":A.write_shift(1,null==i[k][l]?63:i[k][l]?84:70);break;case"B":A.write_shift(8,i[k][l]||0,"f");break;case"N":var B="0";for("number"==typeof i[k][l]&&(B=i[k][l].toFixed(r[l]||0)),m=0;m<q[l]-B.length;++m)A.write_shift(1,32);A.write_shift(1,B,"sbcs");break;case"D":i[k][l]?(A.write_shift(4,("0000"+i[k][l].getFullYear()).slice(-4),"sbcs"),A.write_shift(2,("00"+(i[k][l].getMonth()+1)).slice(-2),"sbcs"),A.write_shift(2,("00"+i[k][l].getDate()).slice(-2),"sbcs")):A.write_shift(8,"00000000","sbcs");break;case"C":var C=String(null!=i[k][l]?i[k][l]:"").slice(0,q[l]);for(A.write_shift(1,C,"sbcs"),m=0;m<q[l]-C.length;++m)A.write_shift(1,32)}}return f.next(1).write_shift(1,26),f.end()}}}(),dL=function(){var a={AA:"\xc0",BA:"\xc1",CA:"\xc2",DA:195,HA:"\xc4",JA:197,AE:"\xc8",BE:"\xc9",CE:"\xca",HE:"\xcb",AI:"\xcc",BI:"\xcd",CI:"\xce",HI:"\xcf",AO:"\xd2",BO:"\xd3",CO:"\xd4",DO:213,HO:"\xd6",AU:"\xd9",BU:"\xda",CU:"\xdb",HU:"\xdc",Aa:"\xe0",Ba:"\xe1",Ca:"\xe2",Da:227,Ha:"\xe4",Ja:229,Ae:"\xe8",Be:"\xe9",Ce:"\xea",He:"\xeb",Ai:"\xec",Bi:"\xed",Ci:"\xee",Hi:"\xef",Ao:"\xf2",Bo:"\xf3",Co:"\xf4",Do:245,Ho:"\xf6",Au:"\xf9",Bu:"\xfa",Cu:"\xfb",Hu:"\xfc",KC:"\xc7",Kc:"\xe7",q:"\xe6",z:"œ",a:"\xc6",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},b=RegExp("\x1bN("+aS(a).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),c=function(b,c){var d=a[c];return"number"==typeof d?Q(d):d},d=function(a,b,c){var d=b.charCodeAt(0)-32<<4|c.charCodeAt(0)-48;return 59==d?a:Q(d)};function f(a,f){var g,h=a.split(/[\n\r]+/),i=-1,j=-1,k=0,l=0,m=[],n=[],o=null,p={},q=[],r=[],s=[],t=0;for(+f.codepage>=0&&K(+f.codepage);k!==h.length;++k){t=0;var u,v=h[k].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,d).replace(b,c),w=v.replace(/;;/g,"\0").split(";").map(function(a){return a.replace(/\u0000/g,";")}),x=w[0];if(v.length>0)switch(x){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==w[1].charAt(0)&&n.push(v.slice(3).replace(/;;/g,";"));break;case"C":var y=!1,z=!1,A=!1,B=!1,C=-1,D=-1;for(l=1;l<w.length;++l)switch(w[l].charAt(0)){case"A":case"G":break;case"X":j=parseInt(w[l].slice(1))-1,z=!0;break;case"Y":for(i=parseInt(w[l].slice(1))-1,z||(j=0),g=m.length;g<=i;++g)m[g]=[];break;case"K":'"'===(u=w[l].slice(1)).charAt(0)?u=u.slice(1,u.length-1):"TRUE"===u?u=!0:"FALSE"===u?u=!1:isNaN(a5(u))?isNaN(a7(u).getDate())||(u=a1(u)):(u=a5(u),null!==o&&aI(o)&&(u=aZ(u))),void 0!==e&&"string"==typeof u&&"string"!=(f||{}).type&&(f||{}).codepage&&(u=e.utils.decode(f.codepage,u)),y=!0;break;case"E":B=!0;var E=et(w[l].slice(1),{r:i,c:j});m[i][j]=[m[i][j],E];break;case"S":A=!0,m[i][j]=[m[i][j],"S5S"];break;case"R":C=parseInt(w[l].slice(1))-1;break;case"C":D=parseInt(w[l].slice(1))-1;break;default:if(f&&f.WTF)throw Error("SYLK bad record "+v)}if(y&&(m[i][j]&&2==m[i][j].length?m[i][j][0]=u:m[i][j]=u,o=null),A){if(B)throw Error("SYLK shared formula cannot have own formula");var F=C>-1&&m[C][D];if(!F||!F[1])throw Error("SYLK shared formula cannot find base");m[i][j][1]=ew(F[1],{r:i-C,c:j-D})}break;case"F":var G=0;for(l=1;l<w.length;++l)switch(w[l].charAt(0)){case"X":j=parseInt(w[l].slice(1))-1,++G;break;case"Y":for(i=parseInt(w[l].slice(1))-1,g=m.length;g<=i;++g)m[g]=[];break;case"M":t=parseInt(w[l].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":o=n[parseInt(w[l].slice(1))];break;case"W":for(g=parseInt((s=w[l].slice(1).split(" "))[0],10);g<=parseInt(s[1],10);++g)t=parseInt(s[2],10),r[g-1]=0===t?{hidden:!0}:{wch:t},ed(r[g-1]);break;case"C":r[j=parseInt(w[l].slice(1))-1]||(r[j]={});break;case"R":q[i=parseInt(w[l].slice(1))-1]||(q[i]={}),t>0?(q[i].hpt=t,q[i].hpx=ee(t)):0===t&&(q[i].hidden=!0);break;default:if(f&&f.WTF)throw Error("SYLK bad record "+v)}G<1&&(o=null);break;default:if(f&&f.WTF)throw Error("SYLK bad record "+v)}}return q.length>0&&(p["!rows"]=q),r.length>0&&(p["!cols"]=r),f&&f.sheetRows&&(m=m.slice(0,f.sheetRows)),[m,p]}function g(a,b){var c=function(a,b){switch(b.type){case"base64":return f(T(a),b);case"binary":return f(a,b);case"buffer":return f(U&&Buffer.isBuffer(a)?a.toString("binary"):Z(a),b);case"array":return f(a2(a),b)}throw Error("Unrecognized type "+b.type)}(a,b),d=c[0],e=c[1],g=cD(d,b);return aS(e).forEach(function(a){g[a]=e[a]}),g}return a["|"]=254,{to_workbook:function(a,b){return cC(g(a,b),b)},to_sheet:g,from_sheet:function(a,b){var c,d=["ID;PWXL;N;E"],e=[],f=cz(a["!ref"]),g=Array.isArray(a);d.push("P;PGeneral"),d.push("F;P0;DG0G8;M255"),a["!cols"]&&a["!cols"].forEach(function(a,b){var c="F;W"+(b+1)+" "+(b+1)+" ";a.hidden?c+="0":("number"!=typeof a.width||a.wpx||(a.wpx=d8(a.width)),"number"!=typeof a.wpx||a.wch||(a.wch=d9(a.wpx)),"number"==typeof a.wch&&(c+=Math.round(a.wch)))," "!=c.charAt(c.length-1)&&d.push(c)}),a["!rows"]&&a["!rows"].forEach(function(a,b){var c="F;";a.hidden?c+="M0;":a.hpt?c+="M"+20*a.hpt+";":a.hpx&&(c+="M"+20*(96*a.hpx/96)+";"),c.length>2&&d.push(c+"R"+(b+1))}),d.push("B;Y"+(f.e.r-f.s.r+1)+";X"+(f.e.c-f.s.c+1)+";D"+[f.s.c,f.s.r,f.e.c,f.e.r].join(" "));for(var h=f.s.r;h<=f.e.r;++h)for(var i=f.s.c;i<=f.e.c;++i){var j=cw({r:h,c:i});(c=g?(a[h]||[])[i]:a[j])&&(null!=c.v||c.f&&!c.F)&&e.push(function(a,b,c,d){var e="C;Y"+(c+1)+";X"+(d+1)+";K";switch(a.t){case"n":e+=a.v||0,a.f&&!a.F&&(e+=";E"+ev(a.f,{r:c,c:d}));break;case"b":e+=a.v?"TRUE":"FALSE";break;case"e":e+=a.w||a.v;break;case"d":e+='"'+(a.w||a.v)+'"';break;case"s":e+='"'+a.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return e}(c,0,h,i,b))}return d.join("\r\n")+"\r\n"+e.join("\r\n")+"\r\nE\r\n"}}}(),dM=function(){var a,b;function c(a,b){for(var c=a.split("\n"),d=-1,e=-1,f=0,g=[];f!==c.length;++f){if("BOT"===c[f].trim()){g[++d]=[],e=0;continue}if(!(d<0)){for(var h=c[f].trim().split(","),i=h[0],j=h[1],k=c[++f]||"";1&(k.match(/["]/g)||[]).length&&f<c.length-1;)k+="\n"+c[++f];switch(k=k.trim(),+i){case -1:if("BOT"===k){g[++d]=[],e=0;continue}if("EOD"!==k)throw Error("Unrecognized DIF special command "+k);break;case 0:"TRUE"===k?g[d][e]=!0:"FALSE"===k?g[d][e]=!1:isNaN(a5(j))?isNaN(a7(j).getDate())?g[d][e]=j:g[d][e]=a1(j):g[d][e]=a5(j),++e;break;case 1:(k=(k=k.slice(1,k.length-1)).replace(/""/g,'"'))&&k.match(/^=".*"$/)&&(k=k.slice(2,-1)),g[d][e++]=""!==k?k:null}if("EOD"===k)break}}return b&&b.sheetRows&&(g=g.slice(0,b.sheetRows)),g}function d(a,b){return cD(function(a,b){switch(b.type){case"base64":return c(T(a),b);case"binary":return c(a,b);case"buffer":return c(U&&Buffer.isBuffer(a)?a.toString("binary"):Z(a),b);case"array":return c(a2(a),b)}throw Error("Unrecognized type "+b.type)}(a,b),b)}return{to_workbook:function(a,b){return cC(d(a,b),b)},to_sheet:d,from_sheet:(a=function(a,b,c,d,e){a.push(b),a.push(c+","+d),a.push('"'+e.replace(/"/g,'""')+'"')},b=function(a,b,c,d){a.push(b+","+c),a.push(1==b?'"'+d.replace(/"/g,'""')+'"':d)},function(c){var d,e=[],f=cz(c["!ref"]),g=Array.isArray(c);a(e,"TABLE",0,1,"sheetjs"),a(e,"VECTORS",0,f.e.r-f.s.r+1,""),a(e,"TUPLES",0,f.e.c-f.s.c+1,""),a(e,"DATA",0,0,"");for(var h=f.s.r;h<=f.e.r;++h){b(e,-1,0,"BOT");for(var i=f.s.c;i<=f.e.c;++i){var j=cw({r:h,c:i});if(!(d=g?(c[h]||[])[i]:c[j])){b(e,1,0,"");continue}switch(d.t){case"n":var k=d.w;k||null==d.v||(k=d.v),null==k?!d.f||d.F?b(e,1,0,""):b(e,1,0,"="+d.f):b(e,0,k,"V");break;case"b":b(e,0,+!!d.v,d.v?"TRUE":"FALSE");break;case"s":b(e,1,0,isNaN(d.v)?d.v:'="'+d.v+'"');break;case"d":d.w||(d.w=aL(d.z||al[14],aV(a1(d.v)))),b(e,0,d.w,"V");break;default:b(e,1,0,"")}}}return b(e,-1,0,"EOD"),e.join("\r\n")})}}(),dN=function(){function a(a){return a.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function b(a,b){return cD(function(a,b){for(var c=a.split("\n"),d=-1,e=-1,f=0,g=[];f!==c.length;++f){var h=c[f].trim().split(":");if("cell"===h[0]){var i=cv(h[1]);if(g.length<=i.r)for(d=g.length;d<=i.r;++d)g[d]||(g[d]=[]);switch(d=i.r,e=i.c,h[2]){case"t":g[d][e]=h[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":g[d][e]=+h[3];break;case"vtf":var j=h[h.length-1];case"vtc":"nl"===h[3]?g[d][e]=!!+h[4]:g[d][e]=+h[4],"vtf"==h[2]&&(g[d][e]=[g[d][e],j])}}}return b&&b.sheetRows&&(g=g.slice(0,b.sheetRows)),g}(a,b),b)}var c="--SocialCalcSpreadsheetControlSave\nContent-type: text/plain; charset=UTF-8\n";return{to_workbook:function(a,c){return cC(b(a,c),c)},to_sheet:b,from_sheet:function(b){return["socialcalc:version:1.5\nMIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave",c,"# SocialCalc Spreadsheet Control Save\npart:sheet",c,function(b){if(!b||!b["!ref"])return"";for(var c,d=[],e=[],f="",g=cx(b["!ref"]),h=Array.isArray(b),i=g.s.r;i<=g.e.r;++i)for(var j=g.s.c;j<=g.e.c;++j)if(f=cw({r:i,c:j}),(c=h?(b[i]||[])[j]:b[f])&&null!=c.v&&"z"!==c.t){switch(e=["cell",f,"t"],c.t){case"s":case"str":e.push(a(c.v));break;case"n":c.f?(e[2]="vtf",e[3]="n",e[4]=c.v,e[5]=a(c.f)):(e[2]="v",e[3]=c.v);break;case"b":e[2]="vt"+(c.f?"f":"c"),e[3]="nl",e[4]=c.v?"1":"0",e[5]=a(c.f||(c.v?"TRUE":"FALSE"));break;case"d":var k=aV(a1(c.v));e[2]="vtc",e[3]="nd",e[4]=""+k,e[5]=c.w||aL(c.z||al[14],k);break;case"e":continue}d.push(e.join(":"))}return d.push("sheet:c:"+(g.e.c-g.s.c+1)+":r:"+(g.e.r-g.s.r+1)+":tvf:1"),d.push("valueformat:1:text-wiki"),d.join("\n")}(b),"--SocialCalcSpreadsheetControlSave--"].join("\n")}}}(),dO=function(){function a(a,b,c,d,e){e.raw?b[c][d]=a:""===a||("TRUE"===a?b[c][d]=!0:"FALSE"===a?b[c][d]=!1:isNaN(a5(a))?isNaN(a7(a).getDate())?b[c][d]=a:b[c][d]=a1(a):b[c][d]=a5(a))}var b={44:",",9:"	",59:";",124:"|"},c={44:3,9:2,59:1,124:0};function d(a){for(var d={},e=!1,f=0,g=0;f<a.length;++f)34==(g=a.charCodeAt(f))?e=!e:!e&&g in b&&(d[g]=(d[g]||0)+1);for(f in g=[],d)Object.prototype.hasOwnProperty.call(d,f)&&g.push([d[f],f]);if(!g.length)for(f in d=c)Object.prototype.hasOwnProperty.call(d,f)&&g.push([d[f],f]);return g.sort(function(a,b){return a[0]-b[0]||c[a[1]]-c[b[1]]}),b[g.pop()[1]]||44}function f(b,c){var f,g="",h="string"==c.type?[0,0,0,0]:fU(b,c);switch(c.type){case"base64":g=T(b);break;case"binary":case"string":g=b;break;case"buffer":g=65001==c.codepage?b.toString("utf8"):c.codepage&&void 0!==e?e.utils.decode(c.codepage,b):U&&Buffer.isBuffer(b)?b.toString("binary"):Z(b);break;case"array":g=a2(b);break;default:throw Error("Unrecognized type "+c.type)}return(239==h[0]&&187==h[1]&&191==h[2]?g=bC(g.slice(3)):"string"!=c.type&&"buffer"!=c.type&&65001==c.codepage?g=bC(g):"binary"==c.type&&void 0!==e&&c.codepage&&(g=e.utils.decode(c.codepage,e.utils.encode(28591,g))),"socialcalc:version:"==g.slice(0,19))?dN.to_sheet("string"==c.type?g:bC(g),c):(f=g,!(c&&c.PRN)||c.FS||"sep="==f.slice(0,4)||f.indexOf("	")>=0||f.indexOf(",")>=0||f.indexOf(";")>=0?function(a,b){var c,e=b||{},f="",g=e.dense?[]:{},h={s:{c:0,r:0},e:{c:0,r:0}};"sep="==a.slice(0,4)?13==a.charCodeAt(5)&&10==a.charCodeAt(6)?(f=a.charAt(4),a=a.slice(7)):13==a.charCodeAt(5)||10==a.charCodeAt(5)?(f=a.charAt(4),a=a.slice(6)):f=d(a.slice(0,1024)):f=e&&e.FS?e.FS:d(a.slice(0,1024));var i=0,j=0,k=0,l=0,m=0,n=f.charCodeAt(0),o=!1,p=0,q=a.charCodeAt(0);a=a.replace(/\r\n/mg,"\n");var r=null!=e.dateNF?RegExp("^"+("number"==typeof(c=e.dateNF)?al[c]:c).replace(aP,"(\\d+)")+"$"):null;function s(){var b=a.slice(l,m),c={};if('"'==b.charAt(0)&&'"'==b.charAt(b.length-1)&&(b=b.slice(1,-1).replace(/""/g,'"')),0===b.length)c.t="z";else if(e.raw)c.t="s",c.v=b;else if(0===b.trim().length)c.t="s",c.v=b;else if(61==b.charCodeAt(0))34==b.charCodeAt(1)&&34==b.charCodeAt(b.length-1)?(c.t="s",c.v=b.slice(2,-1).replace(/""/g,'"')):1!=b.length?(c.t="n",c.f=b.slice(1)):(c.t="s",c.v=b);else if("TRUE"==b)c.t="b",c.v=!0;else if("FALSE"==b)c.t="b",c.v=!1;else if(isNaN(k=a5(b)))if(!isNaN(a7(b).getDate())||r&&b.match(r)){c.z=e.dateNF||al[14];var d,f,o,s,t,u,v,w,x,y,z=0;r&&b.match(r)&&(d=e.dateNF,f=b.match(r)||[],o=-1,s=-1,t=-1,u=-1,v=-1,w=-1,(d.match(aP)||[]).forEach(function(a,b){var c=parseInt(f[b+1],10);switch(a.toLowerCase().charAt(0)){case"y":o=c;break;case"d":t=c;break;case"h":u=c;break;case"s":w=c;break;case"m":u>=0?v=c:s=c}}),w>=0&&-1==v&&s>=0&&(v=s,s=-1),7==(x=(""+(o>=0?o:new Date().getFullYear())).slice(-4)+"-"+("00"+(s>=1?s:1)).slice(-2)+"-"+("00"+(t>=1?t:1)).slice(-2)).length&&(x="0"+x),8==x.length&&(x="20"+x),y=("00"+(u>=0?u:0)).slice(-2)+":"+("00"+(v>=0?v:0)).slice(-2)+":"+("00"+(w>=0?w:0)).slice(-2),b=-1==u&&-1==v&&-1==w?x:-1==o&&-1==s&&-1==t?y:x+"T"+y,z=1),e.cellDates?(c.t="d",c.v=a1(b,z)):(c.t="n",c.v=aV(a1(b,z))),!1!==e.cellText&&(c.w=aL(c.z,c.v instanceof Date?aV(c.v):c.v)),e.cellNF||delete c.z}else c.t="s",c.v=b;else c.t="n",!1!==e.cellText&&(c.w=b),c.v=k;if("z"==c.t||(e.dense?(g[i]||(g[i]=[]),g[i][j]=c):g[cw({c:j,r:i})]=c),l=m+1,q=a.charCodeAt(l),h.e.c<j&&(h.e.c=j),h.e.r<i&&(h.e.r=i),p==n)++j;else if(j=0,++i,e.sheetRows&&e.sheetRows<=i)return!0}a:for(;m<a.length;++m)switch(p=a.charCodeAt(m)){case 34:34===q&&(o=!o);break;case n:case 10:case 13:if(!o&&s())break a}return m-l>0&&s(),g["!ref"]=cy(h),g}(f,c):cD(function(b,c){var d=c||{},e=[];if(!b||0===b.length)return e;for(var f=b.split(/[\r\n]/),g=f.length-1;g>=0&&0===f[g].length;)--g;for(var h=10,i=0,j=0;j<=g;++j)-1==(i=f[j].indexOf(" "))?i=f[j].length:i++,h=Math.max(h,i);for(j=0;j<=g;++j){e[j]=[];var k=0;for(a(f[j].slice(0,h).trim(),e,j,k,d),k=1;k<=(f[j].length-h)/10+1;++k)a(f[j].slice(h+(k-1)*10,h+10*k).trim(),e,j,k,d)}return d.sheetRows&&(e=e.slice(0,d.sheetRows)),e}(f,c),c))}return{to_workbook:function(a,b){return cC(f(a,b),b)},to_sheet:f,from_sheet:function(a){for(var b,c=[],d=cz(a["!ref"]),e=Array.isArray(a),f=d.s.r;f<=d.e.r;++f){for(var g=[],h=d.s.c;h<=d.e.c;++h){var i=cw({r:f,c:h});if(!(b=e?(a[f]||[])[h]:a[i])||null==b.v){g.push("          ");continue}for(var j=(b.w||(cB(b),b.w)||"").slice(0,10);j.length<10;)j+=" ";g.push(j+(0===h?" ":""))}c.push(g.join(""))}return c.join("\n")}}}(),dP=function(){function a(a,b,c){if(a){ci(a,a.l||0);for(var d=c.Enum||l;a.l<a.length;){var e=a.read_shift(2),f=d[e]||d[65535],g=a.read_shift(2),h=a.l+g,i=f.f&&f.f(a,g,c);if(a.l=h,b(i,f,e))return}}}function b(b,c){if(!b)return b;var d=c||{},e=d.dense?[]:{},f="Sheet1",g="",h=0,i={},j=[],k=[],n={s:{r:0,c:0},e:{r:0,c:0}},o=d.sheetRows||0;if(0==b[2]&&(8==b[3]||9==b[3])&&b.length>=16&&5==b[14]&&108===b[15])throw Error("Unsupported Works 3 for Mac file");if(2==b[2])d.Enum=l,a(b,function(a,b,c){switch(c){case 0:d.vers=a,a>=4096&&(d.qpro=!0);break;case 6:n=a;break;case 204:a&&(g=a);break;case 222:g=a;break;case 15:case 51:d.qpro||(a[1].v=a[1].v.slice(1));case 13:case 14:case 16:14==c&&(112&a[2])==112&&(15&a[2])>1&&(15&a[2])<15&&(a[1].z=d.dateNF||al[14],d.cellDates&&(a[1].t="d",a[1].v=aZ(a[1].v))),d.qpro&&a[3]>h&&(e["!ref"]=cy(n),i[f]=e,j.push(f),e=d.dense?[]:{},n={s:{r:0,c:0},e:{r:0,c:0}},h=a[3],f=g||"Sheet"+(h+1),g="");var k=d.dense?(e[a[0].r]||[])[a[0].c]:e[cw(a[0])];if(k){k.t=a[1].t,k.v=a[1].v,null!=a[1].z&&(k.z=a[1].z),null!=a[1].f&&(k.f=a[1].f);break}d.dense?(e[a[0].r]||(e[a[0].r]=[]),e[a[0].r][a[0].c]=a[1]):e[cw(a[0])]=a[1]}},d);else if(26==b[2]||14==b[2])d.Enum=m,14==b[2]&&(d.qpro=!0,b.l=0),a(b,function(a,b,c){switch(c){case 204:f=a;break;case 22:a[1].v=a[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(a[3]>h&&(e["!ref"]=cy(n),i[f]=e,j.push(f),e=d.dense?[]:{},n={s:{r:0,c:0},e:{r:0,c:0}},f="Sheet"+((h=a[3])+1)),o>0&&a[0].r>=o)break;d.dense?(e[a[0].r]||(e[a[0].r]=[]),e[a[0].r][a[0].c]=a[1]):e[cw(a[0])]=a[1],n.e.c<a[0].c&&(n.e.c=a[0].c),n.e.r<a[0].r&&(n.e.r=a[0].r);break;case 27:a[14e3]&&(k[a[14e3][0]]=a[14e3][1]);break;case 1537:k[a[0]]=a[1],a[0]==h&&(f=a[1])}},d);else throw Error("Unrecognized LOTUS BOF "+b[2]);if(e["!ref"]=cy(n),i[g||f]=e,j.push(g||f),!k.length)return{SheetNames:j,Sheets:i};for(var p={},q=[],r=0;r<k.length;++r)i[j[r]]?(q.push(k[r]||j[r]),p[k[r]]=i[k[r]]||i[j[r]]):(q.push(k[r]),p[k[r]]={"!ref":"A1"});return{SheetNames:q,Sheets:p}}function c(a,b,c){var d=[{c:0,r:0},{t:"n",v:0},0,0];return c.qpro&&20768!=c.vers?(d[0].c=a.read_shift(1),d[3]=a.read_shift(1),d[0].r=a.read_shift(2),a.l+=2):(d[2]=a.read_shift(1),d[0].c=a.read_shift(2),d[0].r=a.read_shift(2)),d}function d(a,b,d){var e=a.l+b,f=c(a,b,d);if(f[1].t="s",20768==d.vers){a.l++;var g=a.read_shift(1);return f[1].v=a.read_shift(g,"utf8"),f}return d.qpro&&a.l++,f[1].v=a.read_shift(e-a.l,"cstr"),f}function e(a,b,c){var d=32768&b;return b&=-32769,b=(d?a:0)+(b>=8192?b-16384:b),(d?"":"$")+(c?cu(b):cs(b))}var f={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},g=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function h(a){var b=[{c:0,r:0},{t:"n",v:0},0];return b[0].r=a.read_shift(2),b[3]=a[a.l++],b[0].c=a[a.l++],b}function i(a,b){var c=h(a,b),d=a.read_shift(4),e=a.read_shift(4),f=a.read_shift(2);if(65535==f)return 0===d&&0xc0000000===e?(c[1].t="e",c[1].v=15):0===d&&0xd0000000===e?(c[1].t="e",c[1].v=42):c[1].v=0,c;var g=32768&f;return f=(32767&f)-16446,c[1].v=(1-2*g)*(e*Math.pow(2,f+32)+d*Math.pow(2,f)),c}function j(a,b){var c=h(a,b),d=a.read_shift(8,"f");return c[1].v=d,c}function k(a,b){return 0==a[a.l+b-1]?a.read_shift(b,"cstr"):""}var l={0:{n:"BOF",f:dg},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(a,b,c){var d={s:{c:0,r:0},e:{c:0,r:0}};return 8==b&&c.qpro?(d.s.c=a.read_shift(1),a.l++,d.s.r=a.read_shift(2),d.e.c=a.read_shift(1),a.l++,d.e.r=a.read_shift(2)):(d.s.c=a.read_shift(2),d.s.r=a.read_shift(2),12==b&&c.qpro&&(a.l+=2),d.e.c=a.read_shift(2),d.e.r=a.read_shift(2),12==b&&c.qpro&&(a.l+=2),65535==d.s.c&&(d.s.c=d.e.c=d.s.r=d.e.r=0)),d}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(a,b,d){var e=c(a,b,d);return e[1].v=a.read_shift(2,"i"),e}},14:{n:"NUMBER",f:function(a,b,d){var e=c(a,b,d);return e[1].v=a.read_shift(8,"f"),e}},15:{n:"LABEL",f:d},16:{n:"FORMULA",f:function(a,b,d){var h=a.l+b,i=c(a,b,d);if(i[1].v=a.read_shift(8,"f"),d.qpro)a.l=h;else{var j=a.read_shift(2);(function(a,b){ci(a,0);for(var c=[],d=0,h="",i="",j="",k="";a.l<a.length;){var l=a[a.l++];switch(l){case 0:c.push(a.read_shift(8,"f"));break;case 1:i=e(b[0].c,a.read_shift(2),!0),h=e(b[0].r,a.read_shift(2),!1),c.push(i+h);break;case 2:var m=e(b[0].c,a.read_shift(2),!0),n=e(b[0].r,a.read_shift(2),!1);i=e(b[0].c,a.read_shift(2),!0),h=e(b[0].r,a.read_shift(2),!1),c.push(m+n+":"+i+h);break;case 3:if(a.l<a.length)return void console.error("WK1 premature formula end");break;case 4:c.push("("+c.pop()+")");break;case 5:c.push(a.read_shift(2));break;case 6:for(var o="";l=a[a.l++];)o+=String.fromCharCode(l);c.push('"'+o.replace(/"/g,'""')+'"');break;case 8:c.push("-"+c.pop());break;case 23:c.push("+"+c.pop());break;case 22:c.push("NOT("+c.pop()+")");break;case 20:case 21:k=c.pop(),j=c.pop(),c.push(["AND","OR"][l-20]+"("+j+","+k+")");break;default:if(l<32&&g[l])k=c.pop(),j=c.pop(),c.push(j+g[l]+k);else if(f[l]){if(69==(d=f[l][1])&&(d=a[a.l++]),d>c.length)return void console.error("WK1 bad formula parse 0x"+l.toString(16)+":|"+c.join("|")+"|");var p=c.slice(-d);c.length-=d,c.push(f[l][0]+"("+p.join(",")+")")}else if(l<=7)return console.error("WK1 invalid opcode "+l.toString(16));else if(l<=24)return console.error("WK1 unsupported op "+l.toString(16));else if(l<=30)return console.error("WK1 invalid opcode "+l.toString(16));else if(l<=115)return console.error("WK1 unsupported function opcode "+l.toString(16));else return console.error("WK1 unrecognized opcode "+l.toString(16))}}1==c.length?b[1].f=""+c[0]:console.error("WK1 bad formula parse |"+c.join("|")+"|")})(a.slice(a.l,a.l+j),i),a.l+=j}return i}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:d},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:k},222:{n:"SHEETNAMELP",f:function(a,b){var c=a[a.l++];c>b-1&&(c=b-1);for(var d="";d.length<c;)d+=String.fromCharCode(a[a.l++]);return d}},65535:{n:""}},m={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(a,b){var c=h(a,b);return c[1].t="s",c[1].v=a.read_shift(b-4,"cstr"),c}},23:{n:"NUMBER17",f:i},24:{n:"NUMBER18",f:function(a,b){var c=h(a,b);c[1].v=a.read_shift(2);var d=c[1].v>>1;if(1&c[1].v)switch(7&d){case 0:d=(d>>3)*5e3;break;case 1:d=(d>>3)*500;break;case 2:d=(d>>3)/20;break;case 3:d=(d>>3)/200;break;case 4:d=(d>>3)/2e3;break;case 5:d=(d>>3)/2e4;break;case 6:d=(d>>3)/16;break;case 7:d=(d>>3)/64}return c[1].v=d,c}},25:{n:"FORMULA19",f:function(a,b){var c=i(a,14);return a.l+=b-14,c}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(a,b){for(var c={},d=a.l+b;a.l<d;){var e=a.read_shift(2);if(14e3==e){for(c[e]=[0,""],c[e][0]=a.read_shift(2);a[a.l];)c[e][1]+=String.fromCharCode(a[a.l]),a.l++;a.l++}}return c}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(a,b){var c=h(a,b),d=a.read_shift(4);return c[1].v=d>>6,c}},38:{n:"??"},39:{n:"NUMBER27",f:j},40:{n:"FORMULA28",f:function(a,b){var c=j(a,14);return a.l+=b-10,c}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:k},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(a,b,c){if(c.qpro&&!(b<21)){var d=a.read_shift(1);return a.l+=17,a.l+=1,a.l+=2,[d,a.read_shift(b-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(a,b){var c,d,e,f,g=b||{};if(+g.codepage>=0&&K(+g.codepage),"string"==g.type)throw Error("Cannot write WK1 to JS string");var h=cm(),i=cz(a["!ref"]),j=Array.isArray(a),k=[];fD(h,0,(c=1030,(d=ck(2)).write_shift(2,1030),d)),fD(h,6,(e=i,(f=ck(8)).write_shift(2,e.s.c),f.write_shift(2,e.s.r),f.write_shift(2,e.e.c),f.write_shift(2,e.e.r),f));for(var l=Math.min(i.e.r,8191),m=i.s.r;m<=l;++m)for(var n=cs(m),o=i.s.c;o<=i.e.c;++o){m===i.s.r&&(k[o]=cu(o));var p=k[o]+n,q=j?(a[m]||[])[o]:a[p];q&&"z"!=q.t&&("n"==q.t?(0|q.v)==q.v&&q.v>=-32768&&q.v<=32767?fD(h,13,function(a,b,c){var d=ck(7);return d.write_shift(1,255),d.write_shift(2,b),d.write_shift(2,a),d.write_shift(2,c,"i"),d}(m,o,q.v)):fD(h,14,function(a,b,c){var d=ck(13);return d.write_shift(1,255),d.write_shift(2,b),d.write_shift(2,a),d.write_shift(8,c,"f"),d}(m,o,q.v)):fD(h,15,function(a,b,c){var d=ck(7+c.length);d.write_shift(1,255),d.write_shift(2,b),d.write_shift(2,a),d.write_shift(1,39);for(var e=0;e<d.length;++e){var f=c.charCodeAt(e);d.write_shift(1,f>=128?95:f)}return d.write_shift(1,0),d}(m,o,cB(q).slice(0,239))))}return fD(h,1),h.end()},book_to_wk3:function(a,b){var c=b||{};if(+c.codepage>=0&&K(+c.codepage),"string"==c.type)throw Error("Cannot write WK3 to JS string");var d=cm();fD(d,0,function(a){var b=ck(26);b.write_shift(2,4096),b.write_shift(2,4),b.write_shift(4,0);for(var c=0,d=0,e=0,f=0;f<a.SheetNames.length;++f){var g=a.SheetNames[f],h=a.Sheets[g];if(h&&h["!ref"]){++e;var i=cx(h["!ref"]);c<i.e.r&&(c=i.e.r),d<i.e.c&&(d=i.e.c)}}return c>8191&&(c=8191),b.write_shift(2,c),b.write_shift(1,e),b.write_shift(1,d),b.write_shift(2,0),b.write_shift(2,0),b.write_shift(1,1),b.write_shift(1,2),b.write_shift(4,0),b.write_shift(4,0),b}(a));for(var e=0,f=0;e<a.SheetNames.length;++e)(a.Sheets[a.SheetNames[e]]||{})["!ref"]&&fD(d,27,function(a,b){var c=ck(5+a.length);c.write_shift(2,14e3),c.write_shift(2,b);for(var d=0;d<a.length;++d){var e=a.charCodeAt(d);c[c.l++]=e>127?95:e}return c[c.l++]=0,c}(a.SheetNames[e],f++));var g=0;for(e=0;e<a.SheetNames.length;++e){var h=a.Sheets[a.SheetNames[e]];if(h&&h["!ref"]){for(var i=cz(h["!ref"]),j=Array.isArray(h),k=[],l=Math.min(i.e.r,8191),m=i.s.r;m<=l;++m)for(var n=cs(m),o=i.s.c;o<=i.e.c;++o){m===i.s.r&&(k[o]=cu(o));var p=k[o]+n,q=j?(h[m]||[])[o]:h[p];q&&"z"!=q.t&&("n"==q.t?fD(d,23,function(a,b,c,d){var e=ck(14);if(e.write_shift(2,a),e.write_shift(1,c),e.write_shift(1,b),0==d)return e.write_shift(4,0),e.write_shift(4,0),e.write_shift(2,65535),e;var f=0,g=0,h=0,i=0;return d<0&&(f=1,d=-d),g=0|Math.log2(d),d/=Math.pow(2,g-31),(0x80000000&(i=d>>>0))==0&&(d/=2,++g,i=d>>>0),d-=i,i|=0x80000000,i>>>=0,d*=0x100000000,h=d>>>0,e.write_shift(4,h),e.write_shift(4,i),g+=16383+32768*!!f,e.write_shift(2,g),e}(m,o,g,q.v)):fD(d,22,function(a,b,c,d){var e=ck(6+d.length);e.write_shift(2,a),e.write_shift(1,c),e.write_shift(1,b),e.write_shift(1,39);for(var f=0;f<d.length;++f){var g=d.charCodeAt(f);e.write_shift(1,g>=128?95:g)}return e.write_shift(1,0),e}(m,o,g,cB(q).slice(0,239))))}++g}}return fD(d,1),d.end()},to_workbook:function(a,c){switch(c.type){case"base64":return b(Y(T(a)),c);case"binary":return b(Y(a),c);case"buffer":case"array":return b(a,c)}throw"Unsupported type "+c.type}}}(),dQ=function(){var a=bE("t"),b=bE("rPr");function c(c){var d=c.match(a);if(!d)return{t:"s",v:""};var e={t:"s",v:bs(d[1])},f=c.match(b);return f&&(e.s=function(a){var b={},c=a.match(bl),d=0,e=!1;if(c)for(;d!=c.length;++d){var f=bo(c[d]);switch(f[0].replace(/\w*:/g,"")){case"<condense":case"<extend":break;case"<shadow":if(!f.val)break;case"<shadow>":case"<shadow/>":b.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==f.val)break;b.cp=I[parseInt(f.val,10)];break;case"<outline":if(!f.val)break;case"<outline>":case"<outline/>":b.outline=1;break;case"</outline>":break;case"<rFont":b.name=f.val;break;case"<sz":b.sz=f.val;break;case"<strike":if(!f.val)break;case"<strike>":case"<strike/>":b.strike=1;break;case"</strike>":break;case"<u":if(!f.val)break;switch(f.val){case"double":b.uval="double";break;case"singleAccounting":b.uval="single-accounting";break;case"doubleAccounting":b.uval="double-accounting"}case"<u>":case"<u/>":b.u=1;break;case"</u>":break;case"<b":if("0"==f.val)break;case"<b>":case"<b/>":b.b=1;break;case"</b>":break;case"<i":if("0"==f.val)break;case"<i>":case"<i/>":b.i=1;break;case"</i>":case"<color>":case"<color/>":case"</color>":case"<family>":case"<family/>":case"</family>":case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":case"<scheme":case"<scheme>":case"<scheme/>":case"</scheme>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<color":f.rgb&&(b.color=f.rgb.slice(2,8));break;case"<family":b.family=f.val;break;case"<vertAlign":b.valign=f.val;break;case"<ext":e=!0;break;case"</ext>":e=!1;break;default:if(47!==f[0].charCodeAt(1)&&!e)throw Error("Unrecognized rich format "+f[0])}}return b}(f[1])),e}var d=/<(?:\w+:)?r>/g,e=/<\/(?:\w+:)?r>/;return function(a){return a.replace(d,"").split(e).map(c).filter(function(a){return a.v})}}(),dR=function(){var a=/(\r\n|\n)/g;function b(b){var c,d,e,f,g,h=[[],b.v,[]];return b.v?(b.s&&(c=b.s,d=h[0],e=h[2],f=[],c.u&&f.push("text-decoration: underline;"),c.uval&&f.push("text-underline-style:"+c.uval+";"),c.sz&&f.push("font-size:"+c.sz+"pt;"),c.outline&&f.push("text-effect: outline;"),c.shadow&&f.push("text-shadow: auto;"),d.push('<span style="'+f.join("")+'">'),c.b&&(d.push("<b>"),e.push("</b>")),c.i&&(d.push("<i>"),e.push("</i>")),c.strike&&(d.push("<s>"),e.push("</s>")),"superscript"==(g=c.valign||"")||"super"==g?g="sup":"subscript"==g&&(g="sub"),""!=g&&(d.push("<"+g+">"),e.push("</"+g+">")),e.push("</span>")),h[0].join("")+h[1].replace(a,"<br/>")+h[2].join("")):""}return function(a){return a.map(b).join("")}}(),dS=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,dT=/<(?:\w+:)?r>/,dU=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function dV(a,b){var c=!b||b.cellHTML,d={};return a?(a.match(/^\s*<(?:\w+:)?t[^>]*>/)?(d.t=bs(bC(a.slice(a.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),d.r=bC(a),c&&(d.h=bv(d.t))):a.match(dT)&&(d.r=bC(a),d.t=bs(bC((a.replace(dU,"").match(dS)||[]).join("").replace(bl,""))),c&&(d.h=dR(dQ(d.r)))),d):{t:""}}var dW=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,dX=/<(?:\w+:)?(?:si|sstItem)>/g,dY=/<\/(?:\w+:)?(?:si|sstItem)>/;function dZ(a){if(void 0!==e)return e.utils.encode(G,a);for(var b=[],c=a.split(""),d=0;d<c.length;++d)b[d]=c[d].charCodeAt(0);return b}function d$(a,b){var c={};return c.Major=a.read_shift(2),c.Minor=a.read_shift(2),b>=4&&(a.l+=b-4),c}function d_(a,b){var c=a.l+b,d={};d.Flags=63&a.read_shift(4),a.l+=4,d.AlgID=a.read_shift(4);var e=!1;switch(d.AlgID){case 26126:case 26127:case 26128:e=36==d.Flags;break;case 26625:e=4==d.Flags;break;case 0:e=16==d.Flags||4==d.Flags||36==d.Flags;break;default:throw"Unrecognized encryption algorithm: "+d.AlgID}if(!e)throw Error("Encryption Flags/AlgID mismatch");return d.AlgIDHash=a.read_shift(4),d.KeySize=a.read_shift(4),d.ProviderType=a.read_shift(4),a.l+=8,d.CSPName=a.read_shift(c-a.l>>1,"utf16le"),a.l=c,d}function d0(a,b){var c={},d=a.l+b;return a.l+=4,c.Salt=a.slice(a.l,a.l+16),a.l+=16,c.Verifier=a.slice(a.l,a.l+16),a.l+=16,a.read_shift(4),c.VerifierHash=a.slice(a.l,d),a.l=d,c}var d1=function(){var a=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],b=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],c=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],d=function(a,b){var c;return((c=a^b)/2|128*c)&255},e=function(a){for(var d=b[a.length-1],e=104,f=a.length-1;f>=0;--f)for(var g=a[f],h=0;7!=h;++h)64&g&&(d^=c[e]),g*=2,--e;return d};return function(b){for(var c,f,g,h=dZ(b),i=e(h),j=h.length,k=W(16),l=0;16!=l;++l)k[l]=0;for((1&j)==1&&(c=i>>8,k[j]=d(a[0],c),--j,c=255&i,f=h[h.length-1],k[j]=d(f,c));j>0;)--j,c=i>>8,k[j]=d(h[j],c),--j,c=255&i,k[j]=d(h[j],c);for(j=15,g=15-h.length;g>0;)c=i>>8,k[j]=d(a[g],c),--j,--g,c=255&i,k[j]=d(h[j],c),--j,--g;return k}}(),d2=function(a,b,c,d,e){var f,g;for(e||(e=b),d||(d=d1(a)),f=0;f!=b.length;++f)g=((g=b[f]^d[c])>>5|g<<3)&255,e[f]=g,++c;return[e,c,d]},d3=function(a){var b=0,c=d1(a);return function(a){var d=d2("",a,b,c);return b=d[1],d[0]}},d4=function(){function a(a,c){switch(c.type){case"base64":return b(T(a),c);case"binary":return b(a,c);case"buffer":return b(U&&Buffer.isBuffer(a)?a.toString("binary"):Z(a),c);case"array":return b(a2(a),c)}throw Error("Unrecognized type "+c.type)}function b(a,b){var c=(b||{}).dense?[]:{},d=a.match(/\\trowd.*?\\row\b/g);if(!d.length)throw Error("RTF missing table");var e={s:{c:0,r:0},e:{c:0,r:d.length-1}};return d.forEach(function(a,b){Array.isArray(c)&&(c[b]=[]);for(var d,f=/\\\w+\b/g,g=0,h=-1;d=f.exec(a);){if("\\cell"===d[0]){var i=a.slice(g,f.lastIndex-d[0].length);if(" "==i[0]&&(i=i.slice(1)),++h,i.length){var j={v:i,t:"s"};Array.isArray(c)?c[b][h]=j:c[cw({r:b,c:h})]=j}}g=f.lastIndex}h>e.e.c&&(e.e.c=h)}),c["!ref"]=cy(e),c}return{to_workbook:function(b,c){return cC(a(b,c),c)},to_sheet:a,from_sheet:function(a){for(var b,c=["{\\rtf1\\ansi"],d=cz(a["!ref"]),e=Array.isArray(a),f=d.s.r;f<=d.e.r;++f){c.push("\\trowd\\trautofit1");for(var g=d.s.c;g<=d.e.c;++g)c.push("\\cellx"+(g+1));for(c.push("\\pard\\intbl"),g=d.s.c;g<=d.e.c;++g){var h=cw({r:f,c:g});(b=e?(a[f]||[])[g]:a[h])&&(null!=b.v||b.f&&!b.F)&&(c.push(" "+(b.w||(cB(b),b.w))),c.push("\\cell"))}c.push("\\pard\\intbl\\row")}return c.join("")+"}"}}}();function d5(a){for(var b=0,c=1;3!=b;++b)c=256*c+(a[b]>255?255:a[b]<0?0:a[b]);return c.toString(16).toUpperCase().slice(1)}function d6(a,b){if(0===b)return a;var c,d=function(a){var b=a[0]/255,c=a[1]/255,d=a[2]/255,e=Math.max(b,c,d),f=Math.min(b,c,d),g=e-f;if(0===g)return[0,0,b];var h=0,i=0,j=e+f;switch(i=g/(j>1?2-j:j),e){case b:h=((c-d)/g+6)%6;break;case c:h=(d-b)/g+2;break;case d:h=(b-c)/g+4}return[h/6,i,j/2]}([parseInt((c=a.slice(+("#"===a[0])).slice(0,6)).slice(0,2),16),parseInt(c.slice(2,4),16),parseInt(c.slice(4,6),16)]);return b<0?d[2]=d[2]*(1+b):d[2]=1-(1-d[2])*(1-b),d5(function(a){var b,c=a[0],d=a[1],e=a[2],f=2*d*(e<.5?e:1-e),g=e-f/2,h=[g,g,g],i=6*c;if(0!==d)switch(0|i){case 0:case 6:b=f*i,h[0]+=f,h[1]+=b;break;case 1:b=f*(2-i),h[0]+=b,h[1]+=f;break;case 2:b=f*(i-2),h[1]+=f,h[2]+=b;break;case 3:b=f*(4-i),h[1]+=b,h[2]+=f;break;case 4:b=f*(i-4),h[2]+=f,h[0]+=b;break;case 5:b=f*(6-i),h[2]+=b,h[0]+=f}for(var j=0;3!=j;++j)h[j]=Math.round(255*h[j]);return h}(d))}var d7=6;function d8(a){return Math.floor((a+Math.round(128/d7)/256)*d7)}function d9(a){return Math.floor((a-5)/d7*100+.5)/100}function ea(a){return Math.round((a*d7+5)/d7*256)/256}function eb(a){return ea(d9(d8(a)))}function ec(a){var b=Math.abs(a-eb(a)),c=d7;if(b>.005)for(d7=1;d7<15;++d7)Math.abs(a-eb(a))<=b&&(b=Math.abs(a-eb(a)),c=d7);d7=c}function ed(a){a.width?(a.wpx=d8(a.width),a.wch=d9(a.wpx),a.MDW=d7):a.wpx?(a.wch=d9(a.wpx),a.width=ea(a.wch),a.MDW=d7):"number"==typeof a.wch&&(a.width=ea(a.wch),a.wpx=d8(a.width),a.MDW=d7),a.customWidth&&delete a.customWidth}function ee(a){return 96*a/96}var ef={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"},eg=["numFmtId","fillId","fontId","borderId","xfId"],eh=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"],ei=function(){var a=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,b=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,c=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,d=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,e=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(f,g,h){var i,j,k,l,m,n,o,p,q,r,s,t,u,v={};return f?((u=(f=f.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"")).match(a))&&function(a,b,c){b.NumberFmt=[];for(var d=aS(al),e=0;e<d.length;++e)b.NumberFmt[d[e]]=al[d[e]];var f=a[0].match(bl);if(f)for(e=0;e<f.length;++e){var g=bo(f[e]);switch(bp(g[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":case"</numFmt>":break;case"<numFmt":var h=bs(bC(g.formatCode)),i=parseInt(g.numFmtId,10);if(b.NumberFmt[i]=h,i>0){if(i>392){for(i=392;i>60&&null!=b.NumberFmt[i];--i);b.NumberFmt[i]=h}aM(h,i)}break;default:if(c.WTF)throw Error("unrecognized "+g[0]+" in numFmts")}}}(u,v,h),(u=f.match(d))&&(i=u,v.Fonts=[],j={},k=!1,(i[0].match(bl)||[]).forEach(function(a){var b=bo(a);switch(bp(b[0])){case"<fonts":case"<fonts>":case"</fonts>":case"<font":case"<font>":case"<name/>":case"</name>":case"<sz/>":case"</sz>":case"<vertAlign/>":case"</vertAlign>":case"<family/>":case"</family>":case"<scheme/>":case"</scheme>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"</font>":case"<font/>":v.Fonts.push(j),j={};break;case"<name":b.val&&(j.name=bC(b.val));break;case"<b":j.bold=b.val?bx(b.val):1;break;case"<b/>":j.bold=1;break;case"<i":j.italic=b.val?bx(b.val):1;break;case"<i/>":j.italic=1;break;case"<u":switch(b.val){case"none":j.underline=0;break;case"single":j.underline=1;break;case"double":j.underline=2;break;case"singleAccounting":j.underline=33;break;case"doubleAccounting":j.underline=34}break;case"<u/>":j.underline=1;break;case"<strike":j.strike=b.val?bx(b.val):1;break;case"<strike/>":j.strike=1;break;case"<outline":j.outline=b.val?bx(b.val):1;break;case"<outline/>":j.outline=1;break;case"<shadow":j.shadow=b.val?bx(b.val):1;break;case"<shadow/>":j.shadow=1;break;case"<condense":j.condense=b.val?bx(b.val):1;break;case"<condense/>":j.condense=1;break;case"<extend":j.extend=b.val?bx(b.val):1;break;case"<extend/>":j.extend=1;break;case"<sz":b.val&&(j.sz=+b.val);break;case"<vertAlign":b.val&&(j.vertAlign=b.val);break;case"<family":b.val&&(j.family=parseInt(b.val,10));break;case"<scheme":b.val&&(j.scheme=b.val);break;case"<charset":if("1"==b.val)break;b.codepage=I[parseInt(b.val,10)];break;case"<color":if(j.color||(j.color={}),b.auto&&(j.color.auto=bx(b.auto)),b.rgb)j.color.rgb=b.rgb.slice(-6);else if(b.indexed){j.color.index=parseInt(b.indexed,10);var c=cS[j.color.index];81==j.color.index&&(c=cS[1]),c||(c=cS[1]),j.color.rgb=c[0].toString(16)+c[1].toString(16)+c[2].toString(16)}else b.theme&&(j.color.theme=parseInt(b.theme,10),b.tint&&(j.color.tint=parseFloat(b.tint)),b.theme&&g.themeElements&&g.themeElements.clrScheme&&(j.color.rgb=d6(g.themeElements.clrScheme[j.color.theme].rgb,j.color.tint||0)));break;case"<AlternateContent":case"<ext":k=!0;break;case"</AlternateContent>":case"</ext>":k=!1;break;default:if(h&&h.WTF&&!k)throw Error("unrecognized "+b[0]+" in fonts")}})),(u=f.match(c))&&(l=u,v.Fills=[],m={},n=!1,(l[0].match(bl)||[]).forEach(function(a){var b=bo(a);switch(bp(b[0])){case"<fills":case"<fills>":case"</fills>":case"</fill>":case"<gradientFill>":case"<patternFill/>":case"</patternFill>":case"<bgColor/>":case"</bgColor>":case"<fgColor/>":case"</fgColor>":case"<stop":case"<stop/>":case"</stop>":case"<color":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<fill>":case"<fill":case"<fill/>":m={},v.Fills.push(m);break;case"<gradientFill":case"</gradientFill>":v.Fills.push(m),m={};break;case"<patternFill":case"<patternFill>":b.patternType&&(m.patternType=b.patternType);break;case"<bgColor":m.bgColor||(m.bgColor={}),b.indexed&&(m.bgColor.indexed=parseInt(b.indexed,10)),b.theme&&(m.bgColor.theme=parseInt(b.theme,10)),b.tint&&(m.bgColor.tint=parseFloat(b.tint)),b.rgb&&(m.bgColor.rgb=b.rgb.slice(-6));break;case"<fgColor":m.fgColor||(m.fgColor={}),b.theme&&(m.fgColor.theme=parseInt(b.theme,10)),b.tint&&(m.fgColor.tint=parseFloat(b.tint)),null!=b.rgb&&(m.fgColor.rgb=b.rgb.slice(-6));break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(h&&h.WTF&&!n)throw Error("unrecognized "+b[0]+" in fills")}})),(u=f.match(e))&&(o=u,v.Borders=[],p={},q=!1,(o[0].match(bl)||[]).forEach(function(a){var b=bo(a);switch(bp(b[0])){case"<borders":case"<borders>":case"</borders>":case"</border>":case"<left/>":case"<left":case"<left>":case"</left>":case"<right/>":case"<right":case"<right>":case"</right>":case"<top/>":case"<top":case"<top>":case"</top>":case"<bottom/>":case"<bottom":case"<bottom>":case"</bottom>":case"<diagonal":case"<diagonal>":case"<diagonal/>":case"</diagonal>":case"<horizontal":case"<horizontal>":case"<horizontal/>":case"</horizontal>":case"<vertical":case"<vertical>":case"<vertical/>":case"</vertical>":case"<start":case"<start>":case"<start/>":case"</start>":case"<end":case"<end>":case"<end/>":case"</end>":case"<color":case"<color>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<border":case"<border>":case"<border/>":p={},b.diagonalUp&&(p.diagonalUp=bx(b.diagonalUp)),b.diagonalDown&&(p.diagonalDown=bx(b.diagonalDown)),v.Borders.push(p);break;case"<ext":q=!0;break;case"</ext>":q=!1;break;default:if(h&&h.WTF&&!q)throw Error("unrecognized "+b[0]+" in borders")}})),(u=f.match(b))&&(r=u,v.CellXf=[],t=!1,(r[0].match(bl)||[]).forEach(function(a){var b=bo(a),c=0;switch(bp(b[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":case"</xf>":case"</alignment>":case"<protection":case"</protection>":case"<protection/>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<xf":case"<xf/>":for(s=b,delete s[0],c=0;c<eg.length;++c)s[eg[c]]&&(s[eg[c]]=parseInt(s[eg[c]],10));for(c=0;c<eh.length;++c)s[eh[c]]&&(s[eh[c]]=bx(s[eh[c]]));if(v.NumberFmt&&s.numFmtId>392){for(c=392;c>60;--c)if(v.NumberFmt[s.numFmtId]==v.NumberFmt[c]){s.numFmtId=c;break}}v.CellXf.push(s);break;case"<alignment":case"<alignment/>":var d={};b.vertical&&(d.vertical=b.vertical),b.horizontal&&(d.horizontal=b.horizontal),null!=b.textRotation&&(d.textRotation=b.textRotation),b.indent&&(d.indent=b.indent),b.wrapText&&(d.wrapText=bx(b.wrapText)),s.alignment=d;break;case"<AlternateContent":case"<ext":t=!0;break;case"</AlternateContent>":case"</ext>":t=!1;break;default:if(h&&h.WTF&&!t)throw Error("unrecognized "+b[0]+" in cellXfs")}})),v):v}}(),ej=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function ek(a,b,c){b.themeElements.clrScheme=[];var d={};(a[0].match(bl)||[]).forEach(function(a){var e=bo(a);switch(e[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":d.rgb=e.val;break;case"<a:sysClr":d.rgb=e.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===e[0].charAt(1)?(b.themeElements.clrScheme[ej.indexOf(e[0])]=d,d={}):d.name=e[0].slice(3,e[0].length-1);break;default:if(c&&c.WTF)throw Error("Unrecognized "+e[0]+" in clrScheme")}})}function el(){}function em(){}var en=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,eo=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,ep=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/,eq=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function er(a,b){a&&0!==a.length||(a=function(a,b){!1;!1;var c=[bi];return c[c.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',c[c.length]="<a:themeElements>",c[c.length]='<a:clrScheme name="Office">',c[c.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',c[c.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',c[c.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',c[c.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',c[c.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',c[c.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',c[c.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',c[c.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',c[c.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',c[c.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',c[c.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',c[c.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',c[c.length]="</a:clrScheme>",c[c.length]='<a:fontScheme name="Office">',c[c.length]="<a:majorFont>",c[c.length]='<a:latin typeface="Cambria"/>',c[c.length]='<a:ea typeface=""/>',c[c.length]='<a:cs typeface=""/>',c[c.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',c[c.length]='<a:font script="Hang" typeface="맑은 고딕"/>',c[c.length]='<a:font script="Hans" typeface="宋体"/>',c[c.length]='<a:font script="Hant" typeface="新細明體"/>',c[c.length]='<a:font script="Arab" typeface="Times New Roman"/>',c[c.length]='<a:font script="Hebr" typeface="Times New Roman"/>',c[c.length]='<a:font script="Thai" typeface="Tahoma"/>',c[c.length]='<a:font script="Ethi" typeface="Nyala"/>',c[c.length]='<a:font script="Beng" typeface="Vrinda"/>',c[c.length]='<a:font script="Gujr" typeface="Shruti"/>',c[c.length]='<a:font script="Khmr" typeface="MoolBoran"/>',c[c.length]='<a:font script="Knda" typeface="Tunga"/>',c[c.length]='<a:font script="Guru" typeface="Raavi"/>',c[c.length]='<a:font script="Cans" typeface="Euphemia"/>',c[c.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',c[c.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',c[c.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',c[c.length]='<a:font script="Thaa" typeface="MV Boli"/>',c[c.length]='<a:font script="Deva" typeface="Mangal"/>',c[c.length]='<a:font script="Telu" typeface="Gautami"/>',c[c.length]='<a:font script="Taml" typeface="Latha"/>',c[c.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',c[c.length]='<a:font script="Orya" typeface="Kalinga"/>',c[c.length]='<a:font script="Mlym" typeface="Kartika"/>',c[c.length]='<a:font script="Laoo" typeface="DokChampa"/>',c[c.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',c[c.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',c[c.length]='<a:font script="Viet" typeface="Times New Roman"/>',c[c.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',c[c.length]='<a:font script="Geor" typeface="Sylfaen"/>',c[c.length]="</a:majorFont>",c[c.length]="<a:minorFont>",c[c.length]='<a:latin typeface="Calibri"/>',c[c.length]='<a:ea typeface=""/>',c[c.length]='<a:cs typeface=""/>',c[c.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',c[c.length]='<a:font script="Hang" typeface="맑은 고딕"/>',c[c.length]='<a:font script="Hans" typeface="宋体"/>',c[c.length]='<a:font script="Hant" typeface="新細明體"/>',c[c.length]='<a:font script="Arab" typeface="Arial"/>',c[c.length]='<a:font script="Hebr" typeface="Arial"/>',c[c.length]='<a:font script="Thai" typeface="Tahoma"/>',c[c.length]='<a:font script="Ethi" typeface="Nyala"/>',c[c.length]='<a:font script="Beng" typeface="Vrinda"/>',c[c.length]='<a:font script="Gujr" typeface="Shruti"/>',c[c.length]='<a:font script="Khmr" typeface="DaunPenh"/>',c[c.length]='<a:font script="Knda" typeface="Tunga"/>',c[c.length]='<a:font script="Guru" typeface="Raavi"/>',c[c.length]='<a:font script="Cans" typeface="Euphemia"/>',c[c.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',c[c.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',c[c.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',c[c.length]='<a:font script="Thaa" typeface="MV Boli"/>',c[c.length]='<a:font script="Deva" typeface="Mangal"/>',c[c.length]='<a:font script="Telu" typeface="Gautami"/>',c[c.length]='<a:font script="Taml" typeface="Latha"/>',c[c.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',c[c.length]='<a:font script="Orya" typeface="Kalinga"/>',c[c.length]='<a:font script="Mlym" typeface="Kartika"/>',c[c.length]='<a:font script="Laoo" typeface="DokChampa"/>',c[c.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',c[c.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',c[c.length]='<a:font script="Viet" typeface="Arial"/>',c[c.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',c[c.length]='<a:font script="Geor" typeface="Sylfaen"/>',c[c.length]="</a:minorFont>",c[c.length]="</a:fontScheme>",c[c.length]='<a:fmtScheme name="Office">',c[c.length]="<a:fillStyleLst>",c[c.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',c[c.length]='<a:gradFill rotWithShape="1">',c[c.length]="<a:gsLst>",c[c.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',c[c.length]="</a:gsLst>",c[c.length]='<a:lin ang="16200000" scaled="1"/>',c[c.length]="</a:gradFill>",c[c.length]='<a:gradFill rotWithShape="1">',c[c.length]="<a:gsLst>",c[c.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',c[c.length]="</a:gsLst>",c[c.length]='<a:lin ang="16200000" scaled="0"/>',c[c.length]="</a:gradFill>",c[c.length]="</a:fillStyleLst>",c[c.length]="<a:lnStyleLst>",c[c.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',c[c.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',c[c.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',c[c.length]="</a:lnStyleLst>",c[c.length]="<a:effectStyleLst>",c[c.length]="<a:effectStyle>",c[c.length]="<a:effectLst>",c[c.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',c[c.length]="</a:effectLst>",c[c.length]="</a:effectStyle>",c[c.length]="<a:effectStyle>",c[c.length]="<a:effectLst>",c[c.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',c[c.length]="</a:effectLst>",c[c.length]="</a:effectStyle>",c[c.length]="<a:effectStyle>",c[c.length]="<a:effectLst>",c[c.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',c[c.length]="</a:effectLst>",c[c.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',c[c.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',c[c.length]="</a:effectStyle>",c[c.length]="</a:effectStyleLst>",c[c.length]="<a:bgFillStyleLst>",c[c.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',c[c.length]='<a:gradFill rotWithShape="1">',c[c.length]="<a:gsLst>",c[c.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',c[c.length]="</a:gsLst>",c[c.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',c[c.length]="</a:gradFill>",c[c.length]='<a:gradFill rotWithShape="1">',c[c.length]="<a:gsLst>",c[c.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',c[c.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',c[c.length]="</a:gsLst>",c[c.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',c[c.length]="</a:gradFill>",c[c.length]="</a:bgFillStyleLst>",c[c.length]="</a:fmtScheme>",c[c.length]="</a:themeElements>",c[c.length]="<a:objectDefaults>",c[c.length]="<a:spDef>",c[c.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',c[c.length]="</a:spDef>",c[c.length]="<a:lnDef>",c[c.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',c[c.length]="</a:lnDef>",c[c.length]="</a:objectDefaults>",c[c.length]="<a:extraClrSchemeLst/>",c[c.length]="</a:theme>",c.join("")}());var c,d,e,f={};if(!(e=a.match(eq)))throw Error("themeElements not found in theme");return c=e[0],f.themeElements={},[["clrScheme",en,ek],["fontScheme",eo,el],["fmtScheme",ep,em]].forEach(function(a){if(!(d=c.match(a[1])))throw Error(a[0]+" not found in themeElements");a[2](d,f,b)}),f.raw=a,f}function es(a,b,c,d){var e,f=Array.isArray(a);b.forEach(function(b){var g=cv(b.ref);if(f?(a[g.r]||(a[g.r]=[]),e=a[g.r][g.c]):e=a[b.ref],!e){e={t:"z"},f?a[g.r][g.c]=e:a[b.ref]=e;var h=cz(a["!ref"]||"BDWGO1000001:A1");h.s.r>g.r&&(h.s.r=g.r),h.e.r<g.r&&(h.e.r=g.r),h.s.c>g.c&&(h.s.c=g.c),h.e.c<g.c&&(h.e.c=g.c);var i=cy(h);i!==a["!ref"]&&(a["!ref"]=i)}e.c||(e.c=[]);var j={a:b.author,t:b.t,r:b.r,T:c};b.h&&(j.h=b.h);for(var k=e.c.length-1;k>=0;--k){if(!c&&e.c[k].T)return;c&&!e.c[k].T&&e.c.splice(k,1)}if(c&&d){for(k=0;k<d.length;++k)if(j.a==d[k].id){j.a=d[k].name||j.a;break}}e.c.push(j)})}var et=function(){var a=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,b={r:0,c:0};function c(a,c,d,e){var f=!1,g=!1;0==d.length?g=!0:"["==d.charAt(0)&&(g=!0,d=d.slice(1,-1)),0==e.length?f=!0:"["==e.charAt(0)&&(f=!0,e=e.slice(1,-1));var h=d.length>0?0|parseInt(d,10):0,i=e.length>0?0|parseInt(e,10):0;return f?i+=b.c:--i,g?h+=b.r:--h,c+(f?"":"$")+cu(i)+(g?"":"$")+cs(h)}return function(d,e){return b=e,d.replace(a,c)}}(),eu=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,ev=function(a,b){return a.replace(eu,function(a,c,d,e,f,g){var h=ct(e)-(d?0:b.c),i=cr(g)-(f?0:b.r);return c+"R"+(0==i?"":f?i+1:"["+i+"]")+"C"+(0==h?"":d?h+1:"["+h+"]")})};function ew(a,b){return a.replace(eu,function(a,c,d,e,f,g){return c+("$"==d?d+e:cu(ct(e)+b.c))+("$"==f?f+g:cs(cr(g)+b.r))})}function ex(a){return a.replace(/_xlfn\./g,"")}function ey(a){a.l+=1}function ez(a,b){var c=a.read_shift(1==b?1:2);return[16383&c,c>>14&1,c>>15&1]}function eA(a,b,c){var d=2;if(c)if(c.biff>=2&&c.biff<=5)return eB(a,b,c);else 12==c.biff&&(d=4);var e=a.read_shift(d),f=a.read_shift(d),g=ez(a,2),h=ez(a,2);return{s:{r:e,c:g[0],cRel:g[1],rRel:g[2]},e:{r:f,c:h[0],cRel:h[1],rRel:h[2]}}}function eB(a){var b=ez(a,2),c=ez(a,2),d=a.read_shift(1),e=a.read_shift(1);return{s:{r:b[0],c:d,cRel:b[1],rRel:b[2]},e:{r:c[0],c:e,cRel:c[1],rRel:c[2]}}}function eC(a,b,c){if(c&&c.biff>=2&&c.biff<=5){var d,e,f;return e=ez(d=a,2),f=d.read_shift(1),{r:e[0],c:f,cRel:e[1],rRel:e[2]}}var g=a.read_shift(c&&12==c.biff?4:2),h=ez(a,2);return{r:g,c:h[0],cRel:h[1],rRel:h[2]}}function eD(a){var b=1&a[a.l+1];return a.l+=4,[b,1]}function eE(a){return[a.read_shift(1),a.read_shift(1)]}function eF(a,b,c){var d;return a.l+=2,[{r:a.read_shift(2),c:255&(d=a.read_shift(2)),fQuoted:!!(16384&d),cRel:d>>15,rRel:d>>15}]}function eG(a){return a.l+=6,[]}function eH(a){return a.l+=2,[dg(a),1&a.read_shift(2)]}var eI=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"],eJ={1:{n:"PtgExp",f:function(a,b,c){return(a.l++,c&&12==c.biff)?[a.read_shift(4,"i"),0]:[a.read_shift(2),a.read_shift(c&&2==c.biff?1:2)]}},2:{n:"PtgTbl",f:cj},3:{n:"PtgAdd",f:ey},4:{n:"PtgSub",f:ey},5:{n:"PtgMul",f:ey},6:{n:"PtgDiv",f:ey},7:{n:"PtgPower",f:ey},8:{n:"PtgConcat",f:ey},9:{n:"PtgLt",f:ey},10:{n:"PtgLe",f:ey},11:{n:"PtgEq",f:ey},12:{n:"PtgGe",f:ey},13:{n:"PtgGt",f:ey},14:{n:"PtgNe",f:ey},15:{n:"PtgIsect",f:ey},16:{n:"PtgUnion",f:ey},17:{n:"PtgRange",f:ey},18:{n:"PtgUplus",f:ey},19:{n:"PtgUminus",f:ey},20:{n:"PtgPercent",f:ey},21:{n:"PtgParen",f:ey},22:{n:"PtgMissArg",f:ey},23:{n:"PtgStr",f:function(a,b,c){return a.l++,di(a,b-1,c)}},26:{n:"PtgSheet",f:function(a,b,c){return a.l+=5,a.l+=2,a.l+=2==c.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(a,b,c){return a.l+=2==c.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(a){return a.l++,cT[a.read_shift(1)]}},29:{n:"PtgBool",f:function(a){return a.l++,0!==a.read_shift(1)}},30:{n:"PtgInt",f:function(a){return a.l++,a.read_shift(2)}},31:{n:"PtgNum",f:function(a){return a.l++,cL(a,8)}},32:{n:"PtgArray",f:function(a,b,c){var d=(96&a[a.l++])>>5;return a.l+=2==c.biff?6:12==c.biff?14:7,[d]}},33:{n:"PtgFunc",f:function(a,b,c){var d=(96&a[a.l])>>5;a.l+=1;var e=a.read_shift(c&&c.biff<=3?1:2);return[eX[e],eW[e],d]}},34:{n:"PtgFuncVar",f:function(a,b,c){var d,e=a[a.l++],f=a.read_shift(1),g=c&&c.biff<=3?[88==e?-1:0,a.read_shift(1)]:[(d=a)[d.l+1]>>7,32767&d.read_shift(2)];return[f,(0===g[0]?eW:eV)[g[1]]]}},35:{n:"PtgName",f:function(a,b,c){var d=a.read_shift(1)>>>5&3,e=!c||c.biff>=8?4:2,f=a.read_shift(e);switch(c.biff){case 2:a.l+=5;break;case 3:case 4:a.l+=8;break;case 5:a.l+=12}return[d,0,f]}},36:{n:"PtgRef",f:function(a,b,c){var d=(96&a[a.l])>>5;return a.l+=1,[d,eC(a,0,c)]}},37:{n:"PtgArea",f:function(a,b,c){return[(96&a[a.l++])>>5,eA(a,c.biff>=2&&c.biff<=5?6:8,c)]}},38:{n:"PtgMemArea",f:function(a,b,c){var d=a.read_shift(1)>>>5&3;return a.l+=c&&2==c.biff?3:4,[d,a.read_shift(c&&2==c.biff?1:2)]}},39:{n:"PtgMemErr",f:cj},40:{n:"PtgMemNoMem",f:cj},41:{n:"PtgMemFunc",f:function(a,b,c){return[a.read_shift(1)>>>5&3,a.read_shift(c&&2==c.biff?1:2)]}},42:{n:"PtgRefErr",f:function(a,b,c){var d=a.read_shift(1)>>>5&3;return a.l+=4,c.biff<8&&a.l--,12==c.biff&&(a.l+=2),[d]}},43:{n:"PtgAreaErr",f:function(a,b,c){var d=(96&a[a.l++])>>5;return a.l+=c&&c.biff>8?12:c.biff<8?6:8,[d]}},44:{n:"PtgRefN",f:function(a,b,c){var d=(96&a[a.l])>>5;return a.l+=1,[d,function(a,b,c){var d,e,f,g,h,i=c&&c.biff?c.biff:8;if(i>=2&&i<=5){return e=(d=a).read_shift(2),f=d.read_shift(1),g=(32768&e)>>15,h=(16384&e)>>14,e&=16383,1==g&&e>=8192&&(e-=16384),1==h&&f>=128&&(f-=256),{r:e,c:f,cRel:h,rRel:g}}var j=a.read_shift(i>=12?4:2),k=a.read_shift(2),l=(16384&k)>>14,m=(32768&k)>>15;if(k&=16383,1==m)for(;j>524287;)j-=1048576;if(1==l)for(;k>8191;)k-=16384;return{r:j,c:k,cRel:l,rRel:m}}(a,0,c)]}},45:{n:"PtgAreaN",f:function(a,b,c){return[(96&a[a.l++])>>5,function(a,b,c){if(c.biff<8)return eB(a,b,c);var d=a.read_shift(12==c.biff?4:2),e=a.read_shift(12==c.biff?4:2),f=ez(a,2),g=ez(a,2);return{s:{r:d,c:f[0],cRel:f[1],rRel:f[2]},e:{r:e,c:g[0],cRel:g[1],rRel:g[2]}}}(a,b-1,c)]}},46:{n:"PtgMemAreaN",f:function(a){return[a.read_shift(1)>>>5&3,a.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(a){return[a.read_shift(1)>>>5&3,a.read_shift(2)]}},57:{n:"PtgNameX",f:function(a,b,c){var d,e,f,g;return 5==c.biff?(e=(d=a).read_shift(1)>>>5&3,f=d.read_shift(2,"i"),d.l+=8,g=d.read_shift(2),d.l+=12,[e,f,g]):[a.read_shift(1)>>>5&3,a.read_shift(2),a.read_shift(4)]}},58:{n:"PtgRef3d",f:function(a,b,c){var d=(96&a[a.l])>>5;a.l+=1;var e=a.read_shift(2);return c&&5==c.biff&&(a.l+=12),[d,e,eC(a,0,c)]}},59:{n:"PtgArea3d",f:function(a,b,c){var d=(96&a[a.l++])>>5,e=a.read_shift(2,"i"),f=8;if(c)switch(c.biff){case 5:a.l+=12,f=6;break;case 12:f=12}return[d,e,eA(a,f,c)]}},60:{n:"PtgRefErr3d",f:function(a,b,c){var d=(96&a[a.l++])>>5,e=a.read_shift(2),f=4;if(c)switch(c.biff){case 5:f=15;break;case 12:f=6}return a.l+=f,[d,e]}},61:{n:"PtgAreaErr3d",f:function(a,b,c){var d=(96&a[a.l++])>>5,e=a.read_shift(2),f=8;if(c)switch(c.biff){case 5:a.l+=12,f=6;break;case 12:f=12}return a.l+=f,[d,e]}},255:{}},eK={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},eL={1:{n:"PtgElfLel",f:eH},2:{n:"PtgElfRw",f:eF},3:{n:"PtgElfCol",f:eF},6:{n:"PtgElfRwV",f:eF},7:{n:"PtgElfColV",f:eF},10:{n:"PtgElfRadical",f:eF},11:{n:"PtgElfRadicalS",f:eG},13:{n:"PtgElfColS",f:eG},15:{n:"PtgElfColSV",f:eG},16:{n:"PtgElfRadicalLel",f:eH},25:{n:"PtgList",f:function(a){a.l+=2;var b=a.read_shift(2),c=a.read_shift(2),d=a.read_shift(4),e=a.read_shift(2),f=a.read_shift(2),g=eI[c>>2&31];return{ixti:b,coltype:3&c,rt:g,idx:d,c:e,C:f}}},29:{n:"PtgSxName",f:function(a){return a.l+=2,[a.read_shift(4)]}},255:{}},eM={0:{n:"PtgAttrNoop",f:function(a){return a.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(a,b,c){var d=255&a[a.l+1]?1:0;return a.l+=c&&2==c.biff?3:4,[d]}},2:{n:"PtgAttrIf",f:function(a,b,c){var d=255&a[a.l+1]?1:0;return a.l+=2,[d,a.read_shift(c&&2==c.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(a,b,c){a.l+=2;for(var d=a.read_shift(c&&2==c.biff?1:2),e=[],f=0;f<=d;++f)e.push(a.read_shift(c&&2==c.biff?1:2));return e}},8:{n:"PtgAttrGoto",f:function(a,b,c){var d=255&a[a.l+1]?1:0;return a.l+=2,[d,a.read_shift(c&&2==c.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(a,b,c){a.l+=c&&2==c.biff?3:4}},32:{n:"PtgAttrBaxcel",f:eD},33:{n:"PtgAttrBaxcel",f:eD},64:{n:"PtgAttrSpace",f:function(a){return a.read_shift(2),eE(a,2)}},65:{n:"PtgAttrSpaceSemi",f:function(a){return a.read_shift(2),eE(a,2)}},128:{n:"PtgAttrIfError",f:function(a){var b=255&a[a.l+1]?1:0;return a.l+=2,[b,a.read_shift(2)]}},255:{}};function eN(a,b,c,d){if(d.biff<8)return e=b,void(a.l+=e);for(var e,f,g=a.l+b,h=[],i=0;i!==c.length;++i)switch(c[i][0]){case"PtgArray":c[i][1]=function(a,b,c){var d=0,e=0;12==c.biff?(d=a.read_shift(4),e=a.read_shift(4)):(e=1+a.read_shift(1),d=1+a.read_shift(2)),c.biff>=2&&c.biff<8&&(--d,0==--e&&(e=256));for(var f=0,g=[];f!=d&&(g[f]=[]);++f)for(var h=0;h!=e;++h)g[f][h]=function(a,b){var c=[a.read_shift(1)];if(12==b)switch(c[0]){case 2:c[0]=4;break;case 4:c[0]=16;break;case 0:c[0]=1;break;case 1:c[0]=2}switch(c[0]){case 4:c[1]=df(a,1)?"TRUE":"FALSE",12!=b&&(a.l+=7);break;case 37:case 16:c[1]=cT[a[a.l]],a.l+=12==b?4:8;break;case 0:a.l+=8;break;case 1:c[1]=cL(a,8);break;case 2:c[1]=dl(a,0,{biff:b>0&&b<8?2:b});break;default:throw Error("Bad SerAr: "+c[0])}return c}(a,c.biff);return g}(a,0,d),h.push(c[i][1]);break;case"PtgMemArea":c[i][2]=function(a,b,c){for(var d=a.read_shift(12==c.biff?4:2),e=[],f=0;f!=d;++f)e.push((12==c.biff?cK:ds)(a,8));return e}(a,c[i][1],d),h.push(c[i][2]);break;case"PtgExp":d&&12==d.biff&&(c[i][1][1]=a.read_shift(4),h.push(c[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+c[i][0]}return 0!=(b=g-a.l)&&h.push((f=b,void(a.l+=f))),h}function eO(a,b,c){for(var d,e,f,g=a.l+b,h=[];g!=a.l;)(b=g-a.l,e=eJ[f=a[a.l]]||eJ[eK[f]],(24===f||25===f)&&(e=(24===f?eL:eM)[a[a.l+1]]),e&&e.f)?h.push([e.n,e.f(a,b,c)]):(d=b,a.l+=d);return h}var eP={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function eQ(a,b,c){if(!a)return"SH33TJSERR0";if(c.biff>8&&(!a.XTI||!a.XTI[b]))return a.SheetNames[b];if(!a.XTI)return"SH33TJSERR6";var d=a.XTI[b];if(c.biff<8)return b>1e4&&(b-=65536),b<0&&(b=-b),0==b?"":a.XTI[b-1];if(!d)return"SH33TJSERR1";var e="";if(c.biff>8)switch(a[d[0]][0]){case 357:return e=-1==d[1]?"#REF":a.SheetNames[d[1]],d[1]==d[2]?e:e+":"+a.SheetNames[d[2]];case 358:if(null!=c.SID)return a.SheetNames[c.SID];return"SH33TJSSAME"+a[d[0]][0];default:return"SH33TJSSRC"+a[d[0]][0]}switch(a[d[0]][0][0]){case 1025:return e=-1==d[1]?"#REF":a.SheetNames[d[1]]||"SH33TJSERR3",d[1]==d[2]?e:e+":"+a.SheetNames[d[2]];case 14849:return a[d[0]].slice(1).map(function(a){return a.Name}).join(";;");default:if(!a[d[0]][0][3])return"SH33TJSERR2";return e=-1==d[1]?"#REF":a[d[0]][0][3][d[1]]||"SH33TJSERR4",d[1]==d[2]?e:e+":"+a[d[0]][0][3][d[2]]}}function eR(a,b,c){var d=eQ(a,b,c);return"#REF"==d?d:function(a,b){if(!a&&!(b&&b.biff<=5&&b.biff>=2))throw Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(a)?"'"+a+"'":a}(d,c)}function eS(a,b,c,d,e){var f,g,h,i,j=e&&e.biff||8,k={s:{c:0,r:0},e:{c:0,r:0}},l=[],m=0,n=0,o="";if(!a[0]||!a[0][0])return"";for(var p=-1,q="",r=0,s=a[0].length;r<s;++r){var t=a[0][r];switch(t[0]){case"PtgUminus":l.push("-"+l.pop());break;case"PtgUplus":l.push("+"+l.pop());break;case"PtgPercent":l.push(l.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(f=l.pop(),g=l.pop(),p>=0){switch(a[0][p][1][0]){case 0:q=a4(" ",a[0][p][1][1]);break;case 1:q=a4("\r",a[0][p][1][1]);break;default:if(q="",e.WTF)throw Error("Unexpected PtgAttrSpaceType "+a[0][p][1][0])}g+=q,p=-1}l.push(g+eP[t[0]]+f);break;case"PtgIsect":f=l.pop(),g=l.pop(),l.push(g+" "+f);break;case"PtgUnion":f=l.pop(),g=l.pop(),l.push(g+","+f);break;case"PtgRange":f=l.pop(),g=l.pop(),l.push(g+":"+f);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":h=cn(t[1][1],k,e),l.push(cp(h,j));break;case"PtgRefN":h=c?cn(t[1][1],c,e):t[1][1],l.push(cp(h,j));break;case"PtgRef3d":m=t[1][1],h=cn(t[1][2],k,e),o=eR(d,m,e),l.push(o+"!"+cp(h,j));break;case"PtgFunc":case"PtgFuncVar":var u=t[1][0],v=t[1][1];u||(u=0);var w=0==(u&=127)?[]:l.slice(-u);l.length-=u,"User"===v&&(v=w.shift()),l.push(v+"("+w.join(",")+")");break;case"PtgBool":l.push(t[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":l.push(t[1]);break;case"PtgNum":l.push(String(t[1]));break;case"PtgStr":l.push('"'+t[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":i=co(t[1][1],c?{s:c}:k,e),l.push(cq(i,e));break;case"PtgArea":i=co(t[1][1],k,e),l.push(cq(i,e));break;case"PtgArea3d":m=t[1][1],i=t[1][2],o=eR(d,m,e),l.push(o+"!"+cq(i,e));break;case"PtgAttrSum":l.push("SUM("+l.pop()+")");break;case"PtgName":n=t[1][2];var x=(d.names||[])[n-1]||(d[0]||[])[n],y=x?x.Name:"SH33TJSNAME"+String(n);y&&"_xlfn."==y.slice(0,6)&&!e.xlfn&&(y=y.slice(6)),l.push(y);break;case"PtgNameX":var z,A=t[1][1];if(n=t[1][2],e.biff<=5)A<0&&(A=-A),d[A]&&(z=d[A][n]);else{var B="";if(14849==((d[A]||[])[0]||[])[0]||(1025==((d[A]||[])[0]||[])[0]?d[A][n]&&d[A][n].itab>0&&(B=d.SheetNames[d[A][n].itab-1]+"!"):B=d.SheetNames[n-1]+"!"),d[A]&&d[A][n])B+=d[A][n].Name;else if(d[0]&&d[0][n])B+=d[0][n].Name;else{var C=(eQ(d,A,e)||"").split(";;");C[n-1]?B=C[n-1]:B+="SH33TJSERRX"}l.push(B);break}z||(z={Name:"SH33TJSERRY"}),l.push(z.Name);break;case"PtgParen":var D="(",E=")";if(p>=0){switch(q="",a[0][p][1][0]){case 2:D=a4(" ",a[0][p][1][1])+D;break;case 3:D=a4("\r",a[0][p][1][1])+D;break;case 4:E=a4(" ",a[0][p][1][1])+E;break;case 5:E=a4("\r",a[0][p][1][1])+E;break;default:if(e.WTF)throw Error("Unexpected PtgAttrSpaceType "+a[0][p][1][0])}p=-1}l.push(D+l.pop()+E);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":l.push("#REF!");break;case"PtgExp":h={c:t[1][1],r:t[1][0]};var F={c:c.c,r:c.r};if(d.sharedf[cw(h)]){var G=d.sharedf[cw(h)];l.push(eS(G,k,F,d,e))}else{var H=!1;for(f=0;f!=d.arrayf.length;++f)if((g=d.arrayf[f],!(h.c<g[0].s.c)&&!(h.c>g[0].e.c))&&!(h.r<g[0].s.r)&&!(h.r>g[0].e.r)){l.push(eS(g[1],k,F,d,e)),H=!0;break}H||l.push(t[1])}break;case"PtgArray":l.push("{"+function(a){for(var b=[],c=0;c<a.length;++c){for(var d=a[c],e=[],f=0;f<d.length;++f){var g=d[f];g?2===g[0]?e.push('"'+g[1].replace(/"/g,'""')+'"'):e.push(g[1]):e.push("")}b.push(e.join(","))}return b.join(";")}(t[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":p=r;break;case"PtgMissArg":l.push("");break;case"PtgList":l.push("Table"+t[1].idx+"[#"+t[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw Error("Unsupported ELFs");default:throw Error("Unrecognized Formula Token: "+String(t))}var I=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=e.biff&&p>=0&&-1==I.indexOf(a[0][r][0])){t=a[0][p];var J=!0;switch(t[1][0]){case 4:J=!1;case 0:q=a4(" ",t[1][1]);break;case 5:J=!1;case 1:q=a4("\r",t[1][1]);break;default:if(q="",e.WTF)throw Error("Unexpected PtgAttrSpaceType "+t[1][0])}l.push((J?q:"")+l.pop()+(J?"":q)),p=-1}}if(l.length>1&&e.WTF)throw Error("bad formula stack");return l[0]}function eT(a,b,c){var d=a.l+b,e=dq(a,6);2==c.biff&&++a.l;var f=function(a){var b;if(65535!==b8(a,a.l+6))return[cL(a),"n"];switch(a[a.l]){case 0:return a.l+=8,["String","s"];case 1:return b=1===a[a.l+2],a.l+=8,[b,"b"];case 2:return b=a[a.l+2],a.l+=8,[b,"e"];case 3:return a.l+=8,["","s"]}return[]}(a,8),g=a.read_shift(1);2!=c.biff&&(a.read_shift(1),c.biff>=5&&a.read_shift(4));var h=function(a,b,c){var d,e,f=a.l+b,g=2==c.biff?1:2,h=a.read_shift(g);if(65535==h)return[[],(d=b-2,void(a.l+=d))];var i=eO(a,h,c);return b!==h+g&&(e=eN(a,b-h-g,i,c)),a.l=f,[i,e]}(a,d-a.l,c);return{cell:e,val:f[0],formula:h,shared:g>>3&1,tt:f[1]}}function eU(a,b,c){var d=a.read_shift(4),e=eO(a,d,c),f=a.read_shift(4),g=f>0?eN(a,f,e,c):null;return[e,g]}var eV={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},eW={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},eX={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function eY(a){return"of:"==a.slice(0,3)&&(a=a.slice(3)),61==a.charCodeAt(0)&&61==(a=a.slice(1)).charCodeAt(0)&&(a=a.slice(1)),(a=(a=(a=a.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(a,b){return b.replace(/\./g,"")})).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function eZ(a){var b=a.split(":");return[b[0].split(".")[0],b[0].split(".")[1]+(b.length>1?":"+(b[1].split(".")[1]||b[1].split(".")[0]):"")]}var e$={},e_={};function e0(a,b){if(a){var c=[.7,.7,.75,.75,.3,.3];"xlml"==b&&(c=[1,1,1,1,.5,.5]),null==a.left&&(a.left=c[0]),null==a.right&&(a.right=c[1]),null==a.top&&(a.top=c[2]),null==a.bottom&&(a.bottom=c[3]),null==a.header&&(a.header=c[4]),null==a.footer&&(a.footer=c[5])}}function e1(a,b,c,d,e,f){try{d.cellNF&&(a.z=al[b])}catch(a){if(d.WTF)throw a}if("z"!==a.t||d.cellStyles){if("d"===a.t&&"string"==typeof a.v&&(a.v=a1(a.v)),(!d||!1!==d.cellText)&&"z"!==a.t)try{if(null==al[b]&&aM(aO[b]||"General",b),"e"===a.t)a.w=a.w||cT[a.v];else if(0===b)if("n"===a.t)(0|a.v)===a.v?a.w=a.v.toString(10):a.w=av(a.v);else if("d"===a.t){var g=aV(a.v);(0|g)===g?a.w=g.toString(10):a.w=av(g)}else{if(void 0===a.v)return"";a.w=aw(a.v,e_)}else"d"===a.t?a.w=aL(b,aV(a.v),e_):a.w=aL(b,a.v,e_)}catch(a){if(d.WTF)throw a}if(d.cellStyles&&null!=c)try{a.s=f.Fills[c],a.s.fgColor&&a.s.fgColor.theme&&!a.s.fgColor.rgb&&(a.s.fgColor.rgb=d6(e.themeElements.clrScheme[a.s.fgColor.theme].rgb,a.s.fgColor.tint||0),d.WTF&&(a.s.fgColor.raw_rgb=e.themeElements.clrScheme[a.s.fgColor.theme].rgb)),a.s.bgColor&&a.s.bgColor.theme&&(a.s.bgColor.rgb=d6(e.themeElements.clrScheme[a.s.bgColor.theme].rgb,a.s.bgColor.tint||0),d.WTF&&(a.s.bgColor.raw_rgb=e.themeElements.clrScheme[a.s.bgColor.theme].rgb))}catch(a){if(d.WTF&&f.Fills)throw a}}}var e2=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,e3=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,e4=/<(?:\w:)?hyperlink [^>]*>/mg,e5=/"(\w*:\w*)"/,e6=/<(?:\w:)?col\b[^>]*[\/]?>/g,e7=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,e8=/<(?:\w:)?pageMargins[^>]*\/>/g,e9=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,fa=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,fb=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function fc(a,b,c,d){var e=bo(a);c.Sheets[d]||(c.Sheets[d]={}),e.codeName&&(c.Sheets[d].CodeName=bs(bC(e.codeName)))}var fd=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/,fe=function(){var a=/<(?:\w+:)?c[ \/>]/,b=/<\/(?:\w+:)?row>/,c=/r=["']([^"']*)["']/,d=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,e=/ref=["']([^"']*)["']/,f=bE("v"),g=bE("f");return function(h,i,j,k,l,m){for(var n,o,p,q,r,s=0,t="",u=[],v=[],w=0,x=0,y=0,z="",A=0,B=0,C=0,D=0,E=Array.isArray(m.CellXf),F=[],G=[],H=Array.isArray(i),I=[],J={},K=!1,L=!!j.sheetStubs,M=h.split(b),N=0,O=M.length;N!=O;++N){var P=(t=M[N].trim()).length;if(0!==P){var Q=0;b:for(s=0;s<P;++s)switch(t[s]){case">":if("/"!=t[s-1]){++s;break b}if(j&&j.cellStyles){if(A=null!=(o=bo(t.slice(Q,s),!0)).r?parseInt(o.r,10):A+1,B=-1,j.sheetRows&&j.sheetRows<A)continue;J={},K=!1,o.ht&&(K=!0,J.hpt=parseFloat(o.ht),J.hpx=ee(J.hpt)),"1"==o.hidden&&(K=!0,J.hidden=!0),null!=o.outlineLevel&&(K=!0,J.level=+o.outlineLevel),K&&(I[A-1]=J)}break;case"<":Q=s}if(Q>=s)break;if(A=null!=(o=bo(t.slice(Q,s),!0)).r?parseInt(o.r,10):A+1,B=-1,!j.sheetRows||!(j.sheetRows<A)){k.s.r>A-1&&(k.s.r=A-1),k.e.r<A-1&&(k.e.r=A-1),j&&j.cellStyles&&(J={},K=!1,o.ht&&(K=!0,J.hpt=parseFloat(o.ht),J.hpx=ee(J.hpt)),"1"==o.hidden&&(K=!0,J.hidden=!0),null!=o.outlineLevel&&(K=!0,J.level=+o.outlineLevel),K&&(I[A-1]=J)),u=t.slice(s).split(a);for(var R=0;R!=u.length&&"<"==u[R].trim().charAt(0);++R);for(s=0,u=u.slice(R);s!=u.length;++s)if(0!==(t=u[s].trim()).length){if(v=t.match(c),w=s,x=0,y=0,t="<c "+("<"==t.slice(0,1)?">":"")+t,null!=v&&2===v.length){for(x=0,w=0,z=v[1];x!=z.length&&!((y=z.charCodeAt(x)-64)<1)&&!(y>26);++x)w=26*w+y;B=--w}else++B;for(x=0;x!=t.length&&62!==t.charCodeAt(x);++x);if(++x,(o=bo(t.slice(0,x),!0)).r||(o.r=cw({r:A-1,c:B})),z=t.slice(x),n={t:""},null!=(v=z.match(f))&&""!==v[1]&&(n.v=bs(v[1])),j.cellFormula){if(null!=(v=z.match(g))&&""!==v[1]){if(n.f=bs(bC(v[1])).replace(/\r\n/g,"\n"),j.xlfn||(n.f=ex(n.f)),v[0].indexOf('t="array"')>-1)n.F=(z.match(e)||[])[1],n.F.indexOf(":")>-1&&F.push([cz(n.F),n.F]);else if(v[0].indexOf('t="shared"')>-1){q=bo(v[0]);var S=bs(bC(v[1]));j.xlfn||(S=ex(S)),G[parseInt(q.si,10)]=[q,S,o.r]}}else(v=z.match(/<f[^>]*\/>/))&&G[(q=bo(v[0])).si]&&(n.f=function(a,b,c){var d=cx(b).s,e=cv(c);return ew(a,{r:e.r-d.r,c:e.c-d.c})}(G[q.si][1],G[q.si][2],o.r));var T=cv(o.r);for(x=0;x<F.length;++x)T.r>=F[x][0].s.r&&T.r<=F[x][0].e.r&&T.c>=F[x][0].s.c&&T.c<=F[x][0].e.c&&(n.F=F[x][1])}if(null==o.t&&void 0===n.v)if(n.f||n.F)n.v=0,n.t="n";else{if(!L)continue;n.t="z"}else n.t=o.t||"n";switch(k.s.c>B&&(k.s.c=B),k.e.c<B&&(k.e.c=B),n.t){case"n":if(""==n.v||null==n.v){if(!L)continue;n.t="z"}else n.v=parseFloat(n.v);break;case"s":if(void 0===n.v){if(!L)continue;n.t="z"}else p=e$[parseInt(n.v,10)],n.v=p.t,n.r=p.r,j.cellHTML&&(n.h=p.h);break;case"str":n.t="s",n.v=null!=n.v?bC(n.v):"",j.cellHTML&&(n.h=bv(n.v));break;case"inlineStr":v=z.match(d),n.t="s",null!=v&&(p=dV(v[1]))?(n.v=p.t,j.cellHTML&&(n.h=p.h)):n.v="";break;case"b":n.v=bx(n.v);break;case"d":j.cellDates?n.v=a1(n.v,1):(n.v=aV(a1(n.v,1)),n.t="n");break;case"e":j&&!1===j.cellText||(n.w=n.v),n.v=cU[n.v]}if(C=D=0,r=null,E&&void 0!==o.s&&null!=(r=m.CellXf[o.s])&&(null!=r.numFmtId&&(C=r.numFmtId),j.cellStyles&&null!=r.fillId&&(D=r.fillId)),e1(n,C,D,j,l,m),j.cellDates&&E&&"n"==n.t&&aI(al[C])&&(n.t="d",n.v=aZ(n.v)),o.cm&&j.xlmeta){var U=(j.xlmeta.Cell||[])[o.cm-1];U&&"XLDAPR"==U.type&&(n.D=!0)}if(H){var V=cv(o.r);i[V.r]||(i[V.r]=[]),i[V.r][V.c]=n}else i[o.r]=n}}}}I.length>0&&(i["!rows"]=I)}}();function ff(a){return[cH(a),cL(a),"n"]}var fg=["left","right","top","bottom","header","footer"],fh=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],fi=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],fj=[],fk=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function fl(a,b){for(var c=0;c!=a.length;++c)for(var d=a[c],e=0;e!=b.length;++e){var f=b[e];if(null==d[f[0]])d[f[0]]=f[1];else switch(f[2]){case"bool":"string"==typeof d[f[0]]&&(d[f[0]]=bx(d[f[0]]));break;case"int":"string"==typeof d[f[0]]&&(d[f[0]]=parseInt(d[f[0]],10))}}}function fm(a,b){for(var c=0;c!=b.length;++c){var d=b[c];if(null==a[d[0]])a[d[0]]=d[1];else switch(d[2]){case"bool":"string"==typeof a[d[0]]&&(a[d[0]]=bx(a[d[0]]));break;case"int":"string"==typeof a[d[0]]&&(a[d[0]]=parseInt(a[d[0]],10))}}}function fn(a){fm(a.WBProps,fh),fm(a.CalcPr,fk),fl(a.WBView,fi),fl(a.Sheets,fj),e_.date1904=bx(a.WBProps.date1904)}var fo="][*?/\\".split(""),fp=/<\w+:workbook/;function fq(a,b){var c={};return a.read_shift(4),c.ArchID=a.read_shift(4),a.l+=b-8,c}var fr=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,fs=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function ft(a,b){var c=a.split(/\s+/),d=[];if(b||(d[0]=c[0]),1===c.length)return d;var e,f,g,h=a.match(fr);if(h)for(g=0;g!=h.length;++g)-1===(f=(e=h[g].match(fs))[1].indexOf(":"))?d[e[1]]=e[2].slice(1,e[2].length-1):d["xmlns:"===e[1].slice(0,6)?"xmlns"+e[1].slice(6):e[1].slice(f+1)]=e[2].slice(1,e[2].length-1);return d}function fu(a,b){var c,d,h,i=b||{};aN();var j=O(bK(a));("binary"==i.type||"array"==i.type||"base64"==i.type)&&(j=void 0!==e?e.utils.decode(65001,M(j)):bC(j));var k=j.slice(0,1024).toLowerCase(),l=!1;if((1023&(k=k.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&k.indexOf(","),1023&k.indexOf(";"))){var m=a3(i);return m.type="string",dO.to_workbook(j,m)}if(-1==k.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach(function(a){k.indexOf("<"+a)>=0&&(l=!0)}),l){var n=j,o=i,p=n.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!p||0==p.length)throw Error("Invalid HTML: could not find <table>");if(1==p.length)return cC(fE(p[0],o),o);var q=fY();return p.forEach(function(a,b){fZ(q,fE(a,o),"Sheet"+(b+1))}),q}g={"General Number":"General","General Date":al[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":al[15],"Short Date":al[14],"Long Time":al[19],"Medium Time":al[18],"Short Time":al[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:al[2],Standard:al[4],Percent:al[10],Scientific:al[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var r,s,t,u=[],v={},w=[],x=i.dense?[]:{},y="",z={},A={},B=ft('<Data ss:Type="String">'),C=0,D=0,E=0,F={s:{r:2e6,c:2e6},e:{r:0,c:0}},G={},H={},I="",J=0,K=[],L={},N={},P=0,Q=[],R=[],S={},T=[],U=!1,V=[],W=[],X={},Y=0,Z=0,_={Sheets:[],WBProps:{date1904:!1}},aa={};bL.lastIndex=0,j=j.replace(/<!--([\s\S]*?)-->/mg,"");for(var ab="";r=bL.exec(j);)switch(r[3]=(ab=r[3]).toLowerCase()){case"data":if("data"==ab){if("/"===r[1]){if((s=u.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"))}else"/"!==r[0].charAt(r[0].length-2)&&u.push([r[3],!0]);break}if(u[u.length-1][1])break;"/"===r[1]?function(a,b,c,d,e,f,h,i,j,k){var l="General",m=d.StyleID,n={};k=k||{};var o=[],p=0;for(void 0===m&&i&&(m=i.StyleID),void 0===m&&h&&(m=h.StyleID);void 0!==f[m]&&(f[m].nf&&(l=f[m].nf),f[m].Interior&&o.push(f[m].Interior),f[m].Parent);)m=f[m].Parent;switch(c.Type){case"Boolean":d.t="b",d.v=bx(a);break;case"String":d.t="s",d.r=bw(bs(a)),d.v=a.indexOf("<")>-1?bs(b||a).replace(/<.*?>/g,""):d.r;break;case"DateTime":"Z"!=a.slice(-1)&&(a+="Z"),d.v=(a1(a)-new Date(Date.UTC(1899,11,30)))/864e5,d.v!=d.v?d.v=bs(a):d.v<60&&(d.v=d.v-1),l&&"General"!=l||(l="yyyy-mm-dd");case"Number":void 0===d.v&&(d.v=+a),d.t||(d.t="n");break;case"Error":d.t="e",d.v=cU[a],!1!==k.cellText&&(d.w=a);break;default:""==a&&""==b?d.t="z":(d.t="s",d.v=bw(b||a))}if(!function(a,b,c){if("z"!==a.t){if(!c||!1!==c.cellText)try{if("e"===a.t)a.w=a.w||cT[a.v];else if("General"===b)"n"===a.t?(0|a.v)===a.v?a.w=a.v.toString(10):a.w=av(a.v):a.w=aw(a.v);else{var d,e,f;d=b||"General",e=a.v,f=g[d]||bs(d),a.w="General"===f?aw(e):aL(f,e)}}catch(a){if(c.WTF)throw a}try{var h=g[b]||b||"General";if(c.cellNF&&(a.z=h),c.cellDates&&"n"==a.t&&aI(h)){var i=ap(a.v);i&&(a.t="d",a.v=new Date(i.y,i.m-1,i.d,i.H,i.M,i.S,i.u))}}catch(a){if(c.WTF)throw a}}}(d,l,k),!1!==k.cellFormula)if(d.Formula){var q=bs(d.Formula);61==q.charCodeAt(0)&&(q=q.slice(1)),d.f=et(q,e),delete d.Formula,"RC"==d.ArrayRange?d.F=et("RC:RC",e):d.ArrayRange&&(d.F=et(d.ArrayRange,e),j.push([cz(d.F),d.F]))}else for(p=0;p<j.length;++p)e.r>=j[p][0].s.r&&e.r<=j[p][0].e.r&&e.c>=j[p][0].s.c&&e.c<=j[p][0].e.c&&(d.F=j[p][1]);k.cellStyles&&(o.forEach(function(a){!n.patternType&&a.patternType&&(n.patternType=a.patternType)}),d.s=n),void 0!==d.StyleID&&(d.ixfe=d.StyleID)}(j.slice(C,r.index),I,B,"comment"==u[u.length-1][0]?S:z,{c:D,r:E},G,T[D],A,V,i):(I="",B=ft(r[0]),C=r.index+r[0].length);break;case"cell":if("/"===r[1])if(R.length>0&&(z.c=R),(!i.sheetRows||i.sheetRows>E)&&void 0!==z.v&&(i.dense?(x[E]||(x[E]=[]),x[E][D]=z):x[cu(D)+cs(E)]=z),z.HRef&&(z.l={Target:bs(z.HRef)},z.HRefScreenTip&&(z.l.Tooltip=z.HRefScreenTip),delete z.HRef,delete z.HRefScreenTip),(z.MergeAcross||z.MergeDown)&&(Y=D+(0|parseInt(z.MergeAcross,10)),Z=E+(0|parseInt(z.MergeDown,10)),K.push({s:{c:D,r:E},e:{c:Y,r:Z}})),i.sheetStubs)if(z.MergeAcross||z.MergeDown){for(var ac=D;ac<=Y;++ac)for(var ad=E;ad<=Z;++ad)(ac>D||ad>E)&&(i.dense?(x[ad]||(x[ad]=[]),x[ad][ac]={t:"z"}):x[cu(ac)+cs(ad)]={t:"z"});D=Y+1}else++D;else z.MergeAcross?D=Y+1:++D;else(z=function(a){var b=a.split(/\s+/),c={};if(1===b.length)return c;var d,e,f,g=a.match(fr);if(g)for(f=0;f!=g.length;++f)-1===(e=(d=g[f].match(fs))[1].indexOf(":"))?c[d[1]]=d[2].slice(1,d[2].length-1):c["xmlns:"===d[1].slice(0,6)?"xmlns"+d[1].slice(6):d[1].slice(e+1)]=d[2].slice(1,d[2].length-1);return c}(r[0])).Index&&(D=z.Index-1),D<F.s.c&&(F.s.c=D),D>F.e.c&&(F.e.c=D),"/>"===r[0].slice(-2)&&++D,R=[];break;case"row":"/"===r[1]||"/>"===r[0].slice(-2)?(E<F.s.r&&(F.s.r=E),E>F.e.r&&(F.e.r=E),"/>"===r[0].slice(-2)&&(A=ft(r[0])).Index&&(E=A.Index-1),D=0,++E):((A=ft(r[0])).Index&&(E=A.Index-1),X={},("0"==A.AutoFitHeight||A.Height)&&(X.hpx=parseInt(A.Height,10),X.hpt=96*X.hpx/96,W[E]=X),"1"==A.Hidden&&(X.hidden=!0,W[E]=X));break;case"worksheet":if("/"===r[1]){if((s=u.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"));w.push(y),F.s.r<=F.e.r&&F.s.c<=F.e.c&&(x["!ref"]=cy(F),i.sheetRows&&i.sheetRows<=F.e.r&&(x["!fullref"]=x["!ref"],F.e.r=i.sheetRows-1,x["!ref"]=cy(F))),K.length&&(x["!merges"]=K),T.length>0&&(x["!cols"]=T),W.length>0&&(x["!rows"]=W),v[y]=x}else F={s:{r:2e6,c:2e6},e:{r:0,c:0}},E=D=0,u.push([r[3],!1]),y=bs((s=ft(r[0])).Name),x=i.dense?[]:{},K=[],V=[],W=[],aa={name:y,Hidden:0},_.Sheets.push(aa);break;case"table":if("/"===r[1]){if((s=u.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"))}else"/>"==r[0].slice(-2)||(u.push([r[3],!1]),T=[],U=!1);break;case"style":"/"===r[1]?function(a,b,c){if(c.cellStyles&&b.Interior){var d=b.Interior;d.Pattern&&(d.patternType=ef[d.Pattern]||d.Pattern)}a[b.ID]=b}(G,H,i):H=ft(r[0]);break;case"numberformat":H.nf=bs(ft(r[0]).Format||"General"),g[H.nf]&&(H.nf=g[H.nf]);for(var ae=0;392!=ae&&al[ae]!=H.nf;++ae);if(392==ae){for(ae=57;392!=ae;++ae)if(null==al[ae]){aM(H.nf,ae);break}}break;case"column":if("table"!==u[u.length-1][0])break;if((t=ft(r[0])).Hidden&&(t.hidden=!0,delete t.Hidden),t.Width&&(t.wpx=parseInt(t.Width,10)),!U&&t.wpx>10){U=!0,d7=6;for(var af=0;af<T.length;++af)T[af]&&ed(T[af])}U&&ed(t),T[t.Index-1||T.length]=t;for(var ag=0;ag<+t.Span;++ag)T[T.length]=a3(t);break;case"namedrange":if("/"===r[1])break;_.Names||(_.Names=[]);var ah=bo(r[0]),ai={Name:ah.Name,Ref:et(ah.RefersTo.slice(1),{r:0,c:0})};_.Sheets.length>0&&(ai.Sheet=_.Sheets.length-1),_.Names.push(ai);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":"/>"===r[0].slice(-2)||("/"===r[1]?I+=j.slice(J,r.index):J=r.index+r[0].length);break;case"interior":if(!i.cellStyles)break;H.Interior=ft(r[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":"/>"===r[0].slice(-2)||("/"===r[1]?(c=ab,d=j.slice(P,r.index),f||(f=aT(c3)),L[c=f[c]||c]=d):P=r.index+r[0].length);break;case"styles":case"workbook":if("/"===r[1]){if((s=u.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"))}else u.push([r[3],!1]);break;case"comment":if("/"===r[1]){if((s=u.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"));(h=S).t=h.v||"",h.t=h.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),h.v=h.w=h.ixfe=void 0,R.push(S)}else u.push([r[3],!1]),S={a:(s=ft(r[0])).Author};break;case"autofilter":if("/"===r[1]){if((s=u.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"))}else if("/"!==r[0].charAt(r[0].length-2)){var aj=ft(r[0]);x["!autofilter"]={ref:et(aj.Range).replace(/\$/g,"")},u.push([r[3],!0])}break;case"datavalidation":case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===r[1]){if((s=u.pop())[0]!==r[3])throw Error("Bad state: "+s.join("|"))}else"/"!==r[0].charAt(r[0].length-2)&&u.push([r[3],!0]);break;default:if(0==u.length&&"document"==r[3]||0==u.length&&"uof"==r[3])return fG(j,i);var ak=!0;switch(u[u.length-1][0]){case"officedocumentsettings":switch(r[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:ak=!1}break;case"componentoptions":switch(r[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:ak=!1}break;case"excelworkbook":switch(r[3]){case"date1904":_.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:ak=!1}break;case"workbookoptions":switch(r[3]){case"owcversion":case"height":case"width":break;default:ak=!1}break;case"worksheetoptions":switch(r[3]){case"visible":if("/>"===r[0].slice(-2));else if("/"===r[1])switch(j.slice(P,r.index)){case"SheetHidden":aa.Hidden=1;break;case"SheetVeryHidden":aa.Hidden=2}else P=r.index+r[0].length;break;case"header":x["!margins"]||e0(x["!margins"]={},"xlml"),isNaN(+bo(r[0]).Margin)||(x["!margins"].header=+bo(r[0]).Margin);break;case"footer":x["!margins"]||e0(x["!margins"]={},"xlml"),isNaN(+bo(r[0]).Margin)||(x["!margins"].footer=+bo(r[0]).Margin);break;case"pagemargins":var am=bo(r[0]);x["!margins"]||e0(x["!margins"]={},"xlml"),isNaN(+am.Top)||(x["!margins"].top=+am.Top),isNaN(+am.Left)||(x["!margins"].left=+am.Left),isNaN(+am.Right)||(x["!margins"].right=+am.Right),isNaN(+am.Bottom)||(x["!margins"].bottom=+am.Bottom);break;case"displayrighttoleft":_.Views||(_.Views=[]),_.Views[0]||(_.Views[0]={}),_.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":x["!outline"]||(x["!outline"]={}),x["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":x["!outline"]||(x["!outline"]={}),x["!outline"].left=!0;break;default:ak=!1}break;case"pivottable":case"pivotcache":switch(r[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:ak=!1}break;case"pagebreaks":switch(r[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:ak=!1}break;case"autofilter":switch(r[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:ak=!1}break;case"querytable":switch(r[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:ak=!1}break;case"datavalidation":switch(r[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:ak=!1}break;case"sorting":case"conditionalformatting":switch(r[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:ak=!1}break;case"mapinfo":case"schema":case"data":switch(r[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:ak=!1}break;case"smarttags":break;default:ak=!1}if(ak||r[3].match(/!\[CDATA/))break;if(!u[u.length-1][1])throw"Unrecognized tag: "+r[3]+"|"+u.join("|");if("customdocumentproperties"===u[u.length-1][0]){"/>"===r[0].slice(-2)||("/"===r[1]?function(a,b,c,d){var e=d;switch((c[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":e=bx(d);break;case"i2":case"int":e=parseInt(d,10);break;case"r4":case"float":e=parseFloat(d);break;case"date":case"dateTime.tz":e=a1(d);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw Error("bad custprop:"+c[0])}a[bs(b)]=e}(N,ab,Q,j.slice(P,r.index)):(Q=r,P=r.index+r[0].length));break}if(i.WTF)throw"Unrecognized tag: "+r[3]+"|"+u.join("|")}var an={};return i.bookSheets||i.bookProps||(an.Sheets=v),an.SheetNames=w,an.Workbook=_,an.SSF=a3(al),an.Props=L,an.Custprops=N,an}function fv(a,b){switch(fS(b=b||{}),b.type||"base64"){case"base64":return fu(T(a),b);case"binary":case"buffer":case"file":return fu(a,b);case"array":return fu(Z(a),b)}}var fw=[60,1084,2066,2165,2175];function fx(a,b,c){if("z"!==a.t&&a.XF){var d=0;try{d=a.z||a.XF.numFmtId||0,b.cellNF&&(a.z=al[d])}catch(a){if(b.WTF)throw a}if(!b||!1!==b.cellText)try{"e"===a.t?a.w=a.w||cT[a.v]:0===d||"General"==d?"n"===a.t?(0|a.v)===a.v?a.w=a.v.toString(10):a.w=av(a.v):a.w=aw(a.v):a.w=aL(d,a.v,{date1904:!!c,dateNF:b&&b.dateNF})}catch(a){if(b.WTF)throw a}if(b.cellDates&&d&&"n"==a.t&&aI(al[d]||String(d))){var e=ap(a.v);e&&(a.t="d",a.v=new Date(e.y,e.m-1,e.d,e.H,e.M,e.S,e.u))}}}function fy(a,b,c){return{v:a,ixfe:b,t:c}}var fz={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae"};function fA(a,b){if(b||(b={}),fS(b),L(),b.codepage&&J(b.codepage),a.FullPaths){if(aR.find(a,"/encryption"))throw Error("File is password-protected");e=aR.find(a,"!CompObj"),f=aR.find(a,"/Workbook")||aR.find(a,"/Book")}else{switch(b.type){case"base64":a=Y(T(a));break;case"binary":a=Y(a);break;case"buffer":break;case"array":Array.isArray(a)||(a=Array.prototype.slice.call(a))}ci(a,0),f={content:a}}if(e&&function(a){var b={},c=a.content;if(c.l=28,b.AnsiUserType=c.read_shift(0,"lpstr-ansi"),b.AnsiClipboardFormat=cM(c,1),c.length-c.l<=4)return;var d=c.read_shift(4);if(0!=d&&!(d>40)&&(c.l-=4,b.Reserved1=c.read_shift(0,"lpstr-ansi"),!(c.length-c.l<=4)&&0x71b239f4===(d=c.read_shift(4)))&&(b.UnicodeClipboardFormat=cM(c,2),0!=(d=c.read_shift(4))&&!(d>40)))c.l-=4,b.Reserved2=c.read_shift(0,"lpwstr")}(e),b.bookProps&&!b.bookSheets)g={};else{var c,d,e,f,g,h,i=U?"buffer":"array";if(f&&f.content)g=function(a,b){var c,d,e,f,g={opts:{}},h={},i,j,k,l,m,n=b.dense?[]:{},o={},p={},q=null,r=[],s="",t={},u="",v={},w=[],x=[],y=[],z={Sheets:[],WBProps:{date1904:!1},Views:[{}]},A={},B=function(a){return a<8?cS[a]:a<64&&y[a-8]||cS[a]},C=function(a,b,c){var d,e=b.XF.data;e&&e.patternType&&c&&c.cellStyles&&(b.s={},b.s.patternType=e.patternType,(d=d5(B(e.icvFore)))&&(b.s.fgColor={rgb:d}),(d=d5(B(e.icvBack)))&&(b.s.bgColor={rgb:d}))},D=function(a,b,c){if(!(N>1)&&(!c.sheetRows||!(a.r>=c.sheetRows))){if(c.cellStyles&&b.XF&&b.XF.data&&C(a,b,c),delete b.ixfe,delete b.XF,i=a,u=cw(a),p&&p.s&&p.e||(p={s:{r:0,c:0},e:{r:0,c:0}}),a.r<p.s.r&&(p.s.r=a.r),a.c<p.s.c&&(p.s.c=a.c),a.r+1>p.e.r&&(p.e.r=a.r+1),a.c+1>p.e.c&&(p.e.c=a.c+1),c.cellFormula&&b.f){for(var d=0;d<w.length;++d)if(!(w[d][0].s.c>a.c)&&!(w[d][0].s.r>a.r)&&!(w[d][0].e.c<a.c)&&!(w[d][0].e.r<a.r)){b.F=cy(w[d][0]),(w[d][0].s.c!=a.c||w[d][0].s.r!=a.r)&&delete b.f,b.f&&(b.f=""+eS(w[d][1],p,a,L,E));break}}c.dense?(n[a.r]||(n[a.r]=[]),n[a.r][a.c]=b):n[u]=b}},E={enc:!1,sbcch:0,snames:[],sharedf:v,arrayf:w,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!b&&!!b.cellStyles,WTF:!!b&&!!b.wtf};b.password&&(E.password=b.password);var F=[],G=[],H=[],I=[],J=!1,L=[];L.SheetNames=E.snames,L.sharedf=E.sharedf,L.arrayf=E.arrayf,L.names=[],L.XTI=[];var M=0,N=0,O=0,P=[],Q=[];E.codepage=1200,K(1200);for(var R=!1;a.l<a.length-1;){var S=a.l,T=a.read_shift(2);if(0===T&&10===M)break;var U=a.l===a.length?0:a.read_shift(2),V=fC[T];if(V&&V.f){if(b.bookSheets&&133===M&&133!==T)break;if(M=T,2===V.r||12==V.r){var W=a.read_shift(2);if(U-=2,!E.enc&&W!==T&&((255&W)<<8|W>>8)!==T)throw Error("rt mismatch: "+W+"!="+T);12==V.r&&(a.l+=10,U-=10)}var X={};if(X=10===T?V.f(a,U,E):function(a,b,c,d,e){var f=d,g=[],h=c.slice(c.l,c.l+f);if(e&&e.enc&&e.enc.insitu&&h.length>0)switch(a){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:case 133:break;default:e.enc.insitu(h)}g.push(h),c.l+=f;for(var i=b8(c,c.l),j=fC[i],k=0;null!=j&&fw.indexOf(i)>-1;)f=b8(c,c.l+2),k=c.l+4,2066==i?k+=4:(2165==i||2175==i)&&(k+=12),h=c.slice(k,c.l+4+f),g.push(h),c.l+=4+f,j=fC[i=b8(c,c.l)];var l=aa(g);ci(l,0);var m=0;l.lens=[];for(var n=0;n<g.length;++n)l.lens.push(m),m+=g[n].length;if(l.length<d)throw"XLS Record 0x"+a.toString(16)+" Truncated: "+l.length+" < "+d;return b.f(l,l.length,e)}(T,V,a,U,E),0==N&&-1===[9,521,1033,2057].indexOf(M))continue;switch(T){case 34:g.opts.Date1904=z.WBProps.date1904=X;break;case 134:g.opts.WriteProtect=!0;break;case 47:if(E.enc||(a.l=0),E.enc=X,!b.password)throw Error("File is password-protected");if(null==X.valid)throw Error("Encryption scheme unsupported");if(!X.valid)throw Error("Password is incorrect");break;case 92:E.lastuser=X;break;case 66:var Y=Number(X);switch(Y){case 21010:Y=1200;break;case 32768:Y=1e4;break;case 32769:Y=1252}K(E.codepage=Y),R=!0;break;case 317:E.rrtabid=X;break;case 25:E.winlocked=X;break;case 439:g.opts.RefreshAll=X;break;case 12:g.opts.CalcCount=X;break;case 16:g.opts.CalcDelta=X;break;case 17:g.opts.CalcIter=X;break;case 13:g.opts.CalcMode=X;break;case 14:g.opts.CalcPrecision=X;break;case 95:g.opts.CalcSaveRecalc=X;break;case 15:E.CalcRefMode=X;break;case 2211:g.opts.FullCalc=X;break;case 129:X.fDialog&&(n["!type"]="dialog"),X.fBelow||((n["!outline"]||(n["!outline"]={})).above=!0),X.fRight||((n["!outline"]||(n["!outline"]={})).left=!0);break;case 224:x.push(X);break;case 430:L.push([X]),L[L.length-1].XTI=[];break;case 35:case 547:L[L.length-1].push(X);break;case 24:case 536:f={Name:X.Name,Ref:eS(X.rgce,p,null,L,E)},X.itab>0&&(f.Sheet=X.itab-1),L.names.push(f),L[0]||(L[0]=[],L[0].XTI=[]),L[L.length-1].push(X),"_xlnm._FilterDatabase"==X.Name&&X.itab>0&&X.rgce&&X.rgce[0]&&X.rgce[0][0]&&"PtgArea3d"==X.rgce[0][0][0]&&(Q[X.itab-1]={ref:cy(X.rgce[0][0][1][2])});break;case 22:E.ExternCount=X;break;case 23:0==L.length&&(L[0]=[],L[0].XTI=[]),L[L.length-1].XTI=L[L.length-1].XTI.concat(X),L.XTI=L.XTI.concat(X);break;case 2196:if(E.biff<8)break;null!=f&&(f.Comment=X[1]);break;case 18:n["!protect"]=X;break;case 19:0!==X&&E.WTF&&console.error("Password verifier: "+X);break;case 133:o[X.pos]=X,E.snames.push(X.name);break;case 10:if(--N)break;if(p.e){if(p.e.r>0&&p.e.c>0){if(p.e.r--,p.e.c--,n["!ref"]=cy(p),b.sheetRows&&b.sheetRows<=p.e.r){var Z=p.e.r;p.e.r=b.sheetRows-1,n["!fullref"]=n["!ref"],n["!ref"]=cy(p),p.e.r=Z}p.e.r++,p.e.c++}F.length>0&&(n["!merges"]=F),G.length>0&&(n["!objects"]=G),H.length>0&&(n["!cols"]=H),I.length>0&&(n["!rows"]=I),z.Sheets.push(A)}""===s?t=n:h[s]=n,n=b.dense?[]:{};break;case 9:case 521:case 1033:case 2057:if(8===E.biff&&(E.biff=({9:2,521:3,1033:4})[T]||({512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2})[X.BIFFVer]||8),E.biffguess=0==X.BIFFVer,0==X.BIFFVer&&4096==X.dt&&(E.biff=5,R=!0,K(E.codepage=28591)),8==E.biff&&0==X.BIFFVer&&16==X.dt&&(E.biff=2),N++)break;if(n=b.dense?[]:{},E.biff<8&&!R&&(R=!0,K(E.codepage=b.codepage||1252)),E.biff<5||0==X.BIFFVer&&4096==X.dt){""===s&&(s="Sheet1"),p={s:{r:0,c:0},e:{r:0,c:0}};var _={pos:a.l-U,name:s};o[_.pos]=_,E.snames.push(s)}else s=(o[S]||{name:""}).name;32==X.dt&&(n["!type"]="chart"),64==X.dt&&(n["!type"]="macro"),F=[],G=[],E.arrayf=w=[],H=[],I=[],J=!1,A={Hidden:(o[S]||{hs:0}).hs,name:s};break;case 515:case 3:case 2:"chart"==n["!type"]&&(b.dense?(n[X.r]||[])[X.c]:n[cw({c:X.c,r:X.r})])&&++X.c,c={ixfe:X.ixfe,XF:x[X.ixfe]||{},v:X.val,t:"n"},O>0&&(c.z=P[c.ixfe>>8&63]),fx(c,b,g.opts.Date1904),D({c:X.c,r:X.r},c,b);break;case 5:case 517:c={ixfe:X.ixfe,XF:x[X.ixfe],v:X.val,t:X.t},O>0&&(c.z=P[c.ixfe>>8&63]),fx(c,b,g.opts.Date1904),D({c:X.c,r:X.r},c,b);break;case 638:c={ixfe:X.ixfe,XF:x[X.ixfe],v:X.rknum,t:"n"},O>0&&(c.z=P[c.ixfe>>8&63]),fx(c,b,g.opts.Date1904),D({c:X.c,r:X.r},c,b);break;case 189:for(var ab=X.c;ab<=X.C;++ab){var ac=X.rkrec[ab-X.c][0];c={ixfe:ac,XF:x[ac],v:X.rkrec[ab-X.c][1],t:"n"},O>0&&(c.z=P[c.ixfe>>8&63]),fx(c,b,g.opts.Date1904),D({c:ab,r:X.r},c,b)}break;case 6:case 518:case 1030:if("String"==X.val){q=X;break}if((c=fy(X.val,X.cell.ixfe,X.tt)).XF=x[c.ixfe],b.cellFormula){var ad=X.formula;if(ad&&ad[0]&&ad[0][0]&&"PtgExp"==ad[0][0][0]){var ae=ad[0][0][1][0],af=ad[0][0][1][1],ag=cw({r:ae,c:af});v[ag]?c.f=""+eS(X.formula,p,X.cell,L,E):c.F=((b.dense?(n[ae]||[])[af]:n[ag])||{}).F}else c.f=""+eS(X.formula,p,X.cell,L,E)}O>0&&(c.z=P[c.ixfe>>8&63]),fx(c,b,g.opts.Date1904),D(X.cell,c,b),q=X;break;case 7:case 519:if(q)q.val=X,(c=fy(X,q.cell.ixfe,"s")).XF=x[c.ixfe],b.cellFormula&&(c.f=""+eS(q.formula,p,q.cell,L,E)),O>0&&(c.z=P[c.ixfe>>8&63]),fx(c,b,g.opts.Date1904),D(q.cell,c,b),q=null;else throw Error("String record expects Formula");break;case 33:case 545:w.push(X);var ah=cw(X[0].s);if(j=b.dense?(n[X[0].s.r]||[])[X[0].s.c]:n[ah],b.cellFormula&&j){if(!q||!ah||!j)break;j.f=""+eS(X[1],p,X[0],L,E),j.F=cy(X[0])}break;case 1212:if(!b.cellFormula)break;if(u){if(!q)break;v[cw(q.cell)]=X[0],((j=b.dense?(n[q.cell.r]||[])[q.cell.c]:n[cw(q.cell)])||{}).f=""+eS(X[0],p,i,L,E)}break;case 253:c=fy(r[X.isst].t,X.ixfe,"s"),r[X.isst].h&&(c.h=r[X.isst].h),c.XF=x[c.ixfe],O>0&&(c.z=P[c.ixfe>>8&63]),fx(c,b,g.opts.Date1904),D({c:X.c,r:X.r},c,b);break;case 513:b.sheetStubs&&(c={ixfe:X.ixfe,XF:x[X.ixfe],t:"z"},O>0&&(c.z=P[c.ixfe>>8&63]),fx(c,b,g.opts.Date1904),D({c:X.c,r:X.r},c,b));break;case 190:if(b.sheetStubs)for(var ai=X.c;ai<=X.C;++ai){var aj=X.ixfe[ai-X.c];c={ixfe:aj,XF:x[aj],t:"z"},O>0&&(c.z=P[c.ixfe>>8&63]),fx(c,b,g.opts.Date1904),D({c:ai,r:X.r},c,b)}break;case 214:case 516:case 4:(c=fy(X.val,X.ixfe,"s")).XF=x[c.ixfe],O>0&&(c.z=P[c.ixfe>>8&63]),fx(c,b,g.opts.Date1904),D({c:X.c,r:X.r},c,b);break;case 0:case 512:1===N&&(p=X);break;case 252:r=X;break;case 1054:if(4==E.biff){P[O++]=X[1];for(var ak=0;ak<O+163&&al[ak]!=X[1];++ak);ak>=163&&aM(X[1],O+163)}else aM(X[1],X[0]);break;case 30:P[O++]=X;for(var am=0;am<O+163&&al[am]!=X;++am);am>=163&&aM(X,O+163);break;case 229:F=F.concat(X);break;case 93:G[X.cmo[0]]=E.lastobj=X;break;case 438:E.lastobj.TxO=X;break;case 127:E.lastobj.ImData=X;break;case 440:for(m=X[0].s.r;m<=X[0].e.r;++m)for(l=X[0].s.c;l<=X[0].e.c;++l)(j=b.dense?(n[m]||[])[l]:n[cw({c:l,r:m})])&&(j.l=X[1]);break;case 2048:for(m=X[0].s.r;m<=X[0].e.r;++m)for(l=X[0].s.c;l<=X[0].e.c;++l)(j=b.dense?(n[m]||[])[l]:n[cw({c:l,r:m})])&&j.l&&(j.l.Tooltip=X[1]);break;case 28:if(E.biff<=5&&E.biff>=2)break;j=b.dense?(n[X[0].r]||[])[X[0].c]:n[cw(X[0])];var an=G[X[2]];j||(b.dense?(n[X[0].r]||(n[X[0].r]=[]),j=n[X[0].r][X[0].c]={t:"z"}):j=n[cw(X[0])]={t:"z"},p.e.r=Math.max(p.e.r,X[0].r),p.s.r=Math.min(p.s.r,X[0].r),p.e.c=Math.max(p.e.c,X[0].c),p.s.c=Math.min(p.s.c,X[0].c)),j.c||(j.c=[]),k={a:X[1],t:an.TxO.t},j.c.push(k);break;case 2173:x[X.ixfe],X.ext.forEach(function(a){a[0]});break;case 125:if(!E.cellStyles)break;for(;X.e>=X.s;)H[X.e--]={width:X.w/256,level:X.level||0,hidden:!!(1&X.flags)},J||(J=!0,ec(X.w/256)),ed(H[X.e+1]);break;case 520:var ao={};null!=X.level&&(I[X.r]=ao,ao.level=X.level),X.hidden&&(I[X.r]=ao,ao.hidden=!0),X.hpt&&(I[X.r]=ao,ao.hpt=X.hpt,ao.hpx=ee(X.hpt));break;case 38:case 39:case 40:case 41:n["!margins"]||e0(n["!margins"]={}),n["!margins"][({38:"left",39:"right",40:"top",41:"bottom"})[T]]=X;break;case 161:n["!margins"]||e0(n["!margins"]={}),n["!margins"].header=X.header,n["!margins"].footer=X.footer;break;case 574:X.RTL&&(z.Views[0].RTL=!0);break;case 146:y=X;break;case 2198:e=X;break;case 140:d=X;break;case 442:s?A.CodeName=X||A.name:z.WBProps.CodeName=X||"ThisWorkbook"}}else V||console.error("Missing Info for XLS Record 0x"+T.toString(16)),a.l+=U}return g.SheetNames=aS(o).sort(function(a,b){return Number(a)-Number(b)}).map(function(a){return o[a].name}),b.bookSheets||(g.Sheets=h),!g.SheetNames.length&&t["!ref"]?(g.SheetNames.push("Sheet1"),g.Sheets&&(g.Sheets.Sheet1=t)):g.Preamble=t,g.Sheets&&Q.forEach(function(a,b){g.Sheets[g.SheetNames[b]]["!autofilter"]=a}),g.Strings=r,g.SSF=a3(al),E.enc&&(g.Encryption=E.enc),e&&(g.Themes=e),g.Metadata={},void 0!==d&&(g.Metadata.Country=d),L.names.length>0&&(z.Names=L.names),g.Workbook=z,g}(f.content,b);else if((h=aR.find(a,"PerfectOffice_MAIN"))&&h.content)g=dP.to_workbook(h.content,(b.type=i,b));else if((h=aR.find(a,"NativeContent_MAIN"))&&h.content)g=dP.to_workbook(h.content,(b.type=i,b));else if((h=aR.find(a,"MN0"))&&h.content)throw Error("Unsupported Works 4 for Mac file");else throw Error("Cannot find Workbook stream");b.bookVBA&&a.FullPaths&&aR.find(a,"/_VBA_PROJECT_CUR/VBA/dir")&&(g.vbaraw=(c=a,d=aR.utils.cfb_new({root:"R"}),c.FullPaths.forEach(function(a,b){if("/"!==a.slice(-1)&&a.match(/_VBA_PROJECT_CUR/)){var e=a.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");aR.utils.cfb_add(d,e,c.FileIndex[b].content)}}),aR.write(d)))}var j={};return a.FullPaths&&function(a,b,c){var d=aR.find(a,"/!DocumentSummaryInformation");if(d&&d.size>0)try{var e=dd(d,cO,fz.DSI);for(var f in e)b[f]=e[f]}catch(a){if(c.WTF)throw a}var g=aR.find(a,"/!SummaryInformation");if(g&&g.size>0)try{var h=dd(g,cP,fz.SI);for(var i in h)null==b[i]&&(b[i]=h[i])}catch(a){if(c.WTF)throw a}b.HeadingPairs&&b.TitlesOfParts&&(c1(b.HeadingPairs,b.TitlesOfParts,b,c),delete b.HeadingPairs,delete b.TitlesOfParts)}(a,j,b),g.Props=g.Custprops=j,b.bookFiles&&(g.cfb=a),g}var fB={0:{f:function(a,b){var c={},d=a.l+b;c.r=a.read_shift(4),a.l+=4;var e=a.read_shift(2);a.l+=1;var f=a.read_shift(1);return a.l=d,7&f&&(c.level=7&f),16&f&&(c.hidden=!0),32&f&&(c.hpt=e/20),c}},1:{f:function(a){return[cG(a)]}},2:{f:function(a){return[cG(a),cJ(a),"n"]}},3:{f:function(a){return[cG(a),a.read_shift(1),"e"]}},4:{f:function(a){return[cG(a),a.read_shift(1),"b"]}},5:{f:function(a){return[cG(a),cL(a),"n"]}},6:{f:function(a){return[cG(a),cE(a),"str"]}},7:{f:function(a){return[cG(a),a.read_shift(4),"s"]}},8:{f:function(a,b,c){var d=a.l+b,e=cG(a);e.r=c["!row"];var f=[e,cE(a),"str"];if(c.cellFormula){a.l+=2;var g=eU(a,d-a.l,c);f[3]=eS(g,null,e,c.supbooks,c)}else a.l=d;return f}},9:{f:function(a,b,c){var d=a.l+b,e=cG(a);e.r=c["!row"];var f=[e,cL(a),"n"];if(c.cellFormula){a.l+=2;var g=eU(a,d-a.l,c);f[3]=eS(g,null,e,c.supbooks,c)}else a.l=d;return f}},10:{f:function(a,b,c){var d=a.l+b,e=cG(a);e.r=c["!row"];var f=[e,a.read_shift(1),"b"];if(c.cellFormula){a.l+=2;var g=eU(a,d-a.l,c);f[3]=eS(g,null,e,c.supbooks,c)}else a.l=d;return f}},11:{f:function(a,b,c){var d=a.l+b,e=cG(a);e.r=c["!row"];var f=[e,a.read_shift(1),"e"];if(c.cellFormula){a.l+=2;var g=eU(a,d-a.l,c);f[3]=eS(g,null,e,c.supbooks,c)}else a.l=d;return f}},12:{f:function(a){return[cH(a)]}},13:{f:function(a){return[cH(a),cJ(a),"n"]}},14:{f:function(a){return[cH(a),a.read_shift(1),"e"]}},15:{f:function(a){return[cH(a),a.read_shift(1),"b"]}},16:{f:ff},17:{f:function(a){return[cH(a),cE(a),"str"]}},18:{f:function(a){return[cH(a),a.read_shift(4),"s"]}},19:{f:cF},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(a,b,c){var d=a.l+b;a.l+=4,a.l+=1;var e=a.read_shift(4),f=cE(a),g=eU(a,0,c),h=cI(a);a.l=d;var i={Name:f,Ptg:g};return e<0xfffffff&&(i.Sheet=e),h&&(i.Comment=h),i}},40:{},42:{},43:{f:function(a,b,c){var d,e={};e.sz=a.read_shift(2)/20;var f=(d=a.read_shift(1),a.l++,{fBold:1&d,fItalic:2&d,fUnderline:4&d,fStrikeout:8&d,fOutline:16&d,fShadow:32&d,fCondense:64&d,fExtend:128&d});switch(f.fItalic&&(e.italic=1),f.fCondense&&(e.condense=1),f.fExtend&&(e.extend=1),f.fShadow&&(e.shadow=1),f.fOutline&&(e.outline=1),f.fStrikeout&&(e.strike=1),700===a.read_shift(2)&&(e.bold=1),a.read_shift(2)){case 1:e.vertAlign="superscript";break;case 2:e.vertAlign="subscript"}var g=a.read_shift(1);0!=g&&(e.underline=g);var h=a.read_shift(1);h>0&&(e.family=h);var i=a.read_shift(1);switch(i>0&&(e.charset=i),a.l++,e.color=function(a){var b={},c=a.read_shift(1),d=a.read_shift(1),e=a.read_shift(2,"i"),f=a.read_shift(1),g=a.read_shift(1),h=a.read_shift(1);switch(a.l++,c>>>1){case 0:b.auto=1;break;case 1:b.index=d;var i=cS[d];i&&(b.rgb=d5(i));break;case 2:b.rgb=d5([f,g,h]);break;case 3:b.theme=d}return 0!=e&&(b.tint=e>0?e/32767:e/32768),b}(a,8),a.read_shift(1)){case 1:e.scheme="major";break;case 2:e.scheme="minor"}return e.name=cE(a,b-21),e}},44:{f:function(a,b){return[a.read_shift(2),cE(a,b-2)]}},45:{f:cj},46:{f:cj},47:{f:function(a,b){var c=a.l+b,d=a.read_shift(2),e=a.read_shift(2);return a.l=c,{ixfe:d,numFmtId:e}}},48:{},49:{f:function(a){return a.read_shift(4,"i")}},50:{},51:{f:function(a){for(var b=[],c=a.read_shift(4);c-- >0;)b.push([a.read_shift(4),a.read_shift(4)]);return b}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:dI},62:{f:function(a){return[cG(a),cF(a),"is"]}},63:{f:function(a){var b={};b.i=a.read_shift(4);var c={};c.r=a.read_shift(4),c.c=a.read_shift(4),b.r=cw(c);var d=a.read_shift(1);return 2&d&&(b.l="1"),8&d&&(b.a="1"),b}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:cj,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(a){var b=a.read_shift(2);return a.l+=28,{RTL:32&b}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(a,b){var c={},d=a[a.l];return++a.l,c.above=!(64&d),c.left=!(128&d),a.l+=18,c.name=cE(a,b-19),c}},148:{f:cK,p:16},151:{f:function(){}},152:{},153:{f:function(a,b){var c={},d=a.read_shift(4);c.defaultThemeVersion=a.read_shift(4);var e=b>8?cE(a):"";return e.length>0&&(c.CodeName=e),c.autoCompressPictures=!!(65536&d),c.backupFile=!!(64&d),c.checkCompatibility=!!(4096&d),c.date1904=!!(1&d),c.filterPrivacy=!!(8&d),c.hidePivotFieldList=!!(1024&d),c.promptedSolutions=!!(16&d),c.publishItems=!!(2048&d),c.refreshAllConnections=!!(262144&d),c.saveExternalLinkValues=!!(128&d),c.showBorderUnselectedTables=!!(4&d),c.showInkAnnotation=!!(32&d),c.showObjects=["all","placeholders","none"][d>>13&3],c.showPivotChartFilter=!!(32768&d),c.updateLinks=["userSet","never","always"][d>>8&3],c}},154:{},155:{},156:{f:function(a,b){var c={};return c.Hidden=a.read_shift(4),c.iTabID=a.read_shift(4),c.strRelID=cI(a,b-8),c.name=cE(a),c}},157:{},158:{},159:{T:1,f:function(a){return[a.read_shift(4),a.read_shift(4)]}},160:{T:-1},161:{T:1,f:cK},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:cK},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(a,b){return{flags:a.read_shift(4),version:a.read_shift(4),name:cE(a,b-8)}}},336:{T:-1},337:{f:function(a){return a.l+=4,0!=a.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:cI},357:{},358:{},359:{},360:{T:1},361:{},362:{f:dF},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(a,b,c){var d=a.l+b,e=cK(a,16),f=a.read_shift(1),g=[e];if(g[2]=f,c.cellFormula){var h=eU(a,d-a.l,c);g[1]=h}else a.l=d;return g}},427:{f:function(a,b,c){var d=a.l+b,e=[cK(a,16)];if(c.cellFormula){var f=eU(a,d-a.l,c);e[1]=f,a.l=d}else a.l=d;return e}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(a){var b={};return fg.forEach(function(c){b[c]=cL(a,8)}),b}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(a,b){var c=a.l+b,d=cK(a,16),e=cI(a),f=cE(a),g=cE(a),h=cE(a);a.l=c;var i={rfx:d,relId:e,loc:f,display:h};return g&&(i.Tooltip=g),i}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:cI},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:cE},633:{T:1},634:{T:-1},635:{T:1,f:function(a){var b={};b.iauthor=a.read_shift(4);var c=cK(a,16);return b.rfx=c.s,b.ref=cw(c.s),a.l+=16,b}},636:{T:-1},637:{f:cF},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(a,b){return a.l+=10,{name:cE(a,b-10)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},fC={6:{f:eT},10:{f:de},12:{f:dg},13:{f:dg},14:{f:df},15:{f:df},16:{f:cL},17:{f:df},18:{f:df},19:{f:dg},20:{f:dB},21:{f:dB},23:{f:dF},24:{f:dE},25:{f:df},26:{},27:{},28:{f:function(a,b,c){return function(a,b,c){if(!(c.biff<8)){var d=a.read_shift(2),e=a.read_shift(2),f=a.read_shift(2),g=a.read_shift(2),h=dl(a,0,c);return c.biff<8&&a.read_shift(1),[{r:d,c:e},h,g,f]}}(a,0,c)}},29:{},34:{f:df},35:{f:dC},38:{f:cL},39:{f:cL},40:{f:cL},41:{f:cL},42:{f:df},43:{f:df},47:{f:function(a,b,c){var d,e,f,g={Type:c.biff>=8?a.read_shift(2):0};return g.Type?(d=b-2,(e=g||{}).Info=a.read_shift(2),a.l-=2,1===e.Info?e.Data=function(a){var b={},c=b.EncryptionVersionInfo=d$(a,4);if(1!=c.Major||1!=c.Minor)throw"unrecognized version code "+c.Major+" : "+c.Minor;return b.Salt=a.read_shift(16),b.EncryptedVerifier=a.read_shift(16),b.EncryptedVerifierHash=a.read_shift(16),b}(a,d):e.Data=function(a,b){var c={},d=c.EncryptionVersionInfo=d$(a,4);if(b-=4,2!=d.Minor)throw Error("unrecognized minor version code: "+d.Minor);if(d.Major>4||d.Major<2)throw Error("unrecognized major version code: "+d.Major);c.Flags=a.read_shift(4),b-=4;var e=a.read_shift(4);return b-=4,c.EncryptionHeader=d_(a,e),c.EncryptionVerifier=d0(a,b-=e),c}(a,d)):(c.biff,f={key:dg(a),verificationBytes:dg(a)},c.password&&(f.verifier=function(a){var b,c,d=0,e=dZ(a),f=e.length+1;for(c=1,(b=W(f))[0]=e.length;c!=f;++c)b[c]=e[c-1];for(c=f-1;c>=0;--c)d=((16384&d)!=0|d<<1&32767)^b[c];return 52811^d}(c.password)),g.valid=f.verificationBytes===f.verifier,g.valid&&(g.insitu=d3(c.password))),g}},49:{f:function(a,b,c){var d={dyHeight:a.read_shift(2),fl:a.read_shift(2)};switch(c&&c.biff||8){case 2:break;case 3:case 4:a.l+=2;break;default:a.l+=10}return d.name=di(a,0,c),d}},51:{f:dg},60:{},61:{f:function(a){return{Pos:[a.read_shift(2),a.read_shift(2)],Dim:[a.read_shift(2),a.read_shift(2)],Flags:a.read_shift(2),CurTab:a.read_shift(2),FirstTab:a.read_shift(2),Selected:a.read_shift(2),TabRatio:a.read_shift(2)}}},64:{f:df},65:{f:function(){}},66:{f:dg},77:{},80:{},81:{},82:{},85:{f:dg},89:{},90:{},91:{},92:{f:function(a,b,c){if(c.enc)return a.l+=b,"";var d=a.l,e=dl(a,0,c);return a.read_shift(b+d-a.l),e}},93:{f:function(a,b,c){if(c&&c.biff<8){var d,e,f,g,h,i,j;return d=a,e=b,f=c,d.l+=4,g=d.read_shift(2),h=d.read_shift(2),i=d.read_shift(2),d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=2,d.l+=6,e-=36,(j=[]).push((dH[g]||cj)(d,e,f)),{cmo:[h,g,i],ft:j}}var k=du(a,22),l=function(a,b){for(var c=a.l+b,d=[];a.l<c;){var e=a.read_shift(2);a.l-=2;try{d.push(dw[e](a,c-a.l))}catch(b){return a.l=c,d}}return a.l!=c&&(a.l=c),d}(a,b-22,k[1]);return{cmo:k,ft:l}}},94:{},95:{f:df},96:{},97:{},99:{f:df},125:{f:dI},128:{f:function(a){a.l+=4;var b=[a.read_shift(2),a.read_shift(2)];if(0!==b[0]&&b[0]--,0!==b[1]&&b[1]--,b[0]>7||b[1]>7)throw Error("Bad Gutters: "+b.join("|"));return b}},129:{f:function(a,b,c){var d=c&&8==c.biff||2==b?a.read_shift(2):(a.l+=b,0);return{fDialog:16&d,fBelow:64&d,fRight:128&d}}},130:{f:dg},131:{f:df},132:{f:df},133:{f:function(a,b,c){var d=a.read_shift(4),e=3&a.read_shift(1),f=a.read_shift(1);switch(f){case 0:f="Worksheet";break;case 1:f="Macrosheet";break;case 2:f="Chartsheet";break;case 6:f="VBAModule"}var g=di(a,0,c);return 0===g.length&&(g="Sheet1"),{pos:d,hs:e,dt:f,name:g}}},134:{},140:{f:function(a){var b,c=[0,0];return b=a.read_shift(2),c[0]=cQ[b]||b,b=a.read_shift(2),c[1]=cQ[b]||b,c}},141:{f:dg},144:{},146:{f:function(a){for(var b=a.read_shift(2),c=[];b-- >0;)c.push(dp(a,8));return c}},151:{},152:{},153:{},154:{},155:{},156:{f:dg},157:{},158:{},160:{f:dh},161:{f:function(a,b){var c={};return b<32||(a.l+=16,c.header=cL(a,8),c.footer=cL(a,8),a.l+=2),c}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(a,b){for(var c=a.l+b-2,d=a.read_shift(2),e=a.read_shift(2),f=[];a.l<c;)f.push(dr(a));if(a.l!==c)throw Error("MulRK read error");var g=a.read_shift(2);if(f.length!=g-e+1)throw Error("MulRK length mismatch");return{r:d,c:e,C:g,rkrec:f}}},190:{f:function(a,b){for(var c=a.l+b-2,d=a.read_shift(2),e=a.read_shift(2),f=[];a.l<c;)f.push(a.read_shift(2));if(a.l!==c)throw Error("MulBlank read error");var g=a.read_shift(2);if(f.length!=g-e+1)throw Error("MulBlank length mismatch");return{r:d,c:e,C:g,ixfe:f}}},193:{f:de},197:{},198:{},199:{},200:{},201:{},202:{f:df},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:dg},220:{},221:{f:df},222:{},224:{f:function(a,b,c){var d,e,f,g,h,i={};return i.ifnt=a.read_shift(2),i.numFmtId=a.read_shift(2),i.flags=a.read_shift(2),i.fStyle=i.flags>>2&1,b-=6,i.fStyle,d={},e=a.read_shift(4),f=a.read_shift(4),g=a.read_shift(4),h=a.read_shift(2),d.patternType=cR[g>>26],c.cellStyles&&(d.alc=7&e,d.fWrap=e>>3&1,d.alcV=e>>4&7,d.fJustLast=e>>7&1,d.trot=e>>8&255,d.cIndent=e>>16&15,d.fShrinkToFit=e>>20&1,d.iReadOrder=e>>22&2,d.fAtrNum=e>>26&1,d.fAtrFnt=e>>27&1,d.fAtrAlc=e>>28&1,d.fAtrBdr=e>>29&1,d.fAtrPat=e>>30&1,d.fAtrProt=e>>31&1,d.dgLeft=15&f,d.dgRight=f>>4&15,d.dgTop=f>>8&15,d.dgBottom=f>>12&15,d.icvLeft=f>>16&127,d.icvRight=f>>23&127,d.grbitDiag=f>>30&3,d.icvTop=127&g,d.icvBottom=g>>7&127,d.icvDiag=g>>14&127,d.dgDiag=g>>21&15,d.icvFore=127&h,d.icvBack=h>>7&127,d.fsxButton=h>>14&1),i.data=d,i}},225:{f:function(a,b){return 0===b||a.read_shift(2),1200}},226:{f:de},227:{},229:{f:function(a,b){for(var c=[],d=a.read_shift(2);d--;)c.push(ds(a,b));return c}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(a,b){for(var c=a.l+b,d=a.read_shift(4),e=a.read_shift(4),f=[],g=0;g!=e&&a.l<c;++g)f.push(function(a){var b=F;F=1200;var c,d=a.read_shift(2),e=a.read_shift(1),f=4&e,g=8&e,h=0,i={};g&&(h=a.read_shift(2)),f&&(c=a.read_shift(4));var j=0===d?"":a.read_shift(d,2==1+(1&e)?"dbcs-cont":"sbcs-cont");return g&&(a.l+=4*h),f&&(a.l+=c),i.t=j,g||(i.raw="<t>"+i.t+"</t>",i.r=i.t),F=b,i}(a));return f.Count=d,f.Unique=e,f}},253:{f:function(a){var b=dq(a);return b.isst=a.read_shift(4),b}},255:{f:function(a,b){var c={};return c.dsst=a.read_shift(2),a.l+=b-2,c}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:dh},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:df},353:{f:de},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(a,b,c){var d=a.l+b,e=a.read_shift(2),f=a.read_shift(2);if(c.sbcch=f,1025==f||14849==f)return[f,e];if(f<1||f>255)throw Error("Unexpected SupBook type: "+f);for(var g=dj(a,f),h=[];d>a.l;)h.push(dk(a));return[f,e,g,h]}},431:{f:df},432:{},433:{},434:{},437:{},438:{f:function(a,b,c){var d=a.l,e="";try{a.l+=4;var f=(c.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(f)?a.l+=6:(a.read_shift(1),a.l++,a.read_shift(2),a.l+=2);var g=a.read_shift(2);a.read_shift(2),dg(a,2);var h=a.read_shift(2);a.l+=h;for(var i=1;i<a.lens.length-1;++i){if(a.l-d!=a.lens[i])throw Error("TxO: bad continue record");var j=a[a.l],k=dj(a,a.lens[i+1]-a.lens[i]-1);if((e+=k).length>=(j?g:2*g))break}if(e.length!==g&&e.length!==2*g)throw Error("cchText: "+g+" != "+e.length);return a.l=d+b,{t:e}}catch(c){return a.l=d+b,{t:e}}}},439:{f:df},440:{f:function(a,b){var c=ds(a,8);return a.l+=16,[c,function(a,b){var c=a.l+b,d=a.read_shift(4);if(2!==d)throw Error("Unrecognized streamVersion: "+d);var e=a.read_shift(2);a.l+=2;var f,g,h,i,j,k,l="";16&e&&(f=dm(a,c-a.l)),128&e&&(g=dm(a,c-a.l)),(257&e)==257&&(h=dm(a,c-a.l)),(257&e)==1&&(i=function(a,b){var c,d,e,f,g=a.read_shift(16);switch(b-=16,g){case"e0c9ea79f9bace118c8200aa004ba90b":return c=a.read_shift(4),d=a.l,e=!1,c>24&&(a.l+=c-24,"795881f43b1d7f48af2c825dc4852763"===a.read_shift(16)&&(e=!0),a.l=d),f=a.read_shift((e?c-24:c)>>1,"utf16le").replace(ab,""),e&&(a.l+=24),f;case"0303000000000000c000000000000046":for(var h=a.read_shift(2),i="";h-- >0;)i+="../";var j=a.read_shift(0,"lpstr-ansi");if(a.l+=2,57005!=a.read_shift(2))throw Error("Bad FileMoniker");if(0===a.read_shift(4))return i+j.replace(/\\/g,"/");var k=a.read_shift(4);if(3!=a.read_shift(2))throw Error("Bad FileMoniker");return i+a.read_shift(k>>1,"utf16le").replace(ab,"");default:throw Error("Unsupported Moniker "+g)}}(a,c-a.l)),8&e&&(l=dm(a,c-a.l)),32&e&&(j=a.read_shift(16)),64&e&&(k=c4(a)),a.l=c;var m=g||h||i||"";m&&l&&(m+="#"+l),m||(m="#"+l),2&e&&"/"==m.charAt(0)&&"/"!=m.charAt(1)&&(m="file://"+m);var n={Target:m};return j&&(n.guid=j),k&&(n.time=k),f&&(n.Tooltip=f),n}(a,b-24)]}},441:{},442:{f:dk},443:{},444:{f:dg},445:{},446:{},448:{f:de},449:{f:function(a){return a.read_shift(2),a.read_shift(4)},r:2},450:{f:de},512:{f:dz},513:{f:dq},515:{f:function(a,b,c){c.biffguess&&2==c.biff&&(c.biff=5);var d=dq(a,6);return d.val=cL(a,8),d}},516:{f:function(a,b,c){c.biffguess&&2==c.biff&&(c.biff=5);var d=a.l+b,e=dq(a,6);return 2==c.biff&&a.l++,e.val=dk(a,d-a.l,c),e}},517:{f:dA},519:{f:dk},520:{f:function(a){var b={};b.r=a.read_shift(2),b.c=a.read_shift(2),b.cnt=a.read_shift(2)-b.c;var c=a.read_shift(2);a.l+=4;var d=a.read_shift(1);return a.l+=3,7&d&&(b.level=7&d),32&d&&(b.hidden=!0),64&d&&(b.hpt=c/20),b}},523:{},545:{f:dG},549:{f:dy},566:{},574:{f:function(a,b,c){return c&&c.biff>=2&&c.biff<5?{}:{RTL:64&a.read_shift(2)}}},638:{f:function(a){var b=a.read_shift(2),c=a.read_shift(2),d=dr(a);return{r:b,c:c,ixfe:d[0],rknum:d[1]}}},659:{},1048:{},1054:{f:function(a,b,c){return[a.read_shift(2),dl(a,0,c)]}},1084:{},1212:{f:function(a,b,c){var d=dt(a,6);a.l++;var e=a.read_shift(1);return[function(a,b,c){var d,e,f=a.l+b,g=a.read_shift(2),h=eO(a,g,c);return 65535==g?[[],(d=b-2,void(a.l+=d))]:(b!==g+2&&(e=eN(a,f-g-2,h,c)),[h,e])}(a,b-=8,c),e,d]}},2048:{f:function(a,b){a.read_shift(2);var c=ds(a,8),d=a.read_shift((b-10)/2,"dbcs-cont");return[c,d=d.replace(ab,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:dx},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:de},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(a){a.l+=2;var b={cxfs:0,crc:0};return b.cxfs=a.read_shift(2),b.crc=a.read_shift(4),b},r:12},2173:{f:function(a,b){var c=a.l+b;a.l+=2;var d=a.read_shift(2);a.l+=2;for(var e=a.read_shift(2),f=[];e-- >0;)f.push(function(a){var b=a.read_shift(2),c=a.read_shift(2)-4,d=[b];switch(b){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:d[1]=function(a){var b={};switch(b.xclrType=a.read_shift(2),b.nTintShade=a.read_shift(2),b.xclrType){case 0:case 4:a.l+=4;break;case 1:b.xclrValue=function(a,b){a.l+=b}(a,4);break;case 2:b.xclrValue=dn(a,4);break;case 3:b.xclrValue=a.read_shift(4)}return a.l+=8,b}(a,c);break;case 6:d[1]=void(a.l+=c);break;case 14:case 15:d[1]=a.read_shift(1===c?1:2);break;default:throw Error("Unrecognized ExtProp type: "+b+" "+c)}return d}(a,c-a.l));return{ixfe:d,ext:f}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:df,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(a,b,c){if(c.biff<8){a.l+=b;return}var d=a.read_shift(2),e=a.read_shift(2);return[dj(a,d,c),dj(a,e,c)]},r:12},2197:{},2198:{f:function(a,b,c){var d,e=a.l+b;if(124226!==a.read_shift(4)){if(!c.cellStyles){a.l=e;return}var f=a.slice(a.l);a.l=e;try{d=bg(f,{type:"array"})}catch(a){return}var g=be(d,"theme/theme/theme1.xml",!0);if(g)return er(g,c)}},r:12},2199:{},2200:{},2201:{},2202:{f:function(a){return[0!==a.read_shift(4),0!==a.read_shift(4),a.read_shift(4)]},r:12},2203:{f:de},2204:{},2205:{},2206:{},2207:{},2211:{f:function(a){var b,c,d=(b=a.read_shift(2),c=a.read_shift(2),a.l+=8,{type:b,flags:c});if(2211!=d.type)throw Error("Invalid Future Record "+d.type);return 0!==a.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:dg},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(a,b,c){var d={area:!1};if(5!=c.biff)return a.l+=b,d;var e=a.read_shift(1);return a.l+=3,16&e&&(d.area=!0),d}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(a){for(var b=a.read_shift(2),c=[];b-- >0;)c.push(dp(a,8));return c}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:dz},1:{},2:{f:function(a){var b=dq(a,6);++a.l;var c=a.read_shift(2);return b.t="n",b.val=c,b}},3:{f:function(a){var b=dq(a,6);++a.l;var c=cL(a,8);return b.t="n",b.val=c,b}},4:{f:function(a,b,c){c.biffguess&&5==c.biff&&(c.biff=2);var d=dq(a,6);++a.l;var e=dl(a,b-7,c);return d.t="str",d.val=e,d}},5:{f:dA},7:{f:function(a){var b=a.read_shift(1);return 0===b?(a.l++,""):a.read_shift(b,"sbcs-cont")}},8:{},9:{f:dx},11:{},22:{f:dg},30:{f:dl},31:{},32:{},33:{f:dG},36:{},37:{f:dy},50:{f:function(a,b){a.l+=6,a.l+=2,a.l+=1,a.l+=3,a.l+=1,a.l+=b-13}},62:{},52:{},67:{},68:{f:dg},69:{},86:{},126:{},127:{f:function(a){var b=a.read_shift(2),c=a.read_shift(2),d=a.read_shift(4),e={fmt:b,env:c,len:d,data:a.slice(a.l,a.l+d)};return a.l+=d,e}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(a,b,c){var d=a.l+b,e=dq(a,6),f=a.read_shift(2),g=dj(a,f,c);return a.l=d,e.t="str",e.val=g,e}},223:{},234:{},354:{},421:{},518:{f:eT},521:{f:dx},536:{f:dE},547:{f:dC},561:{},579:{},1030:{f:eT},1033:{f:dx},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function fD(a,b,c,d){if(!isNaN(b)){var e=d||(c||[]).length||0,f=a.next(4);f.write_shift(2,b),f.write_shift(2,e),e>0&&b6(c)&&a.push(c)}}function fE(a,b){var c=b||{},d=c.dense?[]:{},e=(a=a.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!e)throw Error("Invalid HTML: could not find <table>");var f=a.match(/<\/table/i),g=e.index,h=f&&f.index||a.length,i=a8(a.slice(g,h),/(:?<tr[^>]*>)/i,"<tr>"),j=-1,k=0,l=0,m=0,n={s:{r:1e7,c:1e7},e:{r:0,c:0}},o=[];for(g=0;g<i.length;++g){var p=i[g].trim(),q=p.slice(0,3).toLowerCase();if("<tr"==q){if(++j,c.sheetRows&&c.sheetRows<=j){--j;break}k=0;continue}if("<td"==q||"<th"==q){var r=p.split(/<\/t[dh]>/i);for(h=0;h<r.length;++h){var s=r[h].trim();if(s.match(/<t[dh]/i)){for(var t=s,u=0;"<"==t.charAt(0)&&(u=t.indexOf(">"))>-1;)t=t.slice(u+1);for(var v=0;v<o.length;++v){var w=o[v];w.s.c==k&&w.s.r<j&&j<=w.e.r&&(k=w.e.c+1,v=-1)}var x=bo(s.slice(0,s.indexOf(">")));m=x.colspan?+x.colspan:1,((l=+x.rowspan)>1||m>1)&&o.push({s:{r:j,c:k},e:{r:j+(l||1)-1,c:k+m-1}});var y=x.t||x["data-t"]||"";if(!t.length||(t=bF(t),n.s.r>j&&(n.s.r=j),n.e.r<j&&(n.e.r=j),n.s.c>k&&(n.s.c=k),n.e.c<k&&(n.e.c=k),!t.length)){k+=m;continue}var z={t:"s",v:t};c.raw||!t.trim().length||"s"==y||("TRUE"===t?z={t:"b",v:!0}:"FALSE"===t?z={t:"b",v:!1}:isNaN(a5(t))?isNaN(a7(t).getDate())||(z={t:"d",v:a1(t)},c.cellDates||(z={t:"n",v:aV(z.v)}),z.z=c.dateNF||al[14]):z={t:"n",v:a5(t)}),c.dense?(d[j]||(d[j]=[]),d[j][k]=z):d[cw({r:j,c:k})]=z,k+=m}}}}return d["!ref"]=cy(n),o.length&&(d["!merges"]=o),d}var fF={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function fG(a,b){var c=b||{},d,e,f,g,h,i,j=bK(a),k=[],l={name:""},m="",n=0,o={},p=[],q=c.dense?[]:{},r={value:""},s="",t=0,u=[],v=-1,w=-1,x={s:{r:1e6,c:1e7},e:{r:0,c:0}},y=0,z={},A=[],B={},C=0,D=[],E=1,F=1,G=[],H={Names:[]},I={},J=["",""],K=[],L={},M="",N=0,O=!1,P=!1,Q=0;for(bL.lastIndex=0,j=j.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");h=bL.exec(j);)switch(h[3]=h[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===h[1]?(x.e.c>=x.s.c&&x.e.r>=x.s.r?q["!ref"]=cy(x):q["!ref"]="A1:A1",c.sheetRows>0&&c.sheetRows<=x.e.r&&(q["!fullref"]=q["!ref"],x.e.r=c.sheetRows-1,q["!ref"]=cy(x)),A.length&&(q["!merges"]=A),D.length&&(q["!rows"]=D),f.name=f["名称"]||f.name,"undefined"!=typeof JSON&&JSON.stringify(f),p.push(f.name),o[f.name]=q,P=!1):"/"!==h[0].charAt(h[0].length-2)&&(f=bo(h[0],!1),v=w=-1,x.s.r=x.s.c=1e7,x.e.r=x.e.c=0,q=c.dense?[]:{},A=[],D=[],P=!0);break;case"table-row-group":"/"===h[1]?--y:++y;break;case"table-row":case"行":if("/"===h[1]){v+=E,E=1;break}if((g=bo(h[0],!1))["行号"]?v=g["行号"]-1:-1==v&&(v=0),(E=+g["number-rows-repeated"]||1)<10)for(Q=0;Q<E;++Q)y>0&&(D[v+Q]={level:y});w=-1;break;case"covered-table-cell":"/"!==h[1]&&++w,c.sheetStubs&&(c.dense?(q[v]||(q[v]=[]),q[v][w]={t:"z"}):q[cw({r:v,c:w})]={t:"z"}),s="",u=[];break;case"table-cell":case"数据":if("/"===h[0].charAt(h[0].length-2))++w,F=parseInt((r=bo(h[0],!1))["number-columns-repeated"]||"1",10),i={t:"z",v:null},r.formula&&!1!=c.cellFormula&&(i.f=eY(bs(r.formula))),"string"==(r["数据类型"]||r["value-type"])&&(i.t="s",i.v=bs(r["string-value"]||""),c.dense?(q[v]||(q[v]=[]),q[v][w]=i):q[cw({r:v,c:w})]=i),w+=F-1;else if("/"!==h[1]){s="",t=0,u=[],F=1;var R=E?v+E-1:v;if(++w>x.e.c&&(x.e.c=w),w<x.s.c&&(x.s.c=w),v<x.s.r&&(x.s.r=v),R>x.e.r&&(x.e.r=R),r=bo(h[0],!1),K=[],L={},i={t:r["数据类型"]||r["value-type"],v:null},c.cellFormula)if(r.formula&&(r.formula=bs(r.formula)),r["number-matrix-columns-spanned"]&&r["number-matrix-rows-spanned"]&&(B={s:{r:v,c:w},e:{r:v+(C=parseInt(r["number-matrix-rows-spanned"],10)||0)-1,c:w+(parseInt(r["number-matrix-columns-spanned"],10)||0)-1}},i.F=cy(B),G.push([B,i.F])),r.formula)i.f=eY(r.formula);else for(Q=0;Q<G.length;++Q)v>=G[Q][0].s.r&&v<=G[Q][0].e.r&&w>=G[Q][0].s.c&&w<=G[Q][0].e.c&&(i.F=G[Q][1]);switch((r["number-columns-spanned"]||r["number-rows-spanned"])&&(B={s:{r:v,c:w},e:{r:v+(C=parseInt(r["number-rows-spanned"],10)||0)-1,c:w+(parseInt(r["number-columns-spanned"],10)||0)-1}},A.push(B)),r["number-columns-repeated"]&&(F=parseInt(r["number-columns-repeated"],10)),i.t){case"boolean":i.t="b",i.v=bx(r["boolean-value"]);break;case"float":case"percentage":case"currency":i.t="n",i.v=parseFloat(r.value);break;case"date":i.t="d",i.v=a1(r["date-value"]),c.cellDates||(i.t="n",i.v=aV(i.v)),i.z="m/d/yy";break;case"time":i.t="n",i.v=function(a){var b=0,c=0,d=!1,e=a.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!e)throw Error("|"+a+"| is not an ISO8601 Duration");for(var f=1;f!=e.length;++f)if(e[f]){switch(c=1,f>3&&(d=!0),e[f].slice(e[f].length-1)){case"Y":throw Error("Unsupported ISO Duration Field: "+e[f].slice(e[f].length-1));case"D":c*=24;case"H":c*=60;case"M":if(d)c*=60;else throw Error("Unsupported ISO Duration Field: M")}b+=c*parseInt(e[f],10)}return b}(r["time-value"])/86400,c.cellDates&&(i.t="d",i.v=aZ(i.v)),i.z="HH:MM:SS";break;case"number":i.t="n",i.v=parseFloat(r["数据数值"]);break;default:if("string"!==i.t&&"text"!==i.t&&i.t)throw Error("Unsupported value type "+i.t);i.t="s",null!=r["string-value"]&&(s=bs(r["string-value"]),u=[])}}else{if(O=!1,"s"===i.t&&(i.v=s||"",u.length&&(i.R=u),O=0==t),I.Target&&(i.l=I),K.length>0&&(i.c=K,K=[]),s&&!1!==c.cellText&&(i.w=s),O&&(i.t="z",delete i.v),(!O||c.sheetStubs)&&!(c.sheetRows&&c.sheetRows<=v))for(var S=0;S<E;++S){if(F=parseInt(r["number-columns-repeated"]||"1",10),c.dense)for(q[v+S]||(q[v+S]=[]),q[v+S][w]=0==S?i:a3(i);--F>0;)q[v+S][w+F]=a3(i);else for(q[cw({r:v+S,c:w})]=i;--F>0;)q[cw({r:v+S,c:w+F})]=a3(i);x.e.c<=w&&(x.e.c=w)}w+=(F=parseInt(r["number-columns-repeated"]||"1",10))-1,F=0,i={},s="",u=[]}I={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===h[1]){if((d=k.pop())[0]!==h[3])throw"Bad state: "+d}else"/"!==h[0].charAt(h[0].length-2)&&k.push([h[3],!0]);break;case"annotation":if("/"===h[1]){if((d=k.pop())[0]!==h[3])throw"Bad state: "+d;L.t=s,u.length&&(L.R=u),L.a=M,K.push(L)}else"/"!==h[0].charAt(h[0].length-2)&&k.push([h[3],!1]);M="",N=0,s="",t=0,u=[];break;case"creator":"/"===h[1]?M=j.slice(N,h.index):N=h.index+h[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===h[1]){if((d=k.pop())[0]!==h[3])throw"Bad state: "+d}else"/"!==h[0].charAt(h[0].length-2)&&k.push([h[3],!1]);s="",t=0,u=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===h[1]){if(z[l.name]=m,(d=k.pop())[0]!==h[3])throw"Bad state: "+d}else"/"!==h[0].charAt(h[0].length-2)&&(m="",l=bo(h[0],!1),k.push([h[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(k[k.length-1][0]){case"time-style":case"date-style":e=bo(h[0],!1),m+=fF[h[3]][+("long"===e.style)]}break;case"text":if("/>"===h[0].slice(-2));else if("/"===h[1])switch(k[k.length-1][0]){case"number-style":case"date-style":case"time-style":m+=j.slice(n,h.index)}else n=h.index+h[0].length;break;case"named-range":J=eZ((e=bo(h[0],!1))["cell-range-address"]);var T={Name:e.name,Ref:J[0]+"!"+J[1]};P&&(T.Sheet=p.length),H.Names.push(T);break;case"p":case"文本串":if(["master-styles"].indexOf(k[k.length-1][0])>-1)break;if("/"!==h[1]||r&&r["string-value"])bo(h[0],!1),t=h.index+h[0].length;else{var U=[bs(j.slice(t,h.index).replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(a,b){return Array(parseInt(b,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,"\n").replace(/<[^>]*>/g,""))];s=(s.length>0?s+"\n":"")+U[0]}break;case"database-range":if("/"===h[1])break;try{o[(J=eZ(bo(h[0])["target-range-address"]))[0]]["!autofilter"]={ref:J[1]}}catch(a){}break;case"a":if("/"!==h[1]){if(!(I=bo(h[0],!1)).href)break;I.Target=bs(I.href),delete I.href,"#"==I.Target.charAt(0)&&I.Target.indexOf(".")>-1?(J=eZ(I.Target.slice(1)),I.Target="#"+J[0]+"!"+J[1]):I.Target.match(/^\.\.[\\\/]/)&&(I.Target=I.Target.slice(3))}break;default:switch(h[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(c.WTF)throw Error(h)}}var V={Sheets:o,SheetNames:p,Workbook:H};return c.bookSheets&&delete V.Sheets,V}function fH(a){return new DataView(a.buffer,a.byteOffset,a.byteLength)}function fI(a){return"undefined"!=typeof TextDecoder?new TextDecoder().decode(a):bC(Z(a))}function fJ(a){var b=new Uint8Array(a.reduce(function(a,b){return a+b.length},0)),c=0;return a.forEach(function(a){b.set(a,c),c+=a.length}),b}function fK(a){return a-=a>>1&0x55555555,((a=(0x33333333&a)+(a>>2&0x33333333))+(a>>4)&0xf0f0f0f)*0x1010101>>>24}function fL(a,b){var c=b?b[0]:0,d=127&a[c];c:if(a[c++]>=128&&(d|=(127&a[c])<<7,a[c++]<128||(d|=(127&a[c])<<14,a[c++]<128)||(d|=(127&a[c])<<21,a[c++]<128)||(d+=(127&a[c])*0x10000000,++c,a[c++]<128)||(d+=(127&a[c])*0x800000000,++c,a[c++]<128)||(d+=(127&a[c])*0x40000000000,++c,a[c++]<128)))break c;return b&&(b[0]=c),d}function fM(a){var b=0,c=127&a[0];c:if(a[b++]>=128){if(c|=(127&a[b])<<7,a[b++]<128||(c|=(127&a[b])<<14,a[b++]<128)||(c|=(127&a[b])<<21,a[b++]<128))break c;c|=(127&a[b])<<28}return c}function fN(a){for(var b=[],c=[0];c[0]<a.length;){var d,e=c[0],f=fL(a,c),g=7&f,h=0;if(0==(f=Math.floor(f/8)))break;switch(g){case 0:for(var i=c[0];a[c[0]++]>=128;);d=a.slice(i,c[0]);break;case 5:h=4,d=a.slice(c[0],c[0]+h),c[0]+=h;break;case 1:h=8,d=a.slice(c[0],c[0]+h),c[0]+=h;break;case 2:h=fL(a,c),d=a.slice(c[0],c[0]+h),c[0]+=h;break;default:throw Error("PB Type ".concat(g," for Field ").concat(f," at offset ").concat(e))}var j={data:d,type:g};null==b[f]?b[f]=[j]:b[f].push(j)}return b}function fO(a,b){return(null==a?void 0:a.map(function(a){return b(a.data)}))||[]}function fP(a){return fL(fN(a)[1][0].data)}function fQ(a,b){var c=fN(b.data),d=fM(c[1][0].data),e=c[3],f=[];return(e||[]).forEach(function(b){var c=fN(b.data),e=fM(c[1][0].data)>>>0;switch(d){case 1:f[e]=fI(c[3][0].data);break;case 8:var g=fN(a[fP(c[9][0].data)][0].data),h=a[fP(g[1][0].data)][0],i=fM(h.meta[1][0].data);if(2001!=i)throw Error("2000 unexpected reference to ".concat(i));var j=fN(h.data);f[e]=j[3].map(function(a){return fI(a.data)}).join("")}}),f}function fR(a){var b,c,d,e,f={},g=[];if(a.FullPaths.forEach(function(a){if(a.match(/\.iwpv2/))throw Error("Unsupported password protection")}),a.FileIndex.forEach(function(a){var b,c;if(a.name.match(/\.iwa$/)){try{b=function(a){for(var b=[],c=0;c<a.length;){var d=a[c++],e=a[c]|a[c+1]<<8|a[c+2]<<16;c+=3,b.push(function(a,b){if(0!=a)throw Error("Unexpected Snappy chunk type ".concat(a));for(var c=[0],d=fL(b,c),e=[];c[0]<b.length;){var f=3&b[c[0]];if(0==f){var g=b[c[0]++]>>2;if(g<60)++g;else{var h=g-59;g=b[c[0]],h>1&&(g|=b[c[0]+1]<<8),h>2&&(g|=b[c[0]+2]<<16),h>3&&(g|=b[c[0]+3]<<24),g>>>=0,g++,c[0]+=h}e.push(b.slice(c[0],c[0]+g)),c[0]+=g;continue}var i=0,j=0;if(1==f?(j=(b[c[0]]>>2&7)+4,i=(224&b[c[0]++])<<3|b[c[0]++]):(j=(b[c[0]++]>>2)+1,2==f?(i=b[c[0]]|b[c[0]+1]<<8,c[0]+=2):(i=(b[c[0]]|b[c[0]+1]<<8|b[c[0]+2]<<16|b[c[0]+3]<<24)>>>0,c[0]+=4)),e=[fJ(e)],0==i)throw Error("Invalid offset 0");if(i>e[0].length)throw Error("Invalid offset beyond length");if(j>=i)for(e.push(e[0].slice(-i)),j-=i;j>=e[e.length-1].length;)e.push(e[e.length-1]),j-=e[e.length-1].length;e.push(e[0].slice(-i,-i+j))}var k=fJ(e);if(k.length!=d)throw Error("Unexpected length: ".concat(k.length," != ").concat(d));return k}(d,a.slice(c,c+e))),c+=e}if(c!==a.length)throw Error("data is not a valid framed stream!");return fJ(b)}(a.content)}catch(b){return console.log("?? "+a.content.length+" "+(b.message||b))}try{c=function(a){for(var b,c=[],d=[0];d[0]<a.length;){var e=fL(a,d),f=fN(a.slice(d[0],d[0]+e));d[0]+=e;var g={id:fM(f[1][0].data),messages:[]};f[2].forEach(function(b){var c=fN(b.data),e=fM(c[3][0].data);g.messages.push({meta:c,data:a.slice(d[0],d[0]+e)}),d[0]+=e}),(null==(b=f[3])?void 0:b[0])&&(g.merge=fM(f[3][0].data)>>>0>0),c.push(g)}return c}(b)}catch(a){return console.log("## "+(a.message||a))}c.forEach(function(a){f[a.id]=a.messages,g.push(a.id)})}}),!g.length)throw Error("File has no messages");var h=(null==(e=null==(d=null==(c=null==(b=null==f?void 0:f[1])?void 0:b[0])?void 0:c.meta)?void 0:d[1])?void 0:e[0].data)&&1==fM(f[1][0].meta[1][0].data)&&f[1][0];if(h||g.forEach(function(a){f[a].forEach(function(a){if(1==fM(a.meta[1][0].data)>>>0)if(h)throw Error("Document has multiple roots");else h=a})}),!h)throw Error("Cannot find Document root");var i=h,j=fY();if(fO(fN(i.data)[1],fP).forEach(function(a){f[a].forEach(function(a){if(2==fM(a.meta[1][0].data)){var b,c,d,e=(d={name:(null==(b=(c=fN(a.data))[1])?void 0:b[0])?fI(c[1][0].data):"",sheets:[]},fO(c[2],fP).forEach(function(a){f[a].forEach(function(a){6e3==fM(a.meta[1][0].data)&&d.sheets.push(function(a,b){var c=fN(b.data),d={"!ref":"A1"},e=a[fP(c[2][0].data)],f=fM(e[0].meta[1][0].data);if(6001!=f)throw Error("6000 unexpected reference to ".concat(f));return!function(a,b,c){var d,e=fN(b.data),f={s:{r:0,c:0},e:{r:0,c:0}};if(f.e.r=(fM(e[6][0].data)>>>0)-1,f.e.r<0)throw Error("Invalid row varint ".concat(e[6][0].data));if(f.e.c=(fM(e[7][0].data)>>>0)-1,f.e.c<0)throw Error("Invalid col varint ".concat(e[7][0].data));c["!ref"]=cy(f);var g=fN(e[4][0].data),h=fQ(a,a[fP(g[4][0].data)][0]),i=(null==(d=g[17])?void 0:d[0])?fQ(a,a[fP(g[17][0].data)][0]):[],j=fN(g[3][0].data),k=0;j[1].forEach(function(b){var d,e,f,g,j=a[fP(fN(b.data)[2][0].data)][0],l=fM(j.meta[1][0].data);if(6002!=l)throw Error("6001 unexpected reference to ".concat(l));var m=(f=(null==(d=null==(e=fN(j.data))?void 0:e[7])?void 0:d[0])?+(fM(e[7][0].data)>>>0>0):-1,g=fO(e[5],function(a){return function(a,b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s=fN(a),t=fM(s[1][0].data)>>>0,u=fM(s[2][0].data)>>>0,v=(null==(d=null==(c=s[8])?void 0:c[0])?void 0:d.data)&&fM(s[8][0].data)>0||!1;if((null==(f=null==(e=s[7])?void 0:e[0])?void 0:f.data)&&0!=b)q=null==(h=null==(g=s[7])?void 0:g[0])?void 0:h.data,r=null==(j=null==(i=s[6])?void 0:i[0])?void 0:j.data;else if((null==(l=null==(k=s[4])?void 0:k[0])?void 0:l.data)&&1!=b)q=null==(n=null==(m=s[4])?void 0:m[0])?void 0:n.data,r=null==(p=null==(o=s[3])?void 0:o[0])?void 0:p.data;else throw"NUMBERS Tile missing ".concat(b," cell storage");for(var w=v?4:1,x=fH(q),y=[],z=0;z<q.length/2;++z){var A=x.getUint16(2*z,!0);A<65535&&y.push([z,A])}if(y.length!=u)throw"Expected ".concat(u," cells, found ").concat(y.length);var B=[];for(z=0;z<y.length-1;++z)B[y[z][0]]=r.subarray(y[z][1]*w,y[z+1][1]*w);return y.length>=1&&(B[y[y.length-1][0]]=r.subarray(y[y.length-1][1]*w)),{R:t,cells:B}}(a,f)}),{nrows:fM(e[4][0].data)>>>0,data:g.reduce(function(a,b){return a[b.R]||(a[b.R]=[]),b.cells.forEach(function(c,d){if(a[b.R][d])throw Error("Duplicate cell r=".concat(b.R," c=").concat(d));a[b.R][d]=c}),a},[])});m.data.forEach(function(a,b){a.forEach(function(a,d){var e=cw({r:k+b,c:d}),f=function(a,b,c){switch(a[0]){case 0:case 1:case 2:case 3:return function(a,b,c,d){var e,f=fH(a),g=f.getUint32(4,!0),h=(d>1?12:8)+4*fK(g&(d>1?3470:398)),i=-1,j=-1,k=NaN,l=new Date(2001,0,1);switch(512&g&&(i=f.getUint32(h,!0),h+=4),h+=4*fK(g&(d>1?12288:4096)),16&g&&(j=f.getUint32(h,!0),h+=4),32&g&&(k=f.getFloat64(h,!0),h+=8),64&g&&(l.setTime(l.getTime()+1e3*f.getFloat64(h,!0)),h+=8),a[2]){case 0:break;case 2:e={t:"n",v:k};break;case 3:e={t:"s",v:b[j]};break;case 5:e={t:"d",v:l};break;case 6:e={t:"b",v:k>0};break;case 7:e={t:"n",v:k/86400};break;case 8:e={t:"e",v:0};break;case 9:if(i>-1)e={t:"s",v:c[i]};else if(j>-1)e={t:"s",v:b[j]};else if(isNaN(k))throw Error("Unsupported cell type ".concat(a.slice(0,4)));else e={t:"n",v:k};break;default:throw Error("Unsupported cell type ".concat(a.slice(0,4)))}return e}(a,b,c,a[0]);case 5:return function(a,b,c){var d,e=fH(a),f=e.getUint32(8,!0),g=12,h=-1,i=-1,j=NaN,k=NaN,l=new Date(2001,0,1);switch(1&f&&(j=function(a,b){for(var c=(127&a[b+15])<<7|a[b+14]>>1,d=1&a[b+14],e=b+13;e>=b;--e)d=256*d+a[e];return(128&a[b+15]?-d:d)*Math.pow(10,c-6176)}(a,g),g+=16),2&f&&(k=e.getFloat64(g,!0),g+=8),4&f&&(l.setTime(l.getTime()+1e3*e.getFloat64(g,!0)),g+=8),8&f&&(i=e.getUint32(g,!0),g+=4),16&f&&(h=e.getUint32(g,!0),g+=4),a[1]){case 0:break;case 2:case 10:d={t:"n",v:j};break;case 3:d={t:"s",v:b[i]};break;case 5:d={t:"d",v:l};break;case 6:d={t:"b",v:k>0};break;case 7:d={t:"n",v:k/86400};break;case 8:d={t:"e",v:0};break;case 9:if(h>-1)d={t:"s",v:c[h]};else throw Error("Unsupported cell type ".concat(a[1]," : ").concat(31&f," : ").concat(a.slice(0,4)));break;default:throw Error("Unsupported cell type ".concat(a[1]," : ").concat(31&f," : ").concat(a.slice(0,4)))}return d}(a,b,c);default:throw Error("Unsupported payload version ".concat(a[0]))}}(a,h,i);f&&(c[e]=f)})}),k+=m.nrows})}(a,e[0],d),d}(f,a))})}),d);e.sheets.forEach(function(a,b){fZ(j,a,0==b?e.name:e.name+"_"+b,!0)})}})}),0==j.SheetNames.length)throw Error("Empty NUMBERS file");return j}function fS(a){var b;(b=[["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]],function(a){for(var c=0;c!=b.length;++c){var d=b[c];void 0===a[d[0]]&&(a[d[0]]=d[1]),"n"===d[2]&&(a[d[0]]=Number(a[d[0]]))}})(a)}function fT(a){return"/"==a.charAt(0)?a.slice(1):a}function fU(a,b){var c="";switch((b||{}).type||"base64"){case"buffer":case"array":return[a[0],a[1],a[2],a[3],a[4],a[5],a[6],a[7]];case"base64":c=T(a.slice(0,12));break;case"binary":c=a;break;default:throw Error("Unrecognized type "+(b&&b.type||"undefined"))}return[c.charCodeAt(0),c.charCodeAt(1),c.charCodeAt(2),c.charCodeAt(3),c.charCodeAt(4),c.charCodeAt(5),c.charCodeAt(6),c.charCodeAt(7)]}function fV(a,b){var c=0;d:for(;c<a.length;)switch(a.charCodeAt(c)){case 10:case 13:case 32:++c;break;case 60:return fv(a.slice(c),b);default:break d}return dO.to_workbook(a,b)}function fW(a,b,c,d){return d?(c.type="string",dO.to_workbook(a,c)):dO.to_workbook(b,c)}function fX(a,b){if(null==a||null==a["!ref"])return[];var c={t:"n",v:0},d=0,e=1,f=[],g=0,h="",i={s:{r:0,c:0},e:{r:0,c:0}},j=b||{},k=null!=j.range?j.range:a["!ref"];switch(1===j.header?d=1:"A"===j.header?d=2:Array.isArray(j.header)?d=3:null==j.header&&(d=0),typeof k){case"string":i=cz(k);break;case"number":(i=cz(a["!ref"])).s.r=k;break;default:i=k}d>0&&(e=0);var l=cs(i.s.r),m=[],n=[],o=0,p=0,q=Array.isArray(a),r=i.s.r,s=0,t={};q&&!a[r]&&(a[r]=[]);var u=j.skipHidden&&a["!cols"]||[],v=j.skipHidden&&a["!rows"]||[];for(s=i.s.c;s<=i.e.c;++s)if(!(u[s]||{}).hidden)switch(m[s]=cu(s),c=q?a[r][s]:a[m[s]+l],d){case 1:f[s]=s-i.s.c;break;case 2:f[s]=m[s];break;case 3:f[s]=j.header[s-i.s.c];break;default:if(null==c&&(c={w:"__EMPTY",t:"s"}),h=g=cB(c,null,j),p=t[g]||0){do h=g+"_"+p++;while(t[h]);t[g]=p,t[h]=1}else t[g]=1;f[s]=h}for(r=i.s.r+e;r<=i.e.r;++r)if(!(v[r]||{}).hidden){var w=function(a,b,c,d,e,f,g,h){var i=cs(c),j=h.defval,k=h.raw||!Object.prototype.hasOwnProperty.call(h,"raw"),l=!0,m=1===e?[]:{};if(1!==e)if(Object.defineProperty)try{Object.defineProperty(m,"__rowNum__",{value:c,enumerable:!1})}catch(a){m.__rowNum__=c}else m.__rowNum__=c;if(!g||a[c])for(var n=b.s.c;n<=b.e.c;++n){var o=g?a[c][n]:a[d[n]+i];if(void 0===o||void 0===o.t){if(void 0===j)continue;null!=f[n]&&(m[f[n]]=j);continue}var p=o.v;switch(o.t){case"z":if(null==p)break;continue;case"e":p=0==p?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw Error("unrecognized type "+o.t)}if(null!=f[n]){if(null==p)if("e"==o.t&&null===p)m[f[n]]=null;else if(void 0!==j)m[f[n]]=j;else{if(!k||null!==p)continue;m[f[n]]=null}else m[f[n]]=k&&("n"!==o.t||"n"===o.t&&!1!==h.rawNumbers)?p:cB(o,p,h);null!=p&&(l=!1)}}return{row:m,isempty:l}}(a,i,r,m,d,f,q,j);(!1===w.isempty||(1===d?!1!==j.blankrows:j.blankrows))&&(n[o++]=w.row)}return n.length=o,n}function fY(){return{SheetNames:[],Sheets:{}}}function fZ(a,b,c,d){var e=1;if(!c)for(;e<=65535&&-1!=a.SheetNames.indexOf(c="Sheet"+e);++e,c=void 0);if(!c||a.SheetNames.length>=65535)throw Error("Too many worksheets");if(d&&a.SheetNames.indexOf(c)>=0){var f=c.match(/(^.*?)(\d+)$/);e=f&&+f[2]||0;var g=f&&f[1]||c;for(++e;e<=65535&&-1!=a.SheetNames.indexOf(c=g+e);++e);}if(!function(a,b){if(a.length>31)throw Error("Sheet names cannot exceed 31 chars");fo.forEach(function(b){if(-1!=a.indexOf(b)&&!0)throw Error("Sheet name cannot contain : \\ / ? * [ ]")})}(c),a.SheetNames.indexOf(c)>=0)throw Error("Worksheet with name |"+c+"| already exists!");return a.SheetNames.push(c),a.Sheets[c]=b,c}var f$={sheet_to_json:fX};async function f_(a){try{let b=(0,A.rL)(a);if(!b)return y.NextResponse.json({error:"Unauthorized"},{status:401});let c=await a.formData(),f=c.get("file"),g="true"===c.get("skipDuplicates");if(!f)return y.NextResponse.json({error:"No file provided"},{status:400});let h=f.name.split(".").pop()?.toLowerCase();if(!["csv","xlsx","xls"].includes(h||""))return y.NextResponse.json({error:"Invalid file type. Please upload CSV or Excel files."},{status:400});let i=await f.arrayBuffer(),j=[];if("csv"===h){let a=new TextDecoder().decode(i);j=D().parse(a,{header:!0,skipEmptyLines:!0,transformHeader:a=>a.toLowerCase().trim()}).data}else{let a=function a(b,c){L();var f,g,h,i,j,k,l,m=c||{};if("undefined"!=typeof ArrayBuffer&&b instanceof ArrayBuffer)return a(new Uint8Array(b),((m=a3(m)).type="array",m));"undefined"!=typeof Uint8Array&&b instanceof Uint8Array&&!m.type&&(m.type="undefined"!=typeof Deno?"buffer":"array");var n=b,o=[0,0,0,0],p=!1;if(m.cellStyles&&(m.cellNF=!0,m.sheetStubs=!0),e_={},m.dateNF&&(e_.dateNF=m.dateNF),m.type||(m.type=U&&Buffer.isBuffer(b)?"buffer":"base64"),"file"==m.type&&(m.type=U?"buffer":"binary",n=function(a){if(void 0!==d)return d.readFileSync(a);if("undefined"!=typeof Deno)return Deno.readFileSync(a);if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var b=File(a);b.open("r"),b.encoding="binary";var c=b.read();return b.close(),c}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw Error("Cannot access file "+a)}(b),"undefined"==typeof Uint8Array||U||(m.type="array")),"string"==m.type&&(p=!0,m.type="binary",m.codepage=65001,n=b.match(/[^\x00-\x7F]/)?bD(b):b),"array"==m.type&&"undefined"!=typeof Uint8Array&&b instanceof Uint8Array&&"undefined"!=typeof ArrayBuffer){var q=new Uint8Array(new ArrayBuffer(3));if(q.foo="bar",!q.foo)return(m=a3(m)).type="array",a(_(n),m)}switch((o=fU(n,m))[0]){case 208:if(207===o[1]&&17===o[2]&&224===o[3]&&161===o[4]&&177===o[5]&&26===o[6]&&225===o[7])return h=aR.read(n,m),i=m,aR.find(h,"EncryptedPackage")?function(a,b){var c=b||{},d="Workbook",e=aR.find(a,d);try{if(d="/!DataSpaces/Version",!(e=aR.find(a,d))||!e.content||(f=e.content,(g={}).id=f.read_shift(0,"lpp4"),g.R=d$(f,4),g.U=d$(f,4),g.W=d$(f,4),d="/!DataSpaces/DataSpaceMap",!(e=aR.find(a,d))||!e.content))throw Error("ECMA-376 Encrypted file missing "+d);var f,g,h=function(a){var b=[];a.l+=4;for(var c=a.read_shift(4);c-- >0;)b.push(function(a){for(var b=a.read_shift(4),c=a.l+b-4,d={},e=a.read_shift(4),f=[];e-- >0;)f.push({t:a.read_shift(4),v:a.read_shift(0,"lpp4")});if(d.name=a.read_shift(0,"lpp4"),d.comps=f,a.l!=c)throw Error("Bad DataSpaceMapEntry: "+a.l+" != "+c);return d}(a));return b}(e.content);if(1!==h.length||1!==h[0].comps.length||0!==h[0].comps[0].t||"StrongEncryptionDataSpace"!==h[0].name||"EncryptedPackage"!==h[0].comps[0].v)throw Error("ECMA-376 Encrypted file bad "+d);if(d="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",!(e=aR.find(a,d))||!e.content)throw Error("ECMA-376 Encrypted file missing "+d);var i=function(a){var b=[];a.l+=4;for(var c=a.read_shift(4);c-- >0;)b.push(a.read_shift(0,"lpp4"));return b}(e.content);if(1!=i.length||"StrongEncryptionTransform"!=i[0])throw Error("ECMA-376 Encrypted file bad "+d);if(d="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",!(e=aR.find(a,d))||!e.content)throw Error("ECMA-376 Encrypted file missing "+d);!function(a){var b,c=(b={},a.read_shift(4),a.l+=4,b.id=a.read_shift(0,"lpp4"),b.name=a.read_shift(0,"lpp4"),b.R=d$(a,4),b.U=d$(a,4),b.W=d$(a,4),b);if(c.ename=a.read_shift(0,"8lpp4"),c.blksz=a.read_shift(4),c.cmode=a.read_shift(4),4!=a.read_shift(4))throw Error("Bad !Primary record")}(e.content)}catch(a){}if(d="/EncryptionInfo",!(e=aR.find(a,d))||!e.content)throw Error("ECMA-376 Encrypted file missing "+d);var j=function(a){var b,c,d,e,f=d$(a);switch(f.Minor){case 2:return[f.Minor,function(a){if((63&a.read_shift(4))!=36)throw Error("EncryptionInfo mismatch");var b=a.read_shift(4);return{t:"Std",h:d_(a,b),v:d0(a,a.length-a.l)}}(a,f)];case 3:return[f.Minor,function(){throw Error("File is password-protected: ECMA-376 Extensible")}(a,f)];case 4:return[f.Minor,(b=a,c=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"],b.l+=4,d=b.read_shift(b.length-b.l,"utf8"),e={},d.replace(bl,function(a){var b=bo(a);switch(bp(b[0])){case"<?xml":case"<encryption":case"</encryption>":case"</keyEncryptors>":case"</keyEncryptor>":break;case"<keyData":c.forEach(function(a){e[a]=b[a]});break;case"<dataIntegrity":e.encryptedHmacKey=b.encryptedHmacKey,e.encryptedHmacValue=b.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":e.encs=[];break;case"<keyEncryptor":e.uri=b.uri;break;case"<encryptedKey":e.encs.push(b);break;default:throw b[0]}}),e)]}throw Error("ECMA-376 Encrypted file unrecognized Version: "+f.Minor)}(e.content);if(d="/EncryptedPackage",!(e=aR.find(a,d))||!e.content)throw Error("ECMA-376 Encrypted file missing "+d);if(4==j[0]&&"undefined"!=typeof decrypt_agile)return decrypt_agile(j[1],e.content,c.password||"",c);if(2==j[0]&&"undefined"!=typeof decrypt_std76)return decrypt_std76(j[1],e.content,c.password||"",c);throw Error("File is password-protected")}(h,i):fA(h,i);break;case 9:if(o[1]<=8)return fA(n,m);break;case 60:return fv(n,m);case 73:if(73===o[1]&&42===o[2]&&0===o[3])throw Error("TIFF Image File is not a spreadsheet");if(68===o[1]){var r=n,s=m,t=s||{},u=!!t.WTF;t.WTF=!0;try{var v=dL.to_workbook(r,t);return t.WTF=u,v}catch(a){if(t.WTF=u,!a.message.match(/SYLK bad record ID/)&&u)throw a;return dO.to_workbook(r,s)}}break;case 84:if(65===o[1]&&66===o[2]&&76===o[3])return dM.to_workbook(n,m);break;case 80:return 75===o[1]&&o[2]<9&&o[3]<9?(f=n,(g=m||{}).type||(g.type=U&&Buffer.isBuffer(f)?"buffer":"base64"),function(a,b){if(aN(),fS(b=b||{}),bb(a,"META-INF/manifest.xml")||bb(a,"objectdata.xml")){var c=a,d=b;d=d||{},bb(c,"META-INF/manifest.xml")&&function(a,b){for(var c,d,e=bK(a);c=bL.exec(e);)switch(c[3]){case"manifest":break;case"file-entry":if("/"==(d=bo(c[0],!1)).path&&"application/vnd.oasis.opendocument.spreadsheet"!==d.type)throw Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw Error("Unsupported ODS Encryption");default:if(b&&b.WTF)throw c}}(bd(c,"META-INF/manifest.xml"),d);var e=be(c,"content.xml");if(!e)throw Error("Missing content.xml in ODS / UOF file");var f=fG(bC(e),d);return bb(c,"meta.xml")&&(f.Props=c_(bd(c,"meta.xml"))),f}if(bb(a,"Index/Document.iwa")){if("undefined"==typeof Uint8Array)throw Error("NUMBERS file parsing requires Uint8Array support");if(a.FileIndex)return fR(a);var g,h,i,j,k,l,m,n,o,p,q,r,s,t=aR.utils.cfb_new();return bf(a).forEach(function(b){!function(a,b,c){if(a.FullPaths){if("string"==typeof c){var d;return d=U?V(c):function(a){for(var b=[],c=0,d=a.length+250,e=W(a.length+255),f=0;f<a.length;++f){var g=a.charCodeAt(f);if(g<128)e[c++]=g;else if(g<2048)e[c++]=192|g>>6&31,e[c++]=128|63&g;else if(g>=55296&&g<57344){g=(1023&g)+64;var h=1023&a.charCodeAt(++f);e[c++]=240|g>>8&7,e[c++]=128|g>>2&63,e[c++]=128|h>>6&15|(3&g)<<4,e[c++]=128|63&h}else e[c++]=224|g>>12&15,e[c++]=128|g>>6&63,e[c++]=128|63&g;c>d&&(b.push(e.slice(0,c)),c=0,e=W(65535),d=65530)}return b.push(e.slice(0,c)),aa(b)}(c),aR.utils.cfb_add(a,b,d)}aR.utils.cfb_add(a,b,c)}else a.file(b,c)}(t,b,function a(b,c,d){if(!d)return ba(bc(b,c));if(!c)return null;try{return a(b,c)}catch(a){return null}}(a,b))}),fR(t)}if(!bb(a,"[Content_Types].xml")){if(bb(a,"index.xml.gz"))throw Error("Unsupported NUMBERS 08 file");if(bb(a,"index.xml"))throw Error("Unsupported NUMBERS 09 file");throw Error("Unsupported ZIP file")}var u=bf(a),v=function(a){var b={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};if(!a||!a.match)return b;var c={};if((a.match(bl)||[]).forEach(function(a){var d=bo(a);switch(d[0].replace(bm,"<")){case"<?xml":break;case"<Types":b.xmlns=d["xmlns"+(d[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":c[d.Extension]=d.ContentType;break;case"<Override":void 0!==b[cV[d.ContentType]]&&b[cV[d.ContentType]].push(d.PartName)}}),b.xmlns!==bM.CT)throw Error("Unknown Namespace: "+b.xmlns);return b.calcchain=b.calcchains.length>0?b.calcchains[0]:"",b.sst=b.strs.length>0?b.strs[0]:"",b.style=b.styles.length>0?b.styles[0]:"",b.defaults=c,delete b.calcchains,b}(be(a,"[Content_Types].xml")),w=!1;if(0===v.workbooks.length&&bd(a,q="xl/workbook.xml",!0)&&v.workbooks.push(q),0===v.workbooks.length){if(!bd(a,q="xl/workbook.bin",!0))throw Error("Could not find workbook");v.workbooks.push(q),w=!0}"bin"==v.workbooks[0].slice(-3)&&(w=!0);var x={},y={};if(!b.bookSheets&&!b.bookProps){if(e$=[],v.sst)try{e$=function(a,b,c){if(".bin"===b.slice(-4)){var d,e;return d=[],e=!1,cl(a,function(a,b,f){switch(f){case 159:d.Count=a[0],d.Unique=a[1];break;case 19:d.push(a);break;case 160:return!0;case 35:e=!0;break;case 36:e=!1;break;default:if(b.T,!e||c.WTF)throw Error("Unexpected record 0x"+f.toString(16))}}),d}return function(a,b){var c=[],d="";if(!a)return c;var e=a.match(dW);if(e){d=e[2].replace(dX,"").split(dY);for(var f=0;f!=d.length;++f){var g=dV(d[f].trim(),b);null!=g&&(c[c.length]=g)}c.Count=(e=bo(e[1])).count,c.Unique=e.uniqueCount}return c}(a,c)}(bd(a,fT(v.sst)),v.sst,b)}catch(a){if(b.WTF)throw a}b.cellStyles&&v.themes.length&&(g=be(a,v.themes[0].replace(/^\//,""),!0)||"",v.themes[0],x=er(g,b)),v.style&&(y=function(a,b,c,d){if(".bin"===b.slice(-4)){var e={};for(var f in e.NumberFmt=[],al)e.NumberFmt[f]=al[f];e.CellXf=[],e.Fonts=[];var g=[],h=!1;return cl(a,function(a,b,f){switch(f){case 44:e.NumberFmt[a[0]]=a[1],aM(a[1],a[0]);break;case 43:e.Fonts.push(a),null!=a.color.theme&&c&&c.themeElements&&c.themeElements.clrScheme&&(a.color.rgb=d6(c.themeElements.clrScheme[a.color.theme].rgb,a.color.tint||0));break;case 1025:case 45:case 46:case 48:case 507:case 572:case 475:case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 47:617==g[g.length-1]&&e.CellXf.push(a);break;case 35:h=!0;break;case 36:h=!1;break;case 37:g.push(f),h=!0;break;case 38:g.pop(),h=!1;break;default:if(b.T>0)g.push(f);else if(b.T<0)g.pop();else if(!h||d.WTF&&37!=g[g.length-1])throw Error("Unexpected record 0x"+f.toString(16))}}),e}return ei(a,c,d)}(bd(a,fT(v.style)),v.style,x,b))}v.links.map(function(c){try{cY(be(a,cX(fT(c))),c);var d=bd(a,fT(c)),e=c,f=b;if(".bin"===e.slice(-4)){if(!d)return d;var g=f||{},h=!1;return void cl(d,function(a,b,c){switch(c){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:h=!0;break;case 36:h=!1;break;default:if(b.T);else if(!h||g.WTF)throw Error("Unexpected record 0x"+c.toString(16))}},g)}return}catch(a){}});var z=function(a,b,c){if(".bin"===b.slice(-4)){var d,e,f,g,h,i;return e={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},f=[],g=!1,(d=c)||(d={}),d.biff=12,h=[],(i=[[]]).SheetNames=[],i.XTI=[],fB[16]={n:"BrtFRTArchID$",f:fq},cl(a,function(a,b,c){switch(c){case 156:i.SheetNames.push(a.name),e.Sheets.push(a);break;case 153:e.WBProps=a;break;case 39:null!=a.Sheet&&(d.SID=a.Sheet),a.Ref=eS(a.Ptg,null,null,i,d),delete d.SID,delete a.Ptg,h.push(a);break;case 1036:case 361:case 2071:case 158:case 143:case 664:case 353:case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:case 16:break;case 357:case 358:case 355:case 667:i[0].length?i.push([c,a]):i[0]=[c,a],i[i.length-1].XTI=[];break;case 362:0===i.length&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(a),i.XTI=i.XTI.concat(a);break;case 35:case 37:f.push(c),g=!0;break;case 36:case 38:f.pop(),g=!1;break;default:if(b.T);else if(!g||d.WTF&&37!=f[f.length-1]&&35!=f[f.length-1])throw Error("Unexpected record 0x"+c.toString(16))}},d),fn(e),e.Names=h,e.supbooks=i,e}return function(a,b){if(!a)throw Error("Could not find file");var c={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},d=!1,e="xmlns",f={},g=0;if(a.replace(bl,function(h,i){var j=bo(h);switch(bp(j[0])){case"<?xml":case"</workbook>":case"<fileVersion/>":case"</fileVersion>":case"<fileSharing":case"<fileSharing/>":case"</workbookPr>":case"<workbookProtection":case"<workbookProtection/>":case"<bookViews":case"<bookViews>":case"</bookViews>":case"</workbookView>":case"<sheets":case"<sheets>":case"</sheets>":case"</sheet>":case"<functionGroups":case"<functionGroups/>":case"<functionGroup":case"<externalReferences":case"</externalReferences>":case"<externalReferences>":case"<externalReference":case"<definedNames/>":case"<definedName/>":case"</calcPr>":case"<oleSize":case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":case"<customWorkbookView":case"</customWorkbookView>":case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":case"<pivotCache":case"<smartTagPr":case"<smartTagPr/>":case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":case"<smartTagType":case"<webPublishing":case"<webPublishing/>":case"<fileRecoveryPr":case"<fileRecoveryPr/>":case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":case"<webPublishObject":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":case"<ArchID":case"<revisionPtr":break;case"<workbook":h.match(fp)&&(e="xmlns"+h.match(/<(\w+):/)[1]),c.xmlns=j[e];break;case"<fileVersion":delete j[0],c.AppVersion=j;break;case"<workbookPr":case"<workbookPr/>":fh.forEach(function(a){if(null!=j[a[0]])switch(a[2]){case"bool":c.WBProps[a[0]]=bx(j[a[0]]);break;case"int":c.WBProps[a[0]]=parseInt(j[a[0]],10);break;default:c.WBProps[a[0]]=j[a[0]]}}),j.codeName&&(c.WBProps.CodeName=bC(j.codeName));break;case"<workbookView":case"<workbookView/>":delete j[0],c.WBView.push(j);break;case"<sheet":switch(j.state){case"hidden":j.Hidden=1;break;case"veryHidden":j.Hidden=2;break;default:j.Hidden=0}delete j.state,j.name=bs(bC(j.name)),delete j[0],c.Sheets.push(j);break;case"<definedNames>":case"<definedNames":case"<ext":case"<AlternateContent":case"<AlternateContent>":d=!0;break;case"</definedNames>":case"</ext>":case"</AlternateContent>":d=!1;break;case"<definedName":(f={}).Name=bC(j.name),j.comment&&(f.Comment=j.comment),j.localSheetId&&(f.Sheet=+j.localSheetId),bx(j.hidden||"0")&&(f.Hidden=!0),g=i+h.length;break;case"</definedName>":f.Ref=bs(bC(a.slice(g,i))),c.Names.push(f);break;case"<calcPr":case"<calcPr/>":delete j[0],c.CalcPr=j;break;default:if(!d&&b.WTF)throw Error("unrecognized "+j[0]+" in workbook")}return h}),-1===bN.indexOf(c.xmlns))throw Error("Unknown Namespace: "+c.xmlns);return fn(c),c}(a,c)}(bd(a,fT(v.workbooks[0])),v.workbooks[0],b),A={},B="";v.coreprops.length&&((B=bd(a,fT(v.coreprops[0]),!0))&&(A=c_(B)),0!==v.extprops.length)&&(B=bd(a,fT(v.extprops[0]),!0))&&(h=B,i=A,j=b,k={},i||(i={}),h=bC(h),c0.forEach(function(a){var b=(h.match(bE(a[0]))||[])[1];switch(a[2]){case"string":b&&(i[a[1]]=bs(b));break;case"bool":i[a[1]]="true"===b;break;case"raw":var c=h.match(RegExp("<"+a[0]+"[^>]*>([\\s\\S]*?)</"+a[0]+">"));c&&c.length>0&&(k[a[1]]=c[1])}}),k.HeadingPairs&&k.TitlesOfParts&&c1(k.HeadingPairs,k.TitlesOfParts,i,j));var C={};(!b.bookSheets||b.bookProps)&&0!==v.custprops.length&&(B=be(a,fT(v.custprops[0]),!0))&&(C=function(a,b){var c={},d="",e=a.match(c2);if(e)for(var f=0;f!=e.length;++f){var g=e[f],h=bo(g);switch(h[0]){case"<?xml":case"<Properties":break;case"<property":d=bs(h.name);break;case"</property>":d=null;break;default:if(0===g.indexOf("<vt:")){var i=g.split(">"),j=i[0].slice(4),k=i[1];switch(j){case"lpstr":case"bstr":case"lpwstr":case"cy":case"error":c[d]=bs(k);break;case"bool":c[d]=bx(k);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":c[d]=parseInt(k,10);break;case"r4":case"r8":case"decimal":c[d]=parseFloat(k);break;case"filetime":case"date":c[d]=a1(k);break;default:if("/"==j.slice(-1))break;b.WTF&&"undefined"!=typeof console&&console.warn("Unexpected",g,j,i)}}else if("</"===g.slice(0,2));else if(b.WTF)throw Error(g)}}return c}(B,b));var D={};if((b.bookSheets||b.bookProps)&&(z.Sheets?p=z.Sheets.map(function(a){return a.name}):A.Worksheets&&A.SheetNames.length>0&&(p=A.SheetNames),b.bookProps&&(D.Props=A,D.Custprops=C),b.bookSheets&&void 0!==p&&(D.SheetNames=p),b.bookSheets?D.SheetNames:b.bookProps))return D;p={};var E={};b.bookDeps&&v.calcchain&&(E=function(a,b,c){if(".bin"===b.slice(-4)){var d;return d=[],cl(a,function(a,b,c){if(63===c)d.push(a);else if(b.T);else if(1)throw Error("Unexpected record 0x"+c.toString(16))}),d}var e=[];if(!a)return e;var f=1;return(a.match(bl)||[]).forEach(function(a){var b=bo(a);switch(b[0]){case"<?xml":case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete b[0],b.i?f=b.i:b.i=f,e.push(b)}}),e}(bd(a,fT(v.calcchain)),v.calcchain,b));var F=0,G={},H=z.Sheets;A.Worksheets=H.length,A.SheetNames=[];for(var I=0;I!=H.length;++I)A.SheetNames[I]=H[I].name;var J=w?"bin":"xml",K=v.workbooks[0].lastIndexOf("/"),L=(v.workbooks[0].slice(0,K+1)+"_rels/"+v.workbooks[0].slice(K+1)+".rels").replace(/^\//,"");bb(a,L)||(L="xl/_rels/workbook."+J+".rels");var M=cY(be(a,L,!0),L.replace(/_rels.*/,"s5s"));(v.metadata||[]).length>=1&&(b.xlmeta=function(a,b,c){if(".bin"===b.slice(-4)){var d,e,f,g,h;return d={Types:[],Cell:[],Value:[]},e=c||{},f=[],g=!1,h=2,cl(a,function(a,b,c){switch(c){case 335:d.Types.push({name:a.name});break;case 51:a.forEach(function(a){1==h?d.Cell.push({type:d.Types[a[0]-1].name,index:a[1]}):0==h&&d.Value.push({type:d.Types[a[0]-1].name,index:a[1]})});break;case 337:h=+!!a;break;case 338:h=2;break;case 35:f.push(c),g=!0;break;case 36:f.pop(),g=!1;break;default:if(b.T);else if(!g||e.WTF&&35!=f[f.length-1])throw Error("Unexpected record 0x"+c.toString(16))}}),d}var i,j={Types:[],Cell:[],Value:[]};if(!a)return j;var k=!1,l=2;return a.replace(bl,function(a){var b=bo(a);switch(bp(b[0])){case"<?xml":case"<metadata":case"</metadata>":case"<metadataTypes":case"</metadataTypes>":case"</metadataType>":case"</futureMetadata>":case"<bk>":case"</bk>":case"</rc>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<metadataType":j.Types.push({name:b.name});break;case"<futureMetadata":for(var d=0;d<j.Types.length;++d)j.Types[d].name==b.name&&(i=j.Types[d]);break;case"<rc":1==l?j.Cell.push({type:j.Types[b.t-1].name,index:+b.v}):0==l&&j.Value.push({type:j.Types[b.t-1].name,index:+b.v});break;case"<cellMetadata":l=1;break;case"</cellMetadata>":case"</valueMetadata>":l=2;break;case"<valueMetadata":l=0;break;case"<ext":k=!0;break;case"</ext>":k=!1;break;case"<rvb":if(!i)break;i.offsets||(i.offsets=[]),i.offsets.push(+b.i);break;default:if(!k&&c.WTF)throw Error("unrecognized "+b[0]+" in metadata")}return a}),j}(bd(a,fT(v.metadata[0])),v.metadata[0],b)),(v.people||[]).length>=1&&(b.people=(l=bd(a,fT(v.people[0])),m=b,n=[],o=!1,l.replace(bl,function(a){var b=bo(a);switch(bp(b[0])){case"<?xml":case"<personList":case"</personList>":case"</person>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<person":n.push({name:b.displayname,id:b.id});break;case"<ext":o=!0;break;case"</ext>":o=!1;break;default:if(!o&&m.WTF)throw Error("unrecognized "+b[0]+" in threaded comments")}return a}),n)),M&&(M=function(a,b){if(!a)return 0;try{a=b.map(function(b){var c;return b.id||(b.id=b.strRelID),[b.name,a["!id"][b.id].Target,(c=a["!id"][b.id].Type,cW.WS.indexOf(c)>-1?"sheet":cW.CS&&c==cW.CS?"chart":cW.DS&&c==cW.DS?"dialog":cW.MS&&c==cW.MS?"macro":c&&c.length?c:"sheet")]})}catch(a){return null}return a&&0!==a.length?a:null}(M,z.Sheets));var N=+!!bd(a,"xl/worksheets/sheet.xml",!0);for(F=0;F!=A.Worksheets;++F){var O="sheet";if(M&&M[F]?(bb(a,r="xl/"+M[F][1].replace(/[\/]?xl\//,""))||(r=M[F][1]),bb(a,r)||(r=L.replace(/_rels\/.*$/,"")+M[F][1]),O=M[F][2]):r=(r="xl/worksheets/sheet"+(F+1-N)+"."+J).replace(/sheet0\./,"sheet."),s=r.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),b&&null!=b.sheets)switch(typeof b.sheets){case"number":if(F!=b.sheets)continue;break;case"string":if(A.SheetNames[F].toLowerCase()!=b.sheets.toLowerCase())continue;break;default:if(Array.isArray&&Array.isArray(b.sheets)){for(var P=!1,Q=0;Q!=b.sheets.length;++Q)"number"==typeof b.sheets[Q]&&b.sheets[Q]==F&&(P=1),"string"==typeof b.sheets[Q]&&b.sheets[Q].toLowerCase()==A.SheetNames[F].toLowerCase()&&(P=1);if(!P)continue}}!function(a,b,c,d,e,f,g,h,i,j,k,l){try{f[d]=cY(be(a,c,!0),b);var m,n,o=bd(a,b);switch(h){case"sheet":m=f[d],n=".bin"===b.slice(-4)?function(a,b,c,d,e,f,g){if(!a)return a;var h,i,j,k,l,m,n,o,p,q,r,s,t=b||{};d||(d={"!id":{}});var u=t.dense?[]:{},v={s:{r:2e6,c:2e6},e:{r:0,c:0}},w=[],x=!1,y=!1,z=[];t.biff=12,t["!row"]=0;var A=0,B=!1,C=[],D={},E=t.supbooks||e.supbooks||[[]];if(E.sharedf=D,E.arrayf=C,E.SheetNames=e.SheetNames||e.Sheets.map(function(a){return a.name}),!t.supbooks&&(t.supbooks=E,e.Names))for(var F=0;F<e.Names.length;++F)E[0][F+1]=e.Names[F];var G=[],H=[],I=!1;if(fB[16]={n:"BrtShortReal",f:ff},cl(a,function(a,b,F){if(!y)switch(F){case 148:h=a;break;case 0:i=a,t.sheetRows&&t.sheetRows<=i.r&&(y=!0),p=cs(l=i.r),t["!row"]=i.r,(a.hidden||a.hpt||null!=a.level)&&(a.hpt&&(a.hpx=ee(a.hpt)),H[a.r]=a);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(j={t:a[2]},a[2]){case"n":j.v=a[1];break;case"s":o=e$[a[1]],j.v=o.t,j.r=o.r;break;case"b":j.v=!!a[1];break;case"e":j.v=a[1],!1!==t.cellText&&(j.w=cT[j.v]);break;case"str":j.t="s",j.v=a[1];break;case"is":j.t="s",j.v=a[1].t}if((k=g.CellXf[a[0].iStyleRef])&&e1(j,k.numFmtId,null,t,f,g),m=-1==a[0].c?m+1:a[0].c,t.dense?(u[l]||(u[l]=[]),u[l][m]=j):u[cu(m)+p]=j,t.cellFormula){for(A=0,B=!1;A<C.length;++A){var J=C[A];i.r>=J[0].s.r&&i.r<=J[0].e.r&&m>=J[0].s.c&&m<=J[0].e.c&&(j.F=cy(J[0]),B=!0)}!B&&a.length>3&&(j.f=a[3])}if(v.s.r>i.r&&(v.s.r=i.r),v.s.c>m&&(v.s.c=m),v.e.r<i.r&&(v.e.r=i.r),v.e.c<m&&(v.e.c=m),t.cellDates&&k&&"n"==j.t&&aI(al[k.numFmtId])){var K=ap(j.v);K&&(j.t="d",j.v=new Date(K.y,K.m-1,K.d,K.H,K.M,K.S,K.u))}r&&("XLDAPR"==r.type&&(j.D=!0),r=void 0),s&&(s=void 0);break;case 1:case 12:if(!t.sheetStubs||x)break;j={t:"z",v:void 0},m=-1==a[0].c?m+1:a[0].c,t.dense?(u[l]||(u[l]=[]),u[l][m]=j):u[cu(m)+p]=j,v.s.r>i.r&&(v.s.r=i.r),v.s.c>m&&(v.s.c=m),v.e.r<i.r&&(v.e.r=i.r),v.e.c<m&&(v.e.c=m),r&&("XLDAPR"==r.type&&(j.D=!0),r=void 0),s&&(s=void 0);break;case 176:z.push(a);break;case 49:r=((t.xlmeta||{}).Cell||[])[a-1];break;case 494:var L=d["!id"][a.relId];for(L?(a.Target=L.Target,a.loc&&(a.Target+="#"+a.loc),a.Rel=L):""==a.relId&&(a.Target="#"+a.loc),l=a.rfx.s.r;l<=a.rfx.e.r;++l)for(m=a.rfx.s.c;m<=a.rfx.e.c;++m)t.dense?(u[l]||(u[l]=[]),u[l][m]||(u[l][m]={t:"z",v:void 0}),u[l][m].l=a):(u[n=cw({c:m,r:l})]||(u[n]={t:"z",v:void 0}),u[n].l=a);break;case 426:if(!t.cellFormula)break;C.push(a),(q=t.dense?u[l][m]:u[cu(m)+p]).f=eS(a[1],v,{r:i.r,c:m},E,t),q.F=cy(a[0]);break;case 427:if(!t.cellFormula)break;D[cw(a[0].s)]=a[1],(q=t.dense?u[l][m]:u[cu(m)+p]).f=eS(a[1],v,{r:i.r,c:m},E,t);break;case 60:if(!t.cellStyles)break;for(;a.e>=a.s;)G[a.e--]={width:a.w/256,hidden:!!(1&a.flags),level:a.level},I||(I=!0,ec(a.w/256)),ed(G[a.e+1]);break;case 161:u["!autofilter"]={ref:cy(a)};break;case 476:u["!margins"]=a;break;case 147:e.Sheets[c]||(e.Sheets[c]={}),a.name&&(e.Sheets[c].CodeName=a.name),(a.above||a.left)&&(u["!outline"]={above:a.above,left:a.left});break;case 137:e.Views||(e.Views=[{}]),e.Views[0]||(e.Views[0]={}),a.RTL&&(e.Views[0].RTL=!0);break;case 485:case 64:case 1053:case 151:case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:x=!0;break;case 36:x=!1;break;case 37:w.push(F),x=!0;break;case 38:w.pop(),x=!1;break;default:if(b.T);else if(!x||t.WTF)throw Error("Unexpected record 0x"+F.toString(16))}},t),delete t.supbooks,delete t["!row"],!u["!ref"]&&(v.s.r<2e6||h&&(h.e.r>0||h.e.c>0||h.s.r>0||h.s.c>0))&&(u["!ref"]=cy(h||v)),t.sheetRows&&u["!ref"]){var J=cz(u["!ref"]);t.sheetRows<=+J.e.r&&(J.e.r=t.sheetRows-1,J.e.r>v.e.r&&(J.e.r=v.e.r),J.e.r<J.s.r&&(J.s.r=J.e.r),J.e.c>v.e.c&&(J.e.c=v.e.c),J.e.c<J.s.c&&(J.s.c=J.e.c),u["!fullref"]=u["!ref"],u["!ref"]=cy(J))}return z.length>0&&(u["!merges"]=z),G.length>0&&(u["!cols"]=G),H.length>0&&(u["!rows"]=H),u}(o,i,e,m,j,k,l):function(a,b,c,d,e,f,g){if(!a)return a;d||(d={"!id":{}});var h=b.dense?[]:{},i={s:{r:2e6,c:2e6},e:{r:0,c:0}},j="",k="",l=a.match(e3);l?(j=a.slice(0,l.index),k=a.slice(l.index+l[0].length)):j=k=a;var m=j.match(e9);m?fc(m[0],h,e,c):(m=j.match(fa))&&(o=m[0],p=m[1],q=h,r=e,s=c,fc(o.slice(0,o.indexOf(">")),q,r,s));var n=(j.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(n>0){var o,p,q,r,s,t,u=j.slice(n,n+50).match(e5);u&&(t=cz(u[1])).s.r<=t.e.r&&t.s.c<=t.e.c&&t.s.r>=0&&t.s.c>=0&&(h["!ref"]=cy(t))}var v=j.match(fb);v&&v[1]&&function(a,b){b.Views||(b.Views=[{}]),(a.match(fd)||[]).forEach(function(a,c){var d=bo(a);b.Views[c]||(b.Views[c]={}),+d.zoomScale&&(b.Views[c].zoom=+d.zoomScale),bx(d.rightToLeft)&&(b.Views[c].RTL=!0)})}(v[1],e);var w=[];if(b.cellStyles){var x=j.match(e6);x&&function(a,b){for(var c=!1,d=0;d!=b.length;++d){var e=bo(b[d],!0);e.hidden&&(e.hidden=bx(e.hidden));var f=parseInt(e.min,10)-1,g=parseInt(e.max,10)-1;for(e.outlineLevel&&(e.level=+e.outlineLevel||0),delete e.min,delete e.max,e.width=+e.width,!c&&e.width&&(c=!0,ec(e.width)),ed(e);f<=g;)a[f++]=a3(e)}}(w,x)}l&&fe(l[1],h,b,i,f,g);var y=k.match(e7);y&&(h["!autofilter"]={ref:(y[0].match(/ref="([^"]*)"/)||[])[1]});var z=[],A=k.match(e2);if(A)for(n=0;n!=A.length;++n)z[n]=cz(A[n].slice(A[n].indexOf('"')+1));var B=k.match(e4);B&&function(a,b,c){for(var d=Array.isArray(a),e=0;e!=b.length;++e){var f=bo(bC(b[e]),!0);if(!f.ref)return;var g=((c||{})["!id"]||[])[f.id];g?(f.Target=g.Target,f.location&&(f.Target+="#"+bs(f.location))):(f.Target="#"+bs(f.location),g={Target:f.Target,TargetMode:"Internal"}),f.Rel=g,f.tooltip&&(f.Tooltip=f.tooltip,delete f.tooltip);for(var h=cz(f.ref),i=h.s.r;i<=h.e.r;++i)for(var j=h.s.c;j<=h.e.c;++j){var k=cw({c:j,r:i});d?(a[i]||(a[i]=[]),a[i][j]||(a[i][j]={t:"z",v:void 0}),a[i][j].l=f):(a[k]||(a[k]={t:"z",v:void 0}),a[k].l=f)}}}(h,B,d);var C=k.match(e8);if(C&&(h["!margins"]=function(a){var b={};return["left","right","top","bottom","header","footer"].forEach(function(c){a[c]&&(b[c]=parseFloat(a[c]))}),b}(bo(C[0]))),!h["!ref"]&&i.e.c>=i.s.c&&i.e.r>=i.s.r&&(h["!ref"]=cy(i)),b.sheetRows>0&&h["!ref"]){var D=cz(h["!ref"]);b.sheetRows<=+D.e.r&&(D.e.r=b.sheetRows-1,D.e.r>i.e.r&&(D.e.r=i.e.r),D.e.r<D.s.r&&(D.s.r=D.e.r),D.e.c>i.e.c&&(D.e.c=i.e.c),D.e.c<D.s.c&&(D.s.c=D.e.c),h["!fullref"]=h["!ref"],h["!ref"]=cy(D))}return w.length>0&&(h["!cols"]=w),z.length>0&&(h["!merges"]=z),h}(o,i,e,m,j,k,l);break;case"chart":if(!(n=function(a,b,c,d,e,f,g,h){if(".bin"===b.slice(-4)){var i=e;if(!a)return a;i||(i={"!id":{}});var j={"!type":"chart","!drawel":null,"!rel":""},k=[],l=!1;return cl(a,function(a,b,e){switch(e){case 550:j["!rel"]=a;break;case 651:f.Sheets[c]||(f.Sheets[c]={}),a.name&&(f.Sheets[c].CodeName=a.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:l=!0;break;case 36:l=!1;break;case 37:k.push(e);break;case 38:k.pop();break;default:if(b.T>0)k.push(e);else if(b.T<0)k.pop();else if(!l||d.WTF)throw Error("Unexpected record 0x"+e.toString(16))}},d),i["!id"][j["!rel"]]&&(j["!drawel"]=i["!id"][j["!rel"]]),j}var m=e;if(!a)return a;m||(m={"!id":{}});var n,o={"!type":"chart","!drawel":null,"!rel":""},p=a.match(e9);return p&&fc(p[0],o,f,c),(n=a.match(/drawing r:id="(.*?)"/))&&(o["!rel"]=n[1]),m["!id"][o["!rel"]]&&(o["!drawel"]=m["!id"][o["!rel"]]),o}(o,b,e,i,f[d],j,0,0))||!n["!drawel"])break;var p=bh(n["!drawel"].Target,b),q=cX(p),r=function(a,b){if(!a)return"??";var c=(a.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return b["!id"][c].Target}(be(a,p,!0),cY(be(a,q,!0),p)),s=bh(r,p),t=cX(s);n=function(a,b,c,d,e,f){var g=f||{"!type":"chart"};if(!a)return f;var h=0,i=0,j="A",k={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(a.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(a){var b,c,d,e,f=(c=[],d=a.match(/^<c:numCache>/),(a.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(a){var b=a.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);b&&(c[+b[1]]=d?+b[2]:b[2])}),e=bs((a.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]),(a.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(a){b=a.replace(/<.*?>/g,"")}),[c,e,b]);k.s.r=k.s.c=0,k.e.c=h,j=cu(h),f[0].forEach(function(a,b){g[j+cs(b)]={t:"n",v:a,z:f[1]},i=b}),k.e.r<i&&(k.e.r=i),++h}),h>0&&(g["!ref"]=cy(k)),g}(be(a,s,!0),0,0,cY(be(a,t,!0),s),0,n);break;case"macro":f[d],b.slice(-4),n={"!type":"macro"};break;case"dialog":f[d],b.slice(-4),n={"!type":"dialog"};break;default:throw Error("Unrecognized sheet type "+h)}g[d]=n;var u=[];f&&f[d]&&aS(f[d]).forEach(function(c){var e,g,h,j,k,l="";if(f[d][c].Type==cW.CMNT){l=bh(f[d][c].Target,b);var m=function(a,b,c){if(".bin"===b.slice(-4)){var d,e,f,g;return d=[],e=[],f={},g=!1,cl(a,function(a,b,h){switch(h){case 632:e.push(a);break;case 635:f=a;break;case 637:f.t=a.t,f.h=a.h,f.r=a.r;break;case 636:if(f.author=e[f.iauthor],delete f.iauthor,c.sheetRows&&f.rfx&&c.sheetRows<=f.rfx.r)break;f.t||(f.t=""),delete f.rfx,d.push(f);break;case 3072:case 37:case 38:break;case 35:g=!0;break;case 36:g=!1;break;default:if(b.T);else if(!g||c.WTF)throw Error("Unexpected record 0x"+h.toString(16))}}),d}if(a.match(/<(?:\w+:)?comments *\/>/))return[];var h=[],i=[],j=a.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);j&&j[1]&&j[1].split(/<\/\w*:?author>/).forEach(function(a){if(""!==a&&""!==a.trim()){var b=a.match(/<(?:\w+:)?author[^>]*>(.*)/);b&&h.push(b[1])}});var k=a.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return k&&k[1]&&k[1].split(/<\/\w*:?comment>/).forEach(function(a){if(""!==a&&""!==a.trim()){var b=a.match(/<(?:\w+:)?comment[^>]*>/);if(b){var d=bo(b[0]),e={author:d.authorId&&h[d.authorId]||"sheetjsghost",ref:d.ref,guid:d.guid},f=cv(d.ref);if(!c.sheetRows||!(c.sheetRows<=f.r)){var g=a.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),j=!!g&&!!g[1]&&dV(g[1])||{r:"",t:"",h:""};e.r=j.r,"<t></t>"==j.r&&(j.t=j.h=""),e.t=(j.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),c.cellHTML&&(e.h=j.h),i.push(e)}}}}),i}(bd(a,l,!0),l,i);if(!m||!m.length)return;es(n,m,!1)}f[d][c].Type==cW.TCMNT&&(l=bh(f[d][c].Target,b),u=u.concat((e=bd(a,l,!0),g=[],h=!1,j={},k=0,e.replace(bl,function(a,b){var c=bo(a);switch(bp(c[0])){case"<?xml":case"<ThreadedComments":case"</ThreadedComments>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<threadedComment":j={author:c.personId,guid:c.id,ref:c.ref,T:1};break;case"</threadedComment>":null!=j.t&&g.push(j);break;case"<text>":case"<text":k=b+a.length;break;case"</text>":j.t=e.slice(k,b).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":case"<mentions>":case"<ext":h=!0;break;case"</mentions>":case"</ext>":h=!1;break;default:if(!h&&i.WTF)throw Error("unrecognized "+c[0]+" in threaded comments")}return a}),g)))}),u&&u.length&&es(n,u,!0,i.people||[])}catch(a){if(i.WTF)throw a}}(a,r,s,A.SheetNames[F],F,G,p,O,b,z,x,y)}return D={Directory:v,Workbook:z,Props:A,Custprops:C,Deps:E,Sheets:p,SheetNames:A.SheetNames,Strings:e$,Styles:y,Themes:x,SSF:a3(al)},b&&b.bookFiles&&(a.files?(D.keys=u,D.files=a.files):(D.keys=[],D.files={},a.FullPaths.forEach(function(b,c){b=b.replace(/^Root Entry[\/]/,""),D.keys.push(b),D.files[b]=a.FileIndex[c]}))),b&&b.bookVBA&&(v.vba.length>0?D.vbaraw=bd(a,fT(v.vba[0]),!0):v.defaults&&"application/vnd.ms-office.vbaProject"===v.defaults.bin&&(D.vbaraw=bd(a,"xl/vbaProject.bin",!0))),D}(bg(f,g),g)):fW(b,n,m,p);case 239:return 60===o[3]?fv(n,m):fW(b,n,m,p);case 255:if(254===o[1])return j=n,k=m,l=j,"base64"==k.type&&(l=T(l)),l=e.utils.decode(1200,l.slice(2),"str"),k.type="binary",fV(l,k);if(0===o[1]&&2===o[2]&&0===o[3])return dP.to_workbook(n,m);break;case 0:if(0===o[1]&&(o[2]>=2&&0===o[3]||0===o[2]&&(8===o[3]||9===o[3])))return dP.to_workbook(n,m);break;case 3:case 131:case 139:case 140:return dK.to_workbook(n,m);case 123:if(92===o[1]&&114===o[2]&&116===o[3])return d4.to_workbook(n,m);break;case 10:case 13:case 32:var w=n,x=m,y="",z=fU(w,x);switch(x.type){case"base64":y=T(w);break;case"binary":y=w;break;case"buffer":y=w.toString("binary");break;case"array":y=a2(w);break;default:throw Error("Unrecognized type "+x.type)}return 239==z[0]&&187==z[1]&&191==z[2]&&(y=bC(y)),x.type="binary",fV(y,x);case 137:if(80===o[1]&&78===o[2]&&71===o[3])throw Error("PNG Image File is not a spreadsheet")}return dJ.indexOf(o[0])>-1&&o[2]<=12&&o[3]<=31?dK.to_workbook(n,m):fW(b,n,m,p)}(i,{type:"array"}),b=a.SheetNames[0],c=a.Sheets[b],f=f$.sheet_to_json(c,{header:1});if(f.length<2)return y.NextResponse.json({error:"File must contain at least a header row and one data row"},{status:400});let g=f[0].map(a=>a.toLowerCase().trim());j=f.slice(1).map(a=>{let b={};return g.forEach((c,d)=>{b[c]=a[d]||""}),b})}if(0===j.length)return y.NextResponse.json({error:"No data found in file"},{status:400});let k={success:!0,imported:0,errors:[],duplicates:[]},l=await z.z.lead.findMany({where:{userId:b.userId},select:{email:!0}}),m=new Set(l.map(a=>a.email.toLowerCase())),n=[];for(let a=0;a<j.length;a++){let c=j[a],d=a+2;try{let a={name:c.name||c.fullname||c["full name"]||c["lead name"]||"",email:c.email||c["email address"]||c.mail||"",phone:c.phone||c.telephone||c["phone number"]||c.mobile||"",source:c.source||c.origin||c.channel||"",status:"NEW"},e=B.aq.parse(a),f=e.email.toLowerCase();if(m.has(f)){if(k.duplicates.push({row:d,email:e.email}),!g){k.errors.push({row:d,data:c,error:`Email ${e.email} already exists`});continue}}else m.add(f),n.push({...e,userId:b.userId})}catch(a){k.errors.push({row:d,data:c,error:a instanceof Error?a.message:"Invalid data format"})}}if(n.length>0)try{await z.z.lead.createMany({data:n,skipDuplicates:!0}),k.imported=n.length}catch(a){return console.error("Bulk import error:",a),y.NextResponse.json({error:"Failed to import leads to database"},{status:500})}return k.success=0===k.errors.length||k.imported>0,y.NextResponse.json(k)}catch(a){return console.error("Bulk import error:",a),y.NextResponse.json({error:"Internal server error"},{status:500})}}E.version;let f0=new i.AppRouteRouteModule({definition:{kind:j.RouteKind.APP_ROUTE,page:"/api/leads/bulk-import/route",pathname:"/api/leads/bulk-import",filename:"route",bundlePath:"app/api/leads/bulk-import/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\api\\leads\\bulk-import\\route.ts",nextConfigOutput:"",userland:h}),{workAsyncStorage:f1,workUnitAsyncStorage:f2,serverHooks:f3}=f0;function f4(){return(0,k.patchFetch)({workAsyncStorage:f1,workUnitAsyncStorage:f2})}async function f5(a,b,c){var d;let e="/api/leads/bulk-import/route";"/index"===e&&(e="/");let f=await f0.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!f)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:g,params:h,nextConfig:i,isDraftMode:k,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=f,D=(0,n.normalizeAppPath)(e),E=!!(y.dynamicRoutes[D]||y.routes[C]);if(E&&!k){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new w.NoFallbackError}let F=null;!E||f0.isDev||k||(F="/index"===(F=C)?"/":F);let G=!0===f0.isDev||!E,H=E&&!G,I=a.method||"GET",J=(0,m.getTracer)(),K=J.getActiveScopeSpan(),L={params:h,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!i.experimental.dynamicIO,authInterrupts:!!i.experimental.authInterrupts},supportsDynamicResponse:G,incrementalCache:(0,l.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=i.experimental)?void 0:d.cacheLife,isRevalidate:H,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>f0.onRequestError(a,b,d,z)},sharedContext:{buildId:g}},M=new o.NodeNextRequest(a),N=new o.NodeNextResponse(b),O=p.NextRequestAdapter.fromNodeNextRequest(M,(0,p.signalFromNodeResponse)(b));try{let d=async c=>f0.handle(O,L).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=J.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==q.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${I} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${I} ${a.url}`)}),f=async f=>{var g,h;let m=async({previousCacheEntry:g})=>{try{if(!(0,l.getRequestMeta)(a,"minimalMode")&&A&&B&&!g)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(f);a.fetchMetrics=L.renderOpts.fetchMetrics;let h=L.renderOpts.pendingWaitUntil;h&&c.waitUntil&&(c.waitUntil(h),h=void 0);let i=L.renderOpts.collectedTags;if(!E)return await (0,s.I)(M,N,e,L.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,t.toNodeOutgoingHttpHeaders)(e.headers);i&&(b[v.NEXT_CACHE_TAGS_HEADER]=i),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==L.renderOpts.collectedRevalidate&&!(L.renderOpts.collectedRevalidate>=v.INFINITE_CACHE)&&L.renderOpts.collectedRevalidate,d=void 0===L.renderOpts.collectedExpire||L.renderOpts.collectedExpire>=v.INFINITE_CACHE?void 0:L.renderOpts.collectedExpire;return{value:{kind:x.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==g?void 0:g.isStale)&&await f0.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,r.c)({isRevalidate:H,isOnDemandRevalidate:A})},z),b}},n=await f0.handleResponse({req:a,nextConfig:i,cacheKey:F,routeKind:j.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:m,waitUntil:c.waitUntil});if(!E)return null;if((null==n||null==(g=n.value)?void 0:g.kind)!==x.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==n||null==(h=n.value)?void 0:h.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,l.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),k&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let o=(0,t.fromNodeOutgoingHttpHeaders)(n.value.headers);return(0,l.getRequestMeta)(a,"minimalMode")&&E||o.delete(v.NEXT_CACHE_TAGS_HEADER),!n.cacheControl||b.getHeader("Cache-Control")||o.get("Cache-Control")||o.set("Cache-Control",(0,u.getCacheControlHeader)(n.cacheControl)),await (0,s.I)(M,N,new Response(n.value.body,{headers:o,status:n.value.status||200})),null};K?await f(K):await J.withPropagatedContext(a.headers,()=>J.trace(q.BaseServerSpan.handleRequest,{spanName:`${I} ${a.url}`,kind:m.SpanKind.SERVER,attributes:{"http.method":I,"http.target":a.url}},f))}catch(b){if(K||b instanceof w.NoFallbackError||await f0.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,r.c)({isRevalidate:H,isOnDemandRevalidate:A})}),E)throw b;return await (0,s.I)(M,N,new Response(null,{status:500})),null}}},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},85463:(a,b,c)=>{"use strict";c.d(b,{EA:()=>j,Sd:()=>e,UY:()=>i,aE:()=>f,aq:()=>g,pC:()=>h,zE:()=>k});var d=c(87916);let e=d.Ik({name:d.Yj().min(2,"Name must be at least 2 characters"),email:d.Yj().email("Invalid email address"),password:d.Yj().min(6,"Password must be at least 6 characters")}),f=d.Ik({email:d.Yj().email("Invalid email address"),password:d.Yj().min(1,"Password is required")}),g=d.Ik({name:d.Yj().min(1,"Name is required"),email:d.Yj().email("Invalid email address"),phone:d.Yj().optional(),source:d.Yj().optional(),status:d.k5(["NEW","CONTACTED","INTERESTED","CONVERTED","LOST"]).default("NEW")}),h=g.partial(),i=d.Ik({content:d.Yj().min(1,"Content is required"),leadId:d.Yj().min(1,"Lead ID is required")}),j=d.Ik({title:d.Yj().min(1,"Title is required"),description:d.Yj().optional(),dueDate:d.Yj().datetime("Invalid date format"),leadId:d.Yj().min(1,"Lead ID is required")}),k=j.partial().extend({completed:d.zM().optional()});d.Ik({leads:d.YO(g.omit({status:!0}))})},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94747:(a,b,c)=>{"use strict";c.d(b,{z:()=>e});let d=require("@prisma/client"),e=globalThis.prisma??new d.PrismaClient},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55,315,916],()=>b(b.s=25673));module.exports=c})();