{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n}\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    console.log('Verifying token:', {\n      tokenStart: token.substring(0, 20) + '...',\n      secretLength: JWT_SECRET.length,\n      secretStart: JWT_SECRET.substring(0, 10) + '...'\n    })\n    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload\n    console.log('Token verified successfully:', { userId: decoded.userId, email: decoded.email })\n    return decoded\n  } catch (error) {\n    console.error('Token verification failed:', error instanceof Error ? error.message : error)\n    return null\n  }\n}\n\nexport function getTokenFromRequest(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization')\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7)\n  }\n  \n  // Also check for token in cookies\n  const token = request.cookies.get('auth-token')?.value\n  return token || null\n}\n\nexport function getUserFromRequest(request: NextRequest): JWTPayload | null {\n  const token = getTokenFromRequest(request)\n  console.log('Auth check:', {\n    hasToken: !!token,\n    tokenLength: token?.length,\n    cookies: request.cookies.getAll().map(c => ({ name: c.name, hasValue: !!c.value }))\n  })\n\n  if (!token) return null\n\n  const user = verifyToken(token)\n  console.log('Token verification:', { isValid: !!user, userId: user?.userId })\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAOtC,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,QAAQ,GAAG,CAAC,oBAAoB;YAC9B,YAAY,MAAM,SAAS,CAAC,GAAG,MAAM;YACrC,cAAc,WAAW,MAAM;YAC/B,aAAa,WAAW,SAAS,CAAC,GAAG,MAAM;QAC7C;QACA,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,QAAQ,GAAG,CAAC,gCAAgC;YAAE,QAAQ,QAAQ,MAAM;YAAE,OAAO,QAAQ,KAAK;QAAC;QAC3F,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACrF,OAAO;IACT;AACF;AAEO,SAAS,oBAAoB,OAAoB;IACtD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,kCAAkC;IAClC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,SAAS,mBAAmB,OAAoB;IACrD,MAAM,QAAQ,oBAAoB;IAClC,QAAQ,GAAG,CAAC,eAAe;QACzB,UAAU,CAAC,CAAC;QACZ,aAAa,OAAO;QACpB,SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,MAAM,EAAE,IAAI;gBAAE,UAAU,CAAC,CAAC,EAAE,KAAK;YAAC,CAAC;IACnF;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,OAAO,YAAY;IACzB,QAAQ,GAAG,CAAC,uBAAuB;QAAE,SAAS,CAAC,CAAC;QAAM,QAAQ,MAAM;IAAO;IAE3E,OAAO;AACT", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/app/api/debug-auth/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { generateToken, verifyToken } from '@/lib/auth'\n\nexport async function GET(request: NextRequest) {\n  // Test token generation and verification\n  const testPayload = {\n    userId: 'test-user-id',\n    email: '<EMAIL>'\n  }\n  \n  console.log('=== DEBUG AUTH ===')\n  console.log('1. Generating token with payload:', testPayload)\n  \n  const token = generateToken(testPayload)\n  console.log('2. Generated token:', { \n    token: token.substring(0, 50) + '...', \n    length: token.length \n  })\n  \n  console.log('3. Verifying token...')\n  const verified = verifyToken(token)\n  console.log('4. Verification result:', verified)\n  \n  // Check existing cookie\n  const existingToken = request.cookies.get('auth-token')?.value\n  console.log('5. Existing cookie token:', existingToken ? {\n    token: existingToken.substring(0, 50) + '...',\n    length: existingToken.length\n  } : 'No token found')\n  \n  if (existingToken) {\n    console.log('6. Verifying existing token...')\n    const existingVerified = verifyToken(existingToken)\n    console.log('7. Existing token verification:', existingVerified)\n  }\n  \n  return NextResponse.json({\n    testToken: {\n      generated: token,\n      verified: verified\n    },\n    existingToken: existingToken ? {\n      token: existingToken,\n      verified: existingToken ? verifyToken(existingToken) : null\n    } : null,\n    environment: {\n      jwtSecretLength: process.env.JWT_SECRET?.length,\n      nodeEnv: process.env.NODE_ENV\n    }\n  })\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,yCAAyC;IACzC,MAAM,cAAc;QAClB,QAAQ;QACR,OAAO;IACT;IAEA,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,qCAAqC;IAEjD,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;QACjC,OAAO,MAAM,SAAS,CAAC,GAAG,MAAM;QAChC,QAAQ,MAAM,MAAM;IACtB;IAEA,QAAQ,GAAG,CAAC;IACZ,MAAM,WAAW,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,QAAQ,GAAG,CAAC,2BAA2B;IAEvC,wBAAwB;IACxB,MAAM,gBAAgB,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACzD,QAAQ,GAAG,CAAC,6BAA6B,gBAAgB;QACvD,OAAO,cAAc,SAAS,CAAC,GAAG,MAAM;QACxC,QAAQ,cAAc,MAAM;IAC9B,IAAI;IAEJ,IAAI,eAAe;QACjB,QAAQ,GAAG,CAAC;QACZ,MAAM,mBAAmB,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QACrC,QAAQ,GAAG,CAAC,mCAAmC;IACjD;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,WAAW;YACT,WAAW;YACX,UAAU;QACZ;QACA,eAAe,gBAAgB;YAC7B,OAAO;YACP,UAAU,gBAAgB,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,iBAAiB;QACzD,IAAI;QACJ,aAAa;YACX,iBAAiB,QAAQ,GAAG,CAAC,UAAU,EAAE;YACzC,OAAO;QACT;IACF;AACF", "debugId": null}}]}