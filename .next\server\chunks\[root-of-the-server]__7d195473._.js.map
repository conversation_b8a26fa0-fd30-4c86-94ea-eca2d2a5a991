{"version": 3, "sources": [], "sections": [{"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/auth-edge.ts"], "sourcesContent": ["import { jwtVerify } from 'jose'\nimport { NextRequest } from 'next/server'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n}\n\nexport async function verifyTokenEdge(token: string): Promise<JWTPayload | null> {\n  try {\n    const secret = new TextEncoder().encode(JWT_SECRET)\n    const { payload } = await jwtVerify(token, secret)\n    \n    return {\n      userId: payload.userId as string,\n      email: payload.email as string,\n    }\n  } catch (error) {\n    console.error('Edge token verification failed:', error instanceof Error ? error.message : error)\n    return null\n  }\n}\n\nexport function getTokenFromRequest(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization')\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7)\n  }\n  \n  // Also check for token in cookies\n  const token = request.cookies.get('auth-token')?.value\n  return token || null\n}\n\nexport async function getUserFromRequestEdge(request: NextRequest): Promise<JWTPayload | null> {\n  const token = getTokenFromRequest(request)\n  if (!token) return null\n  \n  return await verifyTokenEdge(token)\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAOtC,eAAe,gBAAgB,KAAa;IACjD,IAAI;QACF,MAAM,SAAS,IAAI,cAAc,MAAM,CAAC;QACxC,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAE3C,OAAO;YACL,QAAQ,QAAQ,MAAM;YACtB,OAAO,QAAQ,KAAK;QACtB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC1F,OAAO;IACT;AACF;AAEO,SAAS,oBAAoB,OAAoB;IACtD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,kCAAkC;IAClC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,eAAe,uBAAuB,OAAoB;IAC/D,MAAM,QAAQ,oBAAoB;IAClC,IAAI,CAAC,OAAO,OAAO;IAEnB,OAAO,MAAM,gBAAgB;AAC/B", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getUserFromRequestEdge } from '@/lib/auth-edge'\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // Skip middleware for static files and Next.js internals\n  if (\n    pathname.startsWith('/_next/') ||\n    pathname.startsWith('/api/_next/') ||\n    pathname.includes('favicon.ico') ||\n    pathname.includes('.') && !pathname.includes('/api/')\n  ) {\n    return NextResponse.next()\n  }\n\n  // Only protect API routes (except auth routes)\n  if (pathname.startsWith('/api/')) {\n    const publicApiRoutes = ['/api/auth/', '/api/test-auth', '/api/debug-auth']\n    const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route))\n\n    if (!isPublicApiRoute) {\n      const user = await getUserFromRequestEdge(request)\n      console.log('Middleware API protection:', {\n        pathname,\n        hasUser: !!user,\n        userId: user?.userId,\n        isPublicApiRoute\n      })\n\n      if (!user) {\n        console.log('Blocking API request - no valid user')\n        return NextResponse.json(\n          { error: 'Unauthorized' },\n          { status: 401 }\n        )\n      }\n    }\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|public/).*)',\n  ],\n  runtime: 'nodejs',\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,yDAAyD;IACzD,IACE,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,QAAQ,CAAC,kBAClB,SAAS,QAAQ,CAAC,QAAQ,CAAC,SAAS,QAAQ,CAAC,UAC7C;QACA,OAAO,8HAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,+CAA+C;IAC/C,IAAI,SAAS,UAAU,CAAC,UAAU;QAChC,MAAM,kBAAkB;YAAC;YAAc;YAAkB;SAAkB;QAC3E,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC;QAE3E,IAAI,CAAC,kBAAkB;YACrB,MAAM,OAAO,MAAM,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD,EAAE;YAC1C,QAAQ,GAAG,CAAC,8BAA8B;gBACxC;gBACA,SAAS,CAAC,CAAC;gBACX,QAAQ,MAAM;gBACd;YACF;YAEA,IAAI,CAAC,MAAM;gBACT,QAAQ,GAAG,CAAC;gBACZ,OAAO,8HAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAe,GACxB;oBAAE,QAAQ;gBAAI;YAElB;QACF;IACF;IAEA,OAAO,8HAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;IACD,SAAS;AACX", "debugId": null}}]}