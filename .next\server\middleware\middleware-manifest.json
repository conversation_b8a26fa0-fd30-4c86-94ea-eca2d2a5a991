{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0e912bf5._.js", "server/edge/chunks/[root-of-the-server]__912732ae._.js", "server/edge/chunks/edge-wrapper_455f47ee.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|public/).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "j/hHicgoNX9HWG5VRphNHs9wDCx/Z/kndf6fG17Knw4=", "__NEXT_PREVIEW_MODE_ID": "07fbdb01329f4a0f6b00f64371964264", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d13d473c42b08d7819c83f400a121136b625acc7192ced6c8cb40d3a0f8e6e77", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3b6d0ed918a038e9dc4cacf192d1c01cf3aa9b90b81fe0c6b4b6af5f5f358f5c"}}}, "instrumentation": null, "functions": {}}