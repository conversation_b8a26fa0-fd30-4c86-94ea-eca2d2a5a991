{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n}\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    console.log('Verifying token:', {\n      tokenStart: token.substring(0, 20) + '...',\n      secretLength: JWT_SECRET.length,\n      secretStart: JWT_SECRET.substring(0, 10) + '...'\n    })\n    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload\n    console.log('Token verified successfully:', { userId: decoded.userId, email: decoded.email })\n    return decoded\n  } catch (error) {\n    console.error('Token verification failed:', error instanceof Error ? error.message : error)\n    return null\n  }\n}\n\nexport function getTokenFromRequest(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization')\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    console.log('Found token in Authorization header')\n    return authHeader.substring(7)\n  }\n\n  // Also check for token in cookies\n  const token = request.cookies.get('auth-token')?.value\n  console.log('Token from cookie:', token ? {\n    found: true,\n    length: token.length,\n    start: token.substring(0, 20) + '...'\n  } : { found: false })\n\n  return token || null\n}\n\nexport function getUserFromRequest(request: NextRequest): JWTPayload | null {\n  const token = getTokenFromRequest(request)\n  console.log('Auth check:', {\n    hasToken: !!token,\n    tokenLength: token?.length,\n    cookies: request.cookies.getAll().map(c => ({ name: c.name, hasValue: !!c.value }))\n  })\n\n  if (!token) return null\n\n  const user = verifyToken(token)\n  console.log('Token verification:', { isValid: !!user, userId: user?.userId })\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAOtC,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,QAAQ,GAAG,CAAC,oBAAoB;YAC9B,YAAY,MAAM,SAAS,CAAC,GAAG,MAAM;YACrC,cAAc,WAAW,MAAM;YAC/B,aAAa,WAAW,SAAS,CAAC,GAAG,MAAM;QAC7C;QACA,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,QAAQ,GAAG,CAAC,gCAAgC;YAAE,QAAQ,QAAQ,MAAM;YAAE,OAAO,QAAQ,KAAK;QAAC;QAC3F,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACrF,OAAO;IACT;AACF;AAEO,SAAS,oBAAoB,OAAoB;IACtD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,QAAQ,GAAG,CAAC;QACZ,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,kCAAkC;IAClC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,QAAQ,GAAG,CAAC,sBAAsB,QAAQ;QACxC,OAAO;QACP,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,SAAS,CAAC,GAAG,MAAM;IAClC,IAAI;QAAE,OAAO;IAAM;IAEnB,OAAO,SAAS;AAClB;AAEO,SAAS,mBAAmB,OAAoB;IACrD,MAAM,QAAQ,oBAAoB;IAClC,QAAQ,GAAG,CAAC,eAAe;QACzB,UAAU,CAAC,CAAC;QACZ,aAAa,OAAO;QACpB,SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,MAAM,EAAE,IAAI;gBAAE,UAAU,CAAC,CAAC,EAAE,KAAK;YAAC,CAAC;IACnF;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,OAAO,YAAY;IACzB,QAAQ,GAAG,CAAC,uBAAuB;QAAE,SAAS,CAAC,CAAC;QAAM,QAAQ,MAAM;IAAO;IAE3E,OAAO;AACT", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/app/api/dashboard/stats/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { getUserFromRequest } from '@/lib/auth'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const user = getUserFromRequest(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Get current date for time-based queries\n    const now = new Date()\n    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)\n    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()))\n\n    // Get basic counts\n    const [\n      totalLeads,\n      newLeads,\n      contactedLeads,\n      interestedLeads,\n      convertedLeads,\n      lostLeads,\n      leadsThisMonth,\n      leadsThisWeek,\n      upcomingFollowUps,\n    ] = await Promise.all([\n      // Total leads\n      prisma.lead.count({\n        where: { userId: user.userId },\n      }),\n      // Leads by status\n      prisma.lead.count({\n        where: { userId: user.userId, status: 'NEW' },\n      }),\n      prisma.lead.count({\n        where: { userId: user.userId, status: 'CONTACTED' },\n      }),\n      prisma.lead.count({\n        where: { userId: user.userId, status: 'INTERESTED' },\n      }),\n      prisma.lead.count({\n        where: { userId: user.userId, status: 'CONVERTED' },\n      }),\n      prisma.lead.count({\n        where: { userId: user.userId, status: 'LOST' },\n      }),\n      // Time-based counts\n      prisma.lead.count({\n        where: {\n          userId: user.userId,\n          createdAt: { gte: startOfMonth },\n        },\n      }),\n      prisma.lead.count({\n        where: {\n          userId: user.userId,\n          createdAt: { gte: startOfWeek },\n        },\n      }),\n      // Upcoming follow-ups\n      prisma.followUp.count({\n        where: {\n          lead: { userId: user.userId },\n          completed: false,\n          dueDate: { gte: new Date() },\n        },\n      }),\n    ])\n\n    // Get conversion data for the last 6 months\n    const sixMonthsAgo = new Date()\n    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)\n\n    const conversionData = await prisma.lead.groupBy({\n      by: ['status', 'createdAt'],\n      where: {\n        userId: user.userId,\n        createdAt: { gte: sixMonthsAgo },\n      },\n      _count: true,\n    })\n\n    // Process conversion data by month\n    const monthlyData: { [key: string]: any } = {}\n    conversionData.forEach((item) => {\n      const month = item.createdAt.toISOString().slice(0, 7) // YYYY-MM format\n      if (!monthlyData[month]) {\n        monthlyData[month] = {\n          month,\n          new: 0,\n          contacted: 0,\n          interested: 0,\n          converted: 0,\n          lost: 0,\n        }\n      }\n      monthlyData[month][item.status.toLowerCase()] = item._count\n    })\n\n    const conversionChart = Object.values(monthlyData).sort((a: any, b: any) => \n      a.month.localeCompare(b.month)\n    )\n\n    // Calculate conversion rate\n    const conversionRate = totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0\n\n    const stats = {\n      overview: {\n        totalLeads,\n        newLeads,\n        contactedLeads,\n        interestedLeads,\n        convertedLeads,\n        lostLeads,\n        leadsThisMonth,\n        leadsThisWeek,\n        upcomingFollowUps,\n        conversionRate: Math.round(conversionRate * 100) / 100,\n      },\n      conversionChart,\n    }\n\n    return NextResponse.json(stats)\n  } catch (error) {\n    console.error('Get dashboard stats error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QAChC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,0CAA0C;QAC1C,MAAM,MAAM,IAAI;QAChB,MAAM,eAAe,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI;QACjE,MAAM,cAAc,IAAI,KAAK,IAAI,OAAO,CAAC,IAAI,OAAO,KAAK,IAAI,MAAM;QAEnE,mBAAmB;QACnB,MAAM,CACJ,YACA,UACA,gBACA,iBACA,gBACA,WACA,gBACA,eACA,kBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,cAAc;YACd,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBAAE,QAAQ,KAAK,MAAM;gBAAC;YAC/B;YACA,kBAAkB;YAClB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBAAE,QAAQ,KAAK,MAAM;oBAAE,QAAQ;gBAAM;YAC9C;YACA,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBAAE,QAAQ,KAAK,MAAM;oBAAE,QAAQ;gBAAY;YACpD;YACA,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBAAE,QAAQ,KAAK,MAAM;oBAAE,QAAQ;gBAAa;YACrD;YACA,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBAAE,QAAQ,KAAK,MAAM;oBAAE,QAAQ;gBAAY;YACpD;YACA,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBAAE,QAAQ,KAAK,MAAM;oBAAE,QAAQ;gBAAO;YAC/C;YACA,oBAAoB;YACpB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBACL,QAAQ,KAAK,MAAM;oBACnB,WAAW;wBAAE,KAAK;oBAAa;gBACjC;YACF;YACA,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBACL,QAAQ,KAAK,MAAM;oBACnB,WAAW;wBAAE,KAAK;oBAAY;gBAChC;YACF;YACA,sBAAsB;YACtB,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACpB,OAAO;oBACL,MAAM;wBAAE,QAAQ,KAAK,MAAM;oBAAC;oBAC5B,WAAW;oBACX,SAAS;wBAAE,KAAK,IAAI;oBAAO;gBAC7B;YACF;SACD;QAED,4CAA4C;QAC5C,MAAM,eAAe,IAAI;QACzB,aAAa,QAAQ,CAAC,aAAa,QAAQ,KAAK;QAEhD,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,CAAC;YAC/C,IAAI;gBAAC;gBAAU;aAAY;YAC3B,OAAO;gBACL,QAAQ,KAAK,MAAM;gBACnB,WAAW;oBAAE,KAAK;gBAAa;YACjC;YACA,QAAQ;QACV;QAEA,mCAAmC;QACnC,MAAM,cAAsC,CAAC;QAC7C,eAAe,OAAO,CAAC,CAAC;YACtB,MAAM,QAAQ,KAAK,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,GAAG,GAAG,iBAAiB;;YACxE,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBACvB,WAAW,CAAC,MAAM,GAAG;oBACnB;oBACA,KAAK;oBACL,WAAW;oBACX,YAAY;oBACZ,WAAW;oBACX,MAAM;gBACR;YACF;YACA,WAAW,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,WAAW,GAAG,GAAG,KAAK,MAAM;QAC7D;QAEA,MAAM,kBAAkB,OAAO,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC,GAAQ,IAC/D,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK;QAG/B,4BAA4B;QAC5B,MAAM,iBAAiB,aAAa,IAAI,AAAC,iBAAiB,aAAc,MAAM;QAE9E,MAAM,QAAQ;YACZ,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,OAAO;YACrD;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}