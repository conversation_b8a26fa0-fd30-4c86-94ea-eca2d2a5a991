(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[569],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(5155),s=r(2115),l=r(2085),n=r(9434);let i=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:r,variant:s,size:l,asChild:d=!1,...o}=e;return(0,a.jsx)("button",{className:(0,n.cn)(i({variant:s,size:l,className:r})),ref:t,...o})});d.displayName="Button"},343:(e,t,r)=>{"use strict";r.d(t,{EA:()=>d,Sd:()=>s,UY:()=>i,aE:()=>l,aq:()=>n});var a=r(8309);let s=a.Ik({name:a.Yj().min(2,"Name must be at least 2 characters"),email:a.Yj().email("Invalid email address"),password:a.Yj().min(6,"Password must be at least 6 characters")}),l=a.Ik({email:a.Yj().email("Invalid email address"),password:a.Yj().min(1,"Password is required")}),n=a.Ik({name:a.Yj().min(1,"Name is required"),email:a.Yj().email("Invalid email address"),phone:a.Yj().optional(),source:a.Yj().optional(),status:a.k5(["NEW","CONTACTED","INTERESTED","CONVERTED","LOST"]).default("NEW")});n.partial();let i=a.Ik({content:a.Yj().min(1,"Content is required"),leadId:a.Yj().min(1,"Lead ID is required")}),d=a.Ik({title:a.Yj().min(1,"Title is required"),description:a.Yj().optional(),dueDate:a.Yj().datetime("Invalid date format"),leadId:a.Yj().min(1,"Lead ID is required")});d.partial().extend({completed:a.zM().optional()}),a.Ik({leads:a.YO(n.omit({status:!0}))})},1651:(e,t,r)=>{Promise.resolve().then(r.bind(r,2014))},2014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var a=r(5155),s=r(2115),l=r(2177),n=r(221),i=r(343),d=r(285),o=r(2523),c=r(9409),u=r(6695),m=r(6126),f=r(4165),h=r(7759),p=r(4616),x=r(9946);let g=(0,x.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),v=(0,x.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),j=(0,x.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var b=r(3717),y=r(2525);let N={NEW:"bg-blue-500 text-white",CONTACTED:"bg-gray-500 text-white",INTERESTED:"bg-yellow-500 text-white",CONVERTED:"bg-green-500 text-white",LOST:"bg-red-500 text-white"};function w(){let[e,t]=(0,s.useState)([]),[r,x]=(0,s.useState)(!0),[w,C]=(0,s.useState)(!1),[E,k]=(0,s.useState)(!1),[R,S]=(0,s.useState)(null),[T,A]=(0,s.useState)(""),[L,D]=(0,s.useState)(""),[I,O]=(0,s.useState)(""),Y=(0,l.mN)({resolver:(0,n.u)(i.aq),defaultValues:{name:"",email:"",phone:"",source:"",status:"NEW"}});(0,s.useEffect)(()=>{z()},[L,I]);let z=async()=>{try{let e=new URLSearchParams;L&&e.append("status",L),I&&e.append("source",I);let r=await fetch("/api/leads?".concat(e));if(r.ok){let e=await r.json();t(e.leads)}}catch(e){console.error("Error fetching leads:",e)}finally{x(!1)}},F=async e=>{try{let t=R?"/api/leads/".concat(R.id):"/api/leads",r=R?"PUT":"POST";(await fetch(t,{method:r,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok&&(await z(),C(!1),k(!1),S(null),Y.reset())}catch(e){console.error("Error saving lead:",e)}},W=async e=>{if(confirm("Are you sure you want to delete this lead?"))try{(await fetch("/api/leads/".concat(e),{method:"DELETE"})).ok&&await z()}catch(e){console.error("Error deleting lead:",e)}},M=e.filter(e=>e.name.toLowerCase().includes(T.toLowerCase())||e.email.toLowerCase().includes(T.toLowerCase()));return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Leads"}),(0,a.jsxs)(d.$,{onClick:()=>C(!0),children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Add Lead"]})]}),(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsx)(u.ZB,{children:"Filters"})}),(0,a.jsx)(u.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(o.p,{placeholder:"Search leads...",value:T,onChange:e=>A(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(c.l,{value:L,onChange:e=>D(e.target.value),children:[(0,a.jsx)("option",{value:"",children:"All Statuses"}),(0,a.jsx)("option",{value:"NEW",children:"New"}),(0,a.jsx)("option",{value:"CONTACTED",children:"Contacted"}),(0,a.jsx)("option",{value:"INTERESTED",children:"Interested"}),(0,a.jsx)("option",{value:"CONVERTED",children:"Converted"}),(0,a.jsx)("option",{value:"LOST",children:"Lost"})]}),(0,a.jsx)(o.p,{placeholder:"Filter by source...",value:I,onChange:e=>O(e.target.value)})]})})]}),(0,a.jsx)("div",{className:"grid gap-4",children:r?(0,a.jsx)("p",{children:"Loading leads..."}):0===M.length?(0,a.jsx)(u.Zp,{children:(0,a.jsx)(u.Wu,{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No leads found."})})}):M.map(e=>(0,a.jsx)(u.Zp,{children:(0,a.jsx)(u.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),(0,a.jsx)(m.E,{className:N[e.status],children:e.status})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(v,{className:"h-4 w-4"}),e.email]}),e.phone&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(j,{className:"h-4 w-4"}),e.phone]}),e.source&&(0,a.jsxs)("span",{children:["Source: ",e.source]})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[e._count.notes," notes • ",e._count.followUps," follow-ups"]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>{S(e),Y.reset({name:e.name,email:e.email,phone:e.phone||"",source:e.source||"",status:e.status}),k(!0)},children:(0,a.jsx)(b.A,{className:"h-4 w-4"})}),(0,a.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>W(e.id),children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})]})})},e.id))}),(0,a.jsx)(f.lG,{open:w||E,onOpenChange:e=>{e||(C(!1),k(!1),S(null),Y.reset())},children:(0,a.jsxs)(f.Cf,{children:[(0,a.jsx)(f.c7,{children:(0,a.jsx)(f.L3,{children:R?"Edit Lead":"Add New Lead"})}),(0,a.jsxs)(h.lV,{onSubmit:Y.handleSubmit(F),children:[(0,a.jsxs)(h.zB,{children:[(0,a.jsx)(h.lR,{htmlFor:"name",children:"Name"}),(0,a.jsx)(o.p,{id:"name",placeholder:"Enter lead name",...Y.register("name")}),Y.formState.errors.name&&(0,a.jsx)(h.C5,{children:Y.formState.errors.name.message})]}),(0,a.jsxs)(h.zB,{children:[(0,a.jsx)(h.lR,{htmlFor:"email",children:"Email"}),(0,a.jsx)(o.p,{id:"email",type:"email",placeholder:"Enter email address",...Y.register("email")}),Y.formState.errors.email&&(0,a.jsx)(h.C5,{children:Y.formState.errors.email.message})]}),(0,a.jsxs)(h.zB,{children:[(0,a.jsx)(h.lR,{htmlFor:"phone",children:"Phone (Optional)"}),(0,a.jsx)(o.p,{id:"phone",placeholder:"Enter phone number",...Y.register("phone")})]}),(0,a.jsxs)(h.zB,{children:[(0,a.jsx)(h.lR,{htmlFor:"source",children:"Source (Optional)"}),(0,a.jsx)(o.p,{id:"source",placeholder:"e.g., Website, Referral, Cold Call",...Y.register("source")})]}),(0,a.jsxs)(h.zB,{children:[(0,a.jsx)(h.lR,{htmlFor:"status",children:"Status"}),(0,a.jsxs)(c.l,{...Y.register("status"),children:[(0,a.jsx)("option",{value:"NEW",children:"New"}),(0,a.jsx)("option",{value:"CONTACTED",children:"Contacted"}),(0,a.jsx)("option",{value:"INTERESTED",children:"Interested"}),(0,a.jsx)("option",{value:"CONVERTED",children:"Converted"}),(0,a.jsx)("option",{value:"LOST",children:"Lost"})]})]}),(0,a.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,a.jsx)(d.$,{type:"submit",className:"flex-1",children:R?"Update Lead":"Create Lead"}),(0,a.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>{C(!1),k(!1),S(null),Y.reset()},children:"Cancel"})]})]})]})})]})}},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(5155),s=r(2115),l=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});n.displayName="Input"},2525:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},3717:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4165:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>i,L3:()=>o,c7:()=>d,lG:()=>n});var a=r(5155),s=r(2115),l=r(9434);let n=e=>{let{open:t,onOpenChange:r,children:l}=e;return(s.useEffect(()=>{let e=e=>{"Escape"===e.key&&r&&r(!1)};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,r]),t)?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50",onClick:()=>null==r?void 0:r(!1)}),(0,a.jsx)("div",{className:"relative z-50 max-h-[90vh] overflow-auto",children:l})]}):null},i=s.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("bg-background p-6 shadow-lg rounded-lg border max-w-lg w-full mx-4",r),...n,children:s})});i.displayName="DialogContent";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",r),...s})});d.displayName="DialogHeader";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h2",{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",r),...s})});o.displayName="DialogTitle",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...s})}).displayName="DialogDescription"},4616:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var a=r(5155);r(2115);var s=r(2085),l=r(9434);let n=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:r}),t),...s})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i});var a=r(5155),s=r(2115),l=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});n.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...s})});i.displayName="CardHeader";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});d.displayName="CardTitle";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...s})});o.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},7759:(e,t,r)=>{"use strict";r.d(t,{C5:()=>o,lR:()=>d,lV:()=>n,zB:()=>i});var a=r(5155),s=r(2115),l=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("form",{ref:t,className:(0,l.cn)("space-y-6",r),...s})});n.displayName="Form";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("space-y-2",r),...s})});i.displayName="FormField";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("label",{ref:t,className:(0,l.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",r),...s})});d.displayName="FormLabel";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm font-medium text-destructive",r),...s})});o.displayName="FormMessage"},9409:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});var a=r(5155),s=r(2115),l=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsx)("select",{className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n,children:s})});n.displayName="Select"},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var a=r(2596),s=r(9688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(2115);let s=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:d,className:o="",children:c,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:t,...n,width:s,height:s,stroke:r,strokeWidth:d?24*Number(i)/Number(s):i,className:l("lucide",o),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),d=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:d,...o}=r;return(0,a.createElement)(i,{ref:n,iconNode:t,className:l("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...o})});return r.displayName=s(e),r}}},e=>{e.O(0,[3,889,441,964,358],()=>e(e.s=1651)),_N_E=e.O()}]);