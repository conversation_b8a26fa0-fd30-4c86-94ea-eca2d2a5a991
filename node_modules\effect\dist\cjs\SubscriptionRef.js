"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.updateSomeEffect = exports.updateSomeAndGetEffect = exports.updateSomeAndGet = exports.updateSome = exports.updateEffect = exports.updateAndGetEffect = exports.updateAndGet = exports.update = exports.setAndGet = exports.set = exports.modifySomeEffect = exports.modifySome = exports.modifyEffect = exports.modify = exports.make = exports.getAndUpdateSomeEffect = exports.getAndUpdateSome = exports.getAndUpdateEffect = exports.getAndUpdate = exports.getAndSet = exports.get = exports.SubscriptionRefTypeId = void 0;
var internal = _interopRequireWildcard(require("./internal/subscriptionRef.js"));
var Ref = _interopRequireWildcard(require("./Ref.js"));
var Synchronized = _interopRequireWildcard(require("./SynchronizedRef.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 * @category symbols
 */
const SubscriptionRefTypeId = exports.SubscriptionRefTypeId = internal.SubscriptionRefTypeId;
/**
 * @since 2.0.0
 * @category getters
 */
const get = exports.get = internal.get;
/**
 * @since 2.0.0
 * @category utils
 */
const getAndSet = exports.getAndSet = Ref.getAndSet;
/**
 * @since 2.0.0
 * @category utils
 */
const getAndUpdate = exports.getAndUpdate = Ref.getAndUpdate;
/**
 * @since 2.0.0
 * @category utils
 */
const getAndUpdateEffect = exports.getAndUpdateEffect = Synchronized.getAndUpdateEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const getAndUpdateSome = exports.getAndUpdateSome = Ref.getAndUpdateSome;
/**
 * @since 2.0.0
 * @category utils
 */
const getAndUpdateSomeEffect = exports.getAndUpdateSomeEffect = Synchronized.getAndUpdateSomeEffect;
/**
 * Creates a new `SubscriptionRef` with the specified value.
 *
 * @since 2.0.0
 * @category constructors
 */
const make = exports.make = internal.make;
/**
 * @since 2.0.0
 * @category utils
 */
const modify = exports.modify = internal.modify;
/**
 * @since 2.0.0
 * @category utils
 */
const modifyEffect = exports.modifyEffect = internal.modifyEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const modifySome = exports.modifySome = Ref.modifySome;
/**
 * @since 2.0.0
 * @category utils
 */
const modifySomeEffect = exports.modifySomeEffect = Synchronized.modifySomeEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const set = exports.set = internal.set;
/**
 * @since 2.0.0
 * @category utils
 */
const setAndGet = exports.setAndGet = Ref.setAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
const update = exports.update = Ref.update;
/**
 * @since 2.0.0
 * @category utils
 */
const updateEffect = exports.updateEffect = Synchronized.updateEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const updateAndGet = exports.updateAndGet = Ref.updateAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
const updateAndGetEffect = exports.updateAndGetEffect = Synchronized.updateAndGetEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const updateSome = exports.updateSome = Ref.updateSome;
/**
 * @since 2.0.0
 * @category utils
 */
const updateSomeEffect = exports.updateSomeEffect = Synchronized.updateSomeEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const updateSomeAndGet = exports.updateSomeAndGet = Ref.updateSomeAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
const updateSomeAndGetEffect = exports.updateSomeAndGetEffect = Synchronized.updateSomeAndGetEffect;
//# sourceMappingURL=SubscriptionRef.js.map