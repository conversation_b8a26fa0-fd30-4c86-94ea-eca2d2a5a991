import { NextRequest, NextResponse } from 'next/server'
import { generateToken, verifyToken } from '@/lib/auth'

export async function GET(request: NextRequest) {
  // Test token generation and verification
  const testPayload = {
    userId: 'test-user-id',
    email: '<EMAIL>'
  }
  
  console.log('=== DEBUG AUTH ===')
  console.log('1. Generating token with payload:', testPayload)
  
  const token = generateToken(testPayload)
  console.log('2. Generated token:', { 
    token: token.substring(0, 50) + '...', 
    length: token.length 
  })
  
  console.log('3. Verifying token...')
  const verified = verifyToken(token)
  console.log('4. Verification result:', verified)
  
  // Check existing cookie
  const existingToken = request.cookies.get('auth-token')?.value
  console.log('5. Existing cookie token:', existingToken ? {
    token: existingToken.substring(0, 50) + '...',
    length: existingToken.length
  } : 'No token found')
  
  if (existingToken) {
    console.log('6. Verifying existing token...')
    const existingVerified = verifyToken(existingToken)
    console.log('7. Existing token verification:', existingVerified)
  }
  
  return NextResponse.json({
    testToken: {
      generated: token,
      verified: verified
    },
    existingToken: existingToken ? {
      token: existingToken,
      verified: existingToken ? verifyToken(existingToken) : null
    } : null,
    environment: {
      jwtSecretLength: process.env.JWT_SECRET?.length,
      nodeEnv: process.env.NODE_ENV
    }
  })
}
