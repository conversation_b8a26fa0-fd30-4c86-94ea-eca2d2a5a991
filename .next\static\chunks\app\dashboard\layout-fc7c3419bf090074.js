(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var a=t(5155),s=t(2115),n=t(2085),i=t(9434);let l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,r)=>{let{className:t,variant:s,size:n,asChild:d=!1,...c}=e;return(0,a.jsx)("button",{className:(0,i.cn)(l({variant:s,size:n,className:t})),ref:r,...c})});d.displayName="Button"},2277:(e,r,t)=>{Promise.resolve().then(t.bind(t,5947))},5947:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>b});var a=t(5155),s=t(2115),n=t(6874),i=t.n(n),l=t(5695),d=t(285),c=t(9434),o=t(9946);let h=(0,o.A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var x=t(7580);let u=(0,o.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var m=t(9074),f=t(9869);let g=(0,o.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),y=(0,o.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),p=(0,o.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),v=[{name:"Dashboard",href:"/dashboard",icon:h},{name:"Leads",href:"/dashboard/leads",icon:x.A},{name:"Notes",href:"/dashboard/notes",icon:u},{name:"Follow-ups",href:"/dashboard/follow-ups",icon:m.A},{name:"Import",href:"/dashboard/import",icon:f.A}];function b(e){let{children:r}=e,[t,n]=(0,s.useState)(!1),o=(0,l.usePathname)(),h=(0,l.useRouter)(),x=async()=>{try{await fetch("/api/auth/signout",{method:"POST"}),h.push("/auth/signin"),h.refresh()}catch(e){console.error("Sign out error:",e)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsxs)("div",{className:(0,c.cn)("fixed inset-0 z-50 lg:hidden",t?"block":"hidden"),children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>n(!1)}),(0,a.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white",children:[(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,a.jsx)("h1",{className:"text-xl font-bold",children:"Lead Tracker"}),(0,a.jsx)(d.$,{variant:"ghost",size:"icon",onClick:()=>n(!1),children:(0,a.jsx)(g,{className:"h-6 w-6"})})]}),(0,a.jsx)("nav",{className:"flex-1 space-y-1 px-2 py-4",children:v.map(e=>{let r=o===e.href;return(0,a.jsxs)(i(),{href:e.href,className:(0,c.cn)("group flex items-center px-2 py-2 text-sm font-medium rounded-md",r?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),onClick:()=>n(!1),children:[(0,a.jsx)(e.icon,{className:"mr-3 h-5 w-5"}),e.name]},e.name)})}),(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)(d.$,{variant:"outline",className:"w-full",onClick:x,children:[(0,a.jsx)(y,{className:"mr-2 h-4 w-4"}),"Sign Out"]})})]})]}),(0,a.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,a.jsxs)("div",{className:"flex flex-col flex-grow bg-white border-r border-gray-200",children:[(0,a.jsx)("div",{className:"flex h-16 items-center px-4",children:(0,a.jsx)("h1",{className:"text-xl font-bold",children:"Lead Tracker"})}),(0,a.jsx)("nav",{className:"flex-1 space-y-1 px-2 py-4",children:v.map(e=>{let r=o===e.href;return(0,a.jsxs)(i(),{href:e.href,className:(0,c.cn)("group flex items-center px-2 py-2 text-sm font-medium rounded-md",r?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,a.jsx)(e.icon,{className:"mr-3 h-5 w-5"}),e.name]},e.name)})}),(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)(d.$,{variant:"outline",className:"w-full",onClick:x,children:[(0,a.jsx)(y,{className:"mr-2 h-4 w-4"}),"Sign Out"]})})]})}),(0,a.jsxs)("div",{className:"lg:pl-64",children:[(0,a.jsxs)("div",{className:"flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 lg:hidden",children:[(0,a.jsx)(d.$,{variant:"ghost",size:"icon",onClick:()=>n(!0),children:(0,a.jsx)(p,{className:"h-6 w-6"})}),(0,a.jsx)("h1",{className:"text-xl font-bold",children:"Lead Tracker"})]}),(0,a.jsx)("main",{className:"py-8",children:(0,a.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:r})})]})]})}},7580:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9074:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var a=t(2596),s=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},9869:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9946:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var a=t(2115);let s=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},n=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:c="",children:o,iconNode:h,...x}=e;return(0,a.createElement)("svg",{ref:r,...i,width:s,height:s,stroke:t,strokeWidth:d?24*Number(l)/Number(s):l,className:n("lucide",c),...!o&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(x)&&{"aria-hidden":"true"},...x},[...h.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(o)?o:[o]])}),d=(e,r)=>{let t=(0,a.forwardRef)((t,i)=>{let{className:d,...c}=t;return(0,a.createElement)(l,{ref:i,iconNode:r,className:n("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...c})});return t.displayName=s(e),t}}},e=>{e.O(0,[3,244,441,964,358],()=>e(e.s=2277)),_N_E=e.O()}]);