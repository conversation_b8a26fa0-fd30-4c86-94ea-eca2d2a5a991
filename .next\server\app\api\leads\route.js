const CHUNK_PUBLIC_PATH = "server/app/api/leads/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_ca44bfba._.js");
runtime.loadChunk("server/chunks/node_modules_zod_v4_719ed6d0._.js");
runtime.loadChunk("server/chunks/node_modules_6f2b02f4._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__6db4848b._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/leads/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/leads/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/leads/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
