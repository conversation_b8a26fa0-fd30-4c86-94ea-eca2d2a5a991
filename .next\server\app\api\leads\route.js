(()=>{var a={};a.id=299,a.ids=[299],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(a,b,c)=>{"use strict";c.d(b,{BE:()=>i,Er:()=>h,HU:()=>j,rL:()=>k});var d=c(85663),e=c(43205),f=c.n(e);let g=process.env.JWT_SECRET||"fallback-secret-key";async function h(a){return d.Ay.hash(a,12)}async function i(a,b){return d.Ay.compare(a,b)}function j(a){return f().sign(a,g,{expiresIn:"7d"})}function k(a){let b=function(a){let b=a.headers.get("authorization");return b&&b.startsWith("Bearer ")?b.substring(7):a.cookies.get("auth-token")?.value||null}(a);if(!b)return null;try{return f().verify(b,g)}catch{return null}}},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:a=>{"use strict";a.exports=require("crypto")},62072:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>F,patchFetch:()=>E,routeModule:()=>A,serverHooks:()=>D,workAsyncStorage:()=>B,workUnitAsyncStorage:()=>C});var d={};c.r(d),c.d(d,{GET:()=>y,POST:()=>z});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(94747),w=c(12909),x=c(85463);async function y(a){try{let b=(0,w.rL)(a);if(!b)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:c}=new URL(a.url),d=c.get("status"),e=c.get("source"),f=c.get("sortBy")||"createdAt",g=c.get("sortOrder")||"desc",h=parseInt(c.get("page")||"1"),i=parseInt(c.get("limit")||"10"),j=(h-1)*i,k={userId:b.userId};d&&(k.status=d),e&&(k.source={contains:e,mode:"insensitive"});let[l,m]=await Promise.all([v.z.lead.findMany({where:k,orderBy:{[f]:g},skip:j,take:i,include:{notes:{orderBy:{createdAt:"desc"},take:3},followUps:{where:{completed:!1},orderBy:{dueDate:"asc"},take:1},_count:{select:{notes:!0,followUps:!0}}}}),v.z.lead.count({where:k})]);return u.NextResponse.json({leads:l,pagination:{page:h,limit:i,total:m,pages:Math.ceil(m/i)}})}catch(a){return console.error("Get leads error:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function z(a){try{let b=(0,w.rL)(a);if(!b)return u.NextResponse.json({error:"Unauthorized"},{status:401});let c=await a.json(),d=x.aq.parse(c);if(await v.z.lead.findFirst({where:{userId:b.userId,email:d.email}}))return u.NextResponse.json({error:"Lead with this email already exists"},{status:400});let e=await v.z.lead.create({data:{...d,userId:b.userId},include:{notes:!0,followUps:!0,_count:{select:{notes:!0,followUps:!0}}}});return u.NextResponse.json(e,{status:201})}catch(a){if(console.error("Create lead error:",a),a instanceof Error&&"ZodError"===a.name)return u.NextResponse.json({error:"Invalid input data",details:a},{status:400});return u.NextResponse.json({error:"Internal server error"},{status:500})}}let A=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/leads/route",pathname:"/api/leads",filename:"route",bundlePath:"app/api/leads/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\project\\leads\\src\\app\\api\\leads\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:B,workUnitAsyncStorage:C,serverHooks:D}=A;function E(){return(0,g.patchFetch)({workAsyncStorage:B,workUnitAsyncStorage:C})}async function F(a,b,c){var d;let e="/api/leads/route";"/index"===e&&(e="/");let g=await A.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||A.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===A.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>A.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>A.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await A.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},z),b}},l=await A.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await A.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},85463:(a,b,c)=>{"use strict";c.d(b,{EA:()=>j,Sd:()=>e,UY:()=>i,aE:()=>f,aq:()=>g,pC:()=>h,zE:()=>k});var d=c(87916);let e=d.Ik({name:d.Yj().min(2,"Name must be at least 2 characters"),email:d.Yj().email("Invalid email address"),password:d.Yj().min(6,"Password must be at least 6 characters")}),f=d.Ik({email:d.Yj().email("Invalid email address"),password:d.Yj().min(1,"Password is required")}),g=d.Ik({name:d.Yj().min(1,"Name is required"),email:d.Yj().email("Invalid email address"),phone:d.Yj().optional(),source:d.Yj().optional(),status:d.k5(["NEW","CONTACTED","INTERESTED","CONVERTED","LOST"]).default("NEW")}),h=g.partial(),i=d.Ik({content:d.Yj().min(1,"Content is required"),leadId:d.Yj().min(1,"Lead ID is required")}),j=d.Ik({title:d.Yj().min(1,"Title is required"),description:d.Yj().optional(),dueDate:d.Yj().datetime("Invalid date format"),leadId:d.Yj().min(1,"Lead ID is required")}),k=j.partial().extend({completed:d.zM().optional()});d.Ik({leads:d.YO(g.omit({status:!0}))})},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94747:(a,b,c)=>{"use strict";c.d(b,{z:()=>e});let d=require("@prisma/client"),e=globalThis.prisma??new d.PrismaClient},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55,315,916],()=>b(b.s=62072));module.exports=c})();