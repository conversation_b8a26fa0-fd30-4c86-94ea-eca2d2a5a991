import * as internal from "./internal/stm/tDeferred.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const TDeferredTypeId = internal.TDeferredTypeId;
const _await = internal._await;
export {
/**
 * @since 2.0.0
 * @category getters
 */
_await as await };
/**
 * @since 2.0.0
 * @category mutations
 */
export const done = internal.done;
/**
 * @since 2.0.0
 * @category mutations
 */
export const fail = internal.fail;
/**
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
/**
 * @since 2.0.0
 * @category getters
 */
export const poll = internal.poll;
/**
 * @since 2.0.0
 * @category mutations
 */
export const succeed = internal.succeed;
//# sourceMappingURL=TDeferred.js.map